# QStash Security Setup

This document explains how to set up QStash security verification for your endpoints.

## Environment Variables Required

You need to add the following environment variables to your `.env` file or deployment environment:

```bash
# QStash Configuration
QSTASH_TOKEN=your_qstash_token_here
QSTASH_CURRENT_SIGNING_KEY=your_current_signing_key_here
QSTASH_NEXT_SIGNING_KEY=your_next_signing_key_here
```

## Where to Find These Values

1. Go to [Upstash Console](https://console.upstash.com/qstash)
2. Copy the `QSTASH_TOKEN` (you already have this)
3. Find the signing keys in the QStash console:
   - `QSTASH_CURRENT_SIGNING_KEY`
   - `QSTASH_NEXT_SIGNING_KEY`

## How It Works

The middleware (`server/middleware/qstash.ts`) automatically:

1. **Intercepts requests** to any `/api/qstash/*` endpoints
2. **Verifies the signature** using the `Upstash-Signature` header
3. **Validates the request body** against the signature
4. **Allows or rejects** the request based on verification

## Security Features

- ✅ **Signature verification** - Ensures requests come from QStash
- ✅ **Automatic header detection** - Works with lowercase headers (Vercel/Netlify)
- ✅ **Proper error handling** - Returns 401 for invalid requests
- ✅ **Runtime config** - Uses Nuxt's secure runtime configuration

## Protected Routes

Currently protected routes (defined in `PROTECTED_ROUTES`):
- `/api/qstash/*` - All QStash endpoints

## Example QStash Endpoint

Your existing QStash endpoints will now be automatically protected:
- `/api/qstash/sync-contact-openphone/`
- `/api/qstash/init-calry-property-import/`
- `/api/qstash/import-calry-property-as-listing/`
- etc.

## Testing

To test if the security is working:

1. **Valid request**: Send a request from QStash - should work normally
2. **Invalid request**: Send a direct request without proper signature - should return 401

## Troubleshooting

If you see errors:
- `Missing QStash signature header` - The request doesn't have the required signature
- `Invalid QStash signature` - The signature doesn't match (request might be tampered with)
- `QStash signing keys not configured` - Environment variables are missing

Check the server logs for detailed error information.
