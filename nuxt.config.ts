// https://nuxt.com/docs/api/configuration/nuxt-config
import process from 'node:process'

export default defineNuxtConfig({
  runtimeConfig: {
    public: {
      posthogPublicKey: process.env.NUXT_PUBLIC_POSTHOG_KEY,
      posthogHost: process.env.NUXT_PUBLIC_POSTHOG_HOST,
    },
    openaiApiKey: process.env.OPENAI_API_KEY,
  },
  compatibilityDate: '2025-04-01',
  devtools: {
    enabled: true,

    timeline: {
      enabled: true,
    },
  },
  modules: [
    '@nuxt/ui-pro',
    '@nuxt/icon',
    'nuxt-vuefire',
    '@pinia/nuxt',
    '@nuxt/image',
    '@nuxtjs/mdc',
    'nuxt-charts',
    '@nuxtjs/sitemap',
    '@nuxtjs/seo',
  ],
  css: ['~/assets/css/main.css'],
  mdc: {
    highlight: {
      // noApiRoute: true
      shikiEngine: 'javascript',
    },
  },
  vuefire: {
    auth: {
      enabled: true,
      sessionCookie: true,
    },
    // TODO Implement firebase app check: https://vuefire.vuejs.org/nuxt/getting-started.html
    config: {
      apiKey: process.env.FIREBASE_API_KEY,
      authDomain: process.env.FIREBASE_AUTH_DOMAIN,
      databaseURL: process.env.FIREBASE_DATABASE_URL,
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
      appId: process.env.FIREBASE_APP_ID,
      measurementId: process.env.FIREBASE_MEASUREMENT_ID,
    },
  },
  site: {
    url: 'https://app.yada.ai',
    name: 'Yada AI'
  },
})