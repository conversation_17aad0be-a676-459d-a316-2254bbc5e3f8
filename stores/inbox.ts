import type { Unsubscribe } from 'firebase/firestore'
import {
  addDoc,
  collection,
  doc,
  getDoc,
  getDocs,
  limit,
  onSnapshot,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
} from 'firebase/firestore'
import { defineStore } from 'pinia'
import { useWorkspaceStore } from './workspace'

export const useInboxStore = defineStore('inbox', {
  state: () => ({
    chats: [] as any[],
    conversations: [] as any[],
    unsubscribers: [] as any[],
  }),

  actions: {
    annihilate() {
      this.unsubscribers.forEach(unsubscribe => unsubscribe())
      this.unsubscribers = []
      this.chats = []
      this.conversations = []
    },
    async initChats() {
      const { $db } = useNuxtApp()
      const self = this
      const workspaceStore = useWorkspaceStore()
      const userId = workspaceStore.workspace?.id
      const q = query(
        collection($db, 'chats'),
        where('userId', '==', userId),
        orderBy('lastActivityAt', 'desc'),
        limit(25),
      )
      try {
        const chatsUnsub = onSnapshot(q, (snapshot) => {
          snapshot.docChanges().forEach((change) => {
            if (change.type === 'added') {
              // Check if chat already exists
              const exists = self.chats.some(chat => chat.id === change.doc.id)
              if (!exists) {
                self.chats.push({ ...change.doc.data(), id: change.doc.id })
                self.chats.sort((a, b) => b.lastActivityAt - a.lastActivityAt)
              }
            }
            if (change.type === 'modified') {
              const modifiedChatIndex = self.chats.findIndex(chat => chat.id === change.doc.id)
              const oldChat = self.chats[modifiedChatIndex]
              if (oldChat.lastMessageId !== change.doc.data().lastMessageId) {
                const audio = new Audio(
                  'https://firebasestorage.googleapis.com/v0/b/home-service-62a33.appspot.com/o/webapp%2FyadaDing.mp3?alt=media&token=3369b558-1b47-4348-8534-3d7730cd720d',
                )
                audio.volume = 0.0
                try {
                  audio.play()
                }
                catch (e) {
                  console.warn(e)
                }
              }
              if (modifiedChatIndex !== -1) {
                self.chats[modifiedChatIndex] = { ...change.doc.data(), id: change.doc.id }
              }
              self.chats.sort((a, b) => b.lastActivityAt - a.lastActivityAt)
            }
          })
        })
        this.unsubscribers.push(chatsUnsub)
      }
      catch (e) {
      }
    },
    async loadMoreChats() {
      const { $db } = useNuxtApp()
      const workspaceStore = useWorkspaceStore()
      const userId = workspaceStore.workspace?.id
      if (!this.chats.length)
        return

      // Add 2 second delay to prevent rapid consecutive calls
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Get the last chat's timestamp
      const lastChat = this.chats[this.chats.length - 1]
      const lastTimestamp = lastChat.lastActivityAt

      // Query for next batch after the last timestamp
      const q = query(
        collection($db, 'chats'),
        where('userId', '==', userId),
        orderBy('lastActivityAt', 'desc'),
        startAfter(lastTimestamp),
        limit(25),
      )

      try {
        const snapshot = await getDocs(q)
        const newChats = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))

        // Add new chats to the existing array
        this.chats.push(...newChats)
      }
      catch (e) {
        console.error('Error loading more chats:', e)
      }
    },
    async initConversations() {
      const { $db } = useNuxtApp()
      const self = this
      const workspaceStore = useWorkspaceStore()
      const userId = workspaceStore.workspace?.id
      const q = query(
        collection($db, 'conversations'),
        where('userId', '==', userId),
        orderBy('lastActivityAt', 'desc'),
        limit(25),
      )
      try {
        const conversationsUnsub = onSnapshot(q, (snapshot) => {
          snapshot.docChanges().forEach((change) => {
            if (change.type === 'added') {
              // Check if conversation already exists
              const exists = self.conversations.some(conv => conv.id === change.doc.id)
              if (!exists) {
                self.conversations.push({ ...change.doc.data(), id: change.doc.id })
                self.conversations.sort((a, b) => b.lastActivityAt - a.lastActivityAt)
              }
            }
            if (change.type === 'modified') {
              const modifiedConversationIndex = self.conversations.findIndex(chat => chat.id === change.doc.id)
              const oldChat = self.conversations[modifiedConversationIndex]
              if (oldChat.lastMessageId !== change.doc.data().lastMessageId) {
                const audio = new Audio(
                  'https://firebasestorage.googleapis.com/v0/b/home-service-62a33.appspot.com/o/webapp%2FyadaDing.mp3?alt=media&token=3369b558-1b47-4348-8534-3d7730cd720d',
                )
                audio.volume = 0.0
                try {
                  audio.play()
                }
                catch (e) {
                  console.warn(e)
                }
              }
              if (modifiedConversationIndex !== -1) {
                self.conversations[modifiedConversationIndex] = { ...change.doc.data(), id: change.doc.id }
              }
              self.conversations.sort((a, b) => b.lastActivityAt - a.lastActivityAt)
            }
          })
        })
        this.unsubscribers.push(conversationsUnsub)
      }
      catch (e) {
      }
    },
    async loadMoreConversations() {
      const { $db } = useNuxtApp()
      const workspaceStore = useWorkspaceStore()
      const userId = workspaceStore.workspace?.id
      if (!this.conversations.length)
        return

      // Add 2 second delay to prevent rapid consecutive calls
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Get the last chat's timestamp
      const lastConversation = this.conversations[this.conversations.length - 1]
      const lastTimestamp = lastConversation.lastActivityAt

      // Query for next batch after the last timestamp
      const q = query(
        collection($db, 'conversations'),
        where('userId', '==', userId),
        orderBy('lastActivityAt', 'desc'),
        startAfter(lastTimestamp),
        limit(25),
      )

      try {
        const snapshot = await getDocs(q)
        const newConversations = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))

        // Add new chats to the existing array
        this.conversations.push(...newConversations)
      }
      catch (e) {
        console.error('Error loading more chats:', e)
      }
    },
  },
})
