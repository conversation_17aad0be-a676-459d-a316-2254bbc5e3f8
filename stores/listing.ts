import type { Unsubscribe } from 'firebase/firestore'
import {
  arrayUnion,
  collection,
  doc,
  onSnapshot,
  query,
  updateDoc,
  where,
} from 'firebase/firestore'

import { getDownloadURL, ref, uploadBytes } from 'firebase/storage'
import { defineStore } from 'pinia'
import { generateUniqueID } from '~/shared'
import { useWorkspaceStore } from './workspace'

interface Listing {
  id: string
  userId: string
  name: string
  active: boolean
  address: string
  thumbnail: string
  respondingActive: boolean
  listingGroup: string
  integrations: any
  completionPercentage: number
}

export const useListingStore = defineStore('listing', {
  state: () => ({
    listings: [] as Listing[],
    listingGroups: [] as any[],
    unsubscribers: [] as Unsubscribe[],
  }),
  actions: {
    async init() {
      console.log('initializing listing store')
      const { $db } = useNuxtApp()
      const self = this
      self.listings = []
      const workspaceStore = useWorkspaceStore()
      const userId = workspaceStore.workspace?.id
      const q = query(
        collection($db, 'listings'),
        where('userId', '==', userId),
        where('archived', '!=', true),
      )

      // Use onSnapshot to listen for real-time updates
      const unsub = onSnapshot(
        q,
        (snapshot) => {
          self.listings = snapshot.docs.map(doc => ({
            id: doc.id,
            userId,
            name: doc.data().name,
            active: doc.data().active,
            address: doc.data().v === 4 ? `${doc.data().address.line1} ${doc.data().address.city}` : doc.data().address.fullAddress,
            thumbnail: doc.data().thumbnail,
            respondingActive: doc.data().respondingActive,
            listingGroup: doc.data().listingGroup,
            integrations: doc.data().integrations,
            completionPercentage: doc.data().completionPercentage,
          }))
        },
        (error) => {
          console.error('error loading firestore stuff')
          console.error(error)
        },
      )
      this.unsubscribers.push(unsub)
    },
    async updateListing(listingId: string, payload: any) {
      const { $db } = useNuxtApp()
      const listingRef = doc($db, 'listings', listingId)
      await updateDoc(listingRef, payload)
    },
    async uploadImage(listingId: string, pic: any) {
      const { $storage, $db } = useNuxtApp()
      const toast = useToast()
      const id = generateUniqueID()
      const fileName = pic.name

      const storageRef = ref($storage, `listing_docs_image/${id}_${fileName}`)
      let snap
      try {
        snap = await uploadBytes(storageRef, pic)
      }
      catch (error) {
        console.error('error uploading image', error)
        return
      }

      let imageUrl
      try {
        imageUrl = await getDownloadURL(snap.ref)
      }
      catch (error) {
        console.error('error getting image url', error)
        return
      }

      const docRef = doc($db, 'listings', listingId)
      try {
        const pictureObj = {
          description: '',
          id,
          src: imageUrl,
        }
        await updateDoc(docRef, { pictures: arrayUnion(pictureObj) })
        toast.add({ title: 'Image uploaded', color: 'info' })
        return pictureObj
      }
      catch (error) {
        console.error('error uploading image', error)
        toast.add({ title: 'Error uploading image', color: 'error' })
      }
    },
  },
})
