import type { ServiceAccount } from 'firebase-admin/app'
import process from 'node:process'
import { cert, getApp, getApps, initializeApp } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore } from 'firebase-admin/firestore'
import { getStorage } from 'firebase-admin/storage'

let serviceAccount: ServiceAccount | undefined

if (process.env.NODE_ENV === 'production') {
  serviceAccount = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS!) as ServiceAccount
}
else {
  serviceAccount = process.env.GOOGLE_APPLICATION_CREDENTIALS! as ServiceAccount
}

let app
if (!getApps().length) {
  app = initializeApp({
    credential: cert(serviceAccount),
    storageBucket: 'home-service-62a33.appspot.com',
  })
}
else {
  app = getApp()
}

const firestore = getFirestore(app)
const storage = getStorage(app)
const auth = getAuth(app)

firestore.settings({ ignoreUndefinedProperties: true })

export { app, auth, firestore, storage }
