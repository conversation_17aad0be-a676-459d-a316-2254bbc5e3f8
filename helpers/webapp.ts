import { useWorkspaceStore } from '~/stores/workspace'

export function getChatCode() {
  const workspaceStore = useWorkspaceStore()
  return `<script type="text/javascript" async>
      var readyStateCheckInterval = setInterval(function() {
        if (document.readyState === "complete") {
          clearInterval(readyStateCheckInterval);
          loadYada();
        }
      }, 50);
  
      function loadYada () {
        const s = document.createElement("script");
        s.src = "https://cdn.yada.ai/widget.umd.js"
        document.body.appendChild(s);
        const w = document.createElement("yada-widget");
        w.setAttribute("id", "${workspaceStore.workspace.id}");
        document.body.appendChild(w);
      }
    </script>`
}
