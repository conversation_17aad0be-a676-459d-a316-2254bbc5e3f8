// utils/chargebee.js
let chargebeePromise = null

export default function loadChargebee() {
  if (!chargebeePromise) {
    chargebeePromise = new Promise((resolve) => {
      if (window.Chargebee) {
        resolve(window.Chargebee)
        return
      }
      const script = document.createElement('script')
      script.onload = () => resolve(window.Chargebee)
      script.setAttribute('src', 'https://js.chargebee.com/v2/chargebee.js')
      document.head.appendChild(script)
    }).then((Chargebee) => {
      let site = ''
      let pk = ''
      if (process.env.NODE_ENV !== 'production') {
        site = 'yada-test'
        pk = 'test_jxVBHWAedCaZtuLuol0HFDKDQj6j5rDy'
      }
      else {
        site = 'yada'
        pk = 'live_dbITcdcu9zHnknSNxopF6PU34210hiFErs'
      }
      Chargebee.init({ site, publishableKey: pk })
      console.log('this is the chargebee object: ', Chargebee)
      return Chargebee
    })
  }
  return chargebeePromise
}
