<script setup lang="ts">
import { InboxChatsInbox, InboxChatsList, InboxConversationsInbox, InboxConversationsList } from '#components'
import { useInboxStore } from '#imports'
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'

const { getTasks } = useApi()

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const inboxStore = useInboxStore()
const route = useRoute()
const toast = useToast()

const tabItems = [{
  label: 'PMS',
  value: 'pms',
}, {
  label: 'Webchat',
  value: 'webchat',
}]
const selectedTab = ref('pms')
const selectedConversation = ref<any>()
const selectedId = ref<string | null>()
const router = useRouter()

// Related data for conversation
const conversationContact = ref<any>()
const conversationStay = ref<any>()
const conversationListing = ref<any>()
const conversationTasks = ref<any>()

const chatContact = ref<any>()

const breakpoints = useBreakpoints(breakpointsTailwind)
const isMobile = breakpoints.smaller('lg')

const isMailPanelOpen = computed({
  get() {
    return !!selectedConversation.value
  },
  set(value: boolean) {
    if (!value) {
      selectedConversation.value = null
      clearAllData()
    }
  },
})

onMounted(async () => {
  const conversationId = route.query.id as string
  if (conversationId) {
    try {
      const res = await useApiFetch(`inbox/${conversationId}`, {
        method: 'GET',
      })
      selectedConversation.value = res
      if (res.type === 'conversation') {
        await loadConversationRelatedData(res)
      }
      else if (res.type === 'chat') {
        await loadChatRelatedData(res)
      }
    }
    catch (error: any) {
      toast.add({
        title: 'Error loading conversation/chat',
        description: error.message || 'An error occurred while loading the conversation/chat.',
        color: 'error',
      })
    }
  }
})

watch(selectedId, (newId) => {
  if (newId) {
    router.replace({ query: { id: newId } })
  }
  else {
    router.replace({ query: {} })
  }
}, { immediate: false })

watch(() => route.query.id, async (newId) => {
  if (newId && typeof newId === 'string') {
    try {
      const res = await useApiFetch(`inbox/${newId}`, {
        method: 'GET',
      })
      selectedConversation.value = res
      if (res.type === 'conversation') {
        await loadConversationRelatedData(res)
      }
      else if (res.type === 'chat') {
        await loadChatRelatedData(res)
      }
    }
    catch (error: any) {
      toast.add({
        title: 'Error loading conversation/chat',
        description: error.message || 'An error occurred while loading the conversation/chat.',
        color: 'error',
      })
    }
  }
  else if (!newId) {
    selectedConversation.value = null
    clearAllData()
  }
})

async function loadConversationRelatedData(conversation: any) {
  console.log('Loading conversation related data for:', conversation.id)
  const contact = await useApiFetch(`contacts/${conversation.contactId}`, {
    method: 'GET',
  })
  const stay = await useApiFetch(`stays/${conversation.stayId}`, {
    method: 'GET',
  })
  const listing = await useApiFetch(`listings/${conversation.listingId}`, {
    method: 'GET',
  })
  const tasks = await getTasks({
    conversationId: conversation.id,
  })

  // Store the loaded data in reactive variables
  conversationContact.value = contact
  conversationStay.value = stay
  conversationListing.value = listing
  conversationTasks.value = tasks
}

async function loadChatRelatedData(chat: any) {
  console.log('Loading chat related data for:', chat)
  const contact = await useApiFetch(`contacts/${chat.contactId}`, {
    method: 'GET',
  })
  chatContact.value = contact
}

function clearConversationData() {
  conversationContact.value = null
  conversationStay.value = null
  conversationListing.value = null
  conversationTasks.value = null
}

function clearChatData() {
  chatContact.value = null
}

function clearAllData() {
  clearConversationData()
  clearChatData()
}
</script>

<template>
  <UDashboardPanel id="inbox-1" :default-size="20">
    <UDashboardNavbar title="Inbox">
      <template #right>
        <UTabs v-model="selectedTab" :items="tabItems" :content="false" size="xs" />
      </template>
    </UDashboardNavbar>
    <div v-if="selectedTab === 'pms'" class="flex-1 flex flex-col h-full">
      <InboxConversationsList v-model="selectedId" :conversations="inboxStore.conversations" />
    </div>
    <div v-else-if="selectedTab === 'webchat'" class="flex-1 flex flex-col h-full">
      <InboxChatsList v-model="selectedId" :chats="inboxStore.chats" />
    </div>
  </UDashboardPanel>

  <!-- <InboxMail v-if="selectedMail" :mail="selectedMail" @close="selectedMail = null" /> -->
  <div v-if="selectedConversation">
    <InboxConversationsInbox v-if="selectedConversation.type === 'conversation'" :conversation="selectedConversation"
      :contact="conversationContact" :stay="conversationStay" :listing="conversationListing" :tasks="conversationTasks"
      @close="selectedConversation = null" />
    <InboxChatsInbox v-else-if="selectedConversation.type === 'chat'" :chat="selectedConversation" :contact="chatContact"
      @close="selectedConversation = null" />
  </div>
  <div v-else class="hidden lg:flex flex-1 items-center justify-center">
    <UIcon name="i-lucide-inbox" class="size-32 text-dimmed" />
  </div>

  <ClientOnly>
    <USlideover v-if="isMobile" v-model:open="isMailPanelOpen">
      <template #content>
        <InboxConversationsInbox v-if="selectedConversation?.type === 'conversation'" :conversation="selectedConversation"
          :contact="conversationContact" :stay="conversationStay" :listing="conversationListing"
          :tasks="conversationTasks" @close="selectedConversation = null" />
        <InboxChatsInbox v-else-if="selectedConversation?.type === 'chat'" :chat="selectedConversation"
          :contact="chatContact" @close="selectedConversation = null" />
      </template>
    </USlideover>
  </ClientOnly>
</template>
