<script lang="ts" setup>
import type { TableColumn } from '@nuxt/ui'
import { AppConfirm, BillingAddPaymentMethod } from '#components'
import { useListingStore, useWorkspaceStore } from '#imports'
import { computed } from 'vue'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})
const overlay = useOverlay()
const toast = useToast()
const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const expandNumListings = ref()
const expandMinListings = ref()
const annual = ref(false)
const isActivating = ref(false)
const isUpdating = ref(false)
const isSaving = ref(false)
const numListings = ref(10)
const subscribeDialog = ref(false)
const cancelDialog = ref(false)
const terms = ref(false)
const deletion = ref(false)

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

function hasPaymentMethod() {
  return workspaceStore.billing && Array.isArray(workspaceStore.billing.paymentMethod) && workspaceStore.billing.paymentMethod.length > 0 && workspaceStore.billing.paymentMethod[0].card
}

async function handleAddPaymentMethod() {
  const addPaymentMethodModal = overlay.create(BillingAddPaymentMethod, {
    props: {
    },
  })
  const addPaymentMethodModalInstance = addPaymentMethodModal.open()
  const result: any = await addPaymentMethodModalInstance.result
  if (!result)
    return
  await workspaceStore.loadBilling()
}

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'Invoice #',
    cell: ({ row }) => `#${row.getValue('id')}`,
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => formatDate(row.getValue('date')),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => capitalize(row.getValue('status')),
  },
  {
    accessorKey: 'total',
    header: 'Total',
    cell: ({ row }) => formatCurrency(row.getValue('total'), row.getValue('currency_code')),
  },
  {
    accessorKey: 'line_items',
    header: 'Line Items',
    cell: ({ row }) => formatLineItems(row.original),
  },
  {
    accessorKey: 'paid_at',
    header: 'Paid At',
    cell: ({ row }) => formatDate(row.getValue('paid_at')),
  },
]

function capitalize(value: string) {
  if (!value)
    return ''
  return value.charAt(0).toUpperCase() + value.slice(1)
}

function formatDate(ts: any) {
  if (!ts)
    return ''
  const d = new Date(ts * 1000)
  return d.toLocaleDateString()
}

function formatCurrency(amount: any, currency: any) {
  if (typeof amount !== 'number')
    return ''
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: currency || 'USD' }).format(amount / 100)
}

function formatLineItems(invoice: any) {
  if (!invoice.line_items || !invoice.line_items.length)
    return ''
  return invoice.line_items.map(item => `${item.description} (x${item.quantity}) - ${formatCurrency(item.amount, invoice.currency_code)}`).join('; ')
}

async function confirmSubscriptionUpdate() {
  console.log('NUM LISTINGS: ', expandNumListings.value)
  if (expandNumListings.value <= (workspaceStore.billing.subscription.subscription_items[0].quantity)) {
    toast.add({
      color: 'error',
      title: 'Invalid Selection',
      description: 'Please choose a number of listings greater than your current plan.',
    })
  }
  const confirmModal = overlay.create(AppConfirm, {
    props: {
      message: `Update your subscription to ${expandNumListings.value} listings?`,
    },
  })
  const confirmModalInstance = confirmModal.open()
  const result: any = await confirmModalInstance.result
  if (!result)
    return

  console.log('ready to go on...')
  try {
    await useApiFetch('workspace/billing/update-subscription', {
      method: 'POST',
      body: { numListings: expandNumListings.value },
    })
    await workspaceStore.loadBilling()
  }
  catch (e) {
    toast.add({
      color: 'error',
      title: 'Unable to update subscription',
      description: e.message,
    })
  }
  finally {
    console.log('done')
  }
}

onMounted(async () => {
  await workspaceStore.loadBilling()
  if (workspaceStore?.billing?.subscription?.subscription_items[0]?.quantity) {
    const currentQuantity = workspaceStore.billing.subscription.subscription_items[0].quantity
    expandNumListings.value = currentQuantity
    expandMinListings.value = currentQuantity
  }
})
</script>

<template>
  <UDashboardPanel id="billing">
    <template #header>
      <UDashboardNavbar title="Billing">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <UCard>
        <template #header>
          <div>Payment Method</div>
        </template>
        <div v-if="hasPaymentMethod()">
          <strong>{{ capitalize(workspaceStore.billing.paymentMethod[0].card.brand) }}</strong>
          ending in {{ workspaceStore.billing.paymentMethod[0].card.last4 }},
          Expires {{ workspaceStore.billing.paymentMethod[0].card.expiry_month }}/{{ workspaceStore.billing.paymentMethod[0].card.expiry_year }}
        </div>
        <div v-else>
          No payment method on file.
        </div>
        <template #footer>
          <UButton @click="handleAddPaymentMethod">
            {{ hasPaymentMethod() ? 'Update Payment Method' : 'Add Payment Method' }}
          </UButton>
        </template>
      </UCard>
      <UCard v-if="!workspaceStore.billing.subscription">
        <template #header>
          <div>Create New Subscription</div>
        </template>
      </UCard>
      <UCard v-else>
        <template #header>
          <div>Manage Subscription</div>
        </template>
        <div class="flex justify-between items-center mb-4">
          <span>Active Listings / Listings on your Subscription</span>
          <span>{{ listingStore.listings.filter(obj => obj.active === true).length }} / {{ workspaceStore.billing.subscription.subscription_items[0].quantity }}</span>
        </div>
        <div v-if="expandMinListings >= listingStore.listings.length">
          <UAlert color="primary" variant="subtle" title="Heads Up!" description="You are at the maximum number of listings for your account." icon="i-lucide-info" class="my-2" />
        </div>
        <div v-else class="mb-4">
          <div class="text-lg font-medium mb-2">
            Adjust Number of Listings
          </div>
          <div v-if="listingStore.listings.length <= 5" class="text-gray-600 dark:text-gray-400 mb-4">
            You are at the minimum number of listings for your account
          </div>
          <div v-else>
            <div class="mb-2">
              Select the number of listings for your subscription. Minimum of {{ expandMinListings }} listings required.
            </div>
            <USelect
              v-model="expandNumListings"
              :items="Array.from({ length: Math.ceil(listingStore.listings.length) - expandMinListings + 1 }, (_, i) => i + expandMinListings)"
              class="w-full"
              placeholder="Select number of listings"
            />
            <div class="flex justify-between text-sm ">
              <span>{{ expandNumListings }} Listings</span>
              <span>${{ (expandNumListings * (annual ? 9.99 : 12.49)).toFixed(2) }}/{{ annual ? 'month (billed annually)' : 'month' }}</span>
            </div>

            <div class="flex justify-end mt-4">
              <UButton
                label="Update Subscription"
                severity="success"
                :disabled="expandNumListings <= workspaceStore.billing.subscription.subscription_items[0].quantity"
                :loading="isUpdating"
                @click="confirmSubscriptionUpdate"
              />
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>Invoices</div>
        </template>
        <div>
          <div v-if="!workspaceStore.billing.invoices || workspaceStore.billing.invoices.length === 0" class="text-gray-500">
            No invoices available
          </div>
          <div v-else>
            <UTable :data="workspaceStore.billing.invoices" :columns="columns" />
          </div>
        </div>
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
