<script lang="ts" setup>
import { useWorkspaceStore } from '#imports'
import { ref } from 'vue'
import { useProfile } from '~/composables/useProfile'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const workspaceStore = useWorkspaceStore()
const { updateProfile } = useProfile()
const isSaving = ref(false)
const saveError = ref<string | null>(null)

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

async function handleSave() {
  isSaving.value = true
  saveError.value = null
  try {
    await updateProfile({
      firstName: workspaceStore.profile.firstName,
      lastName: workspaceStore.profile.lastName,
      phone: workspaceStore.profile.phone,
    })
  }
  catch (err: any) {
    console.log('error updating profile', err)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <UDashboardPanel id="settings-profile">
    <template #header>
      <UDashboardNavbar title="Profile">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <UCard>
        <template #header>
          <div>Profile Information</div>
        </template>
        <div class="flex flex-col gap-4">
          <UFormField label="First Name">
            <UInput v-model="workspaceStore.profile.firstName" label="First Name" />
          </UFormField>
          <UFormField label="Last Name">
            <UInput v-model="workspaceStore.profile.lastName" label="Last Name" />
          </UFormField>
          <UFormField label="Phone">
            <UInput v-model="workspaceStore.profile.phone" label="Phone" />
          </UFormField>
        </div>
        <template #footer>
          <UButton :disabled="isSaving" @click="handleSave">
            Save Changes
          </UButton>
        </template>
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
