<script lang="ts" setup>
import type { TableColumn } from '@nuxt/ui'
import { AgentsCreate } from '#components'
import { h } from 'vue'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const router = useRouter()

const overlay = useOverlay()

const agentsCreateModal = overlay.create(AgentsCreate, {
  props: {
  },
})

const agents = ref(await useApiFetch('workspace/agents'))

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      return h('span', {
        style: 'color: #2563eb; cursor: pointer; text-decoration: underline;',
        onClick: () => { router.push(`agents/${row.original.id}`) },
      }, row.original.email)
    },
  },
  {
    accessorKey: 'firstName',
    header: 'First Name',
    cell: ({ row }) => `${row.getValue('firstName')}`,
  },
  {
    accessorKey: 'lastName',
    header: 'Last Name',
    cell: ({ row }) => `${row.getValue('lastName')}`,
  },
  // {
  //   id: 'resetpwd',
  // },
  // {
  //   id: 'delete',
  // },
]

async function handleCreateAgent() {
  const agentCreateModalInstance = agentsCreateModal.open()
  const result = await agentCreateModalInstance.result
  if (!result) {
    return
  }

  agents.value = await useApiFetch('workspace/agents')
}
</script>

<template>
  <UDashboardPanel id="settings-agents">
    <template #header>
      <UDashboardNavbar title="Manage Agents">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton @click="handleCreateAgent()">
            Create Agent
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UTable :data="agents" :columns="columns">
        <template #resetpwd-cell="{ row }">
          <UButton
            color="primary"
            variant="subtle"
            label="Reset Password"
          />
        </template>
        <template #delete-cell="{ row }">
          <UButton
            color="error"
            variant="subtle"
            label="Delete Agent"
            disabled
          />
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
