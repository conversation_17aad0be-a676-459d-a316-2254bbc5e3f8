<script setup lang="ts">
import { ref, watch } from 'vue'
import { taskTypes, timezones } from '~/shared/webapp'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const route = useRoute()
const toast = useToast()
const agent = ref(await useApiFetch(`workspace/agents/${route.params.id}`))

// Ensure agent.autoAssignTasks is always an array
if (!Array.isArray(agent.value.autoAssignTasks)) {
  agent.value.autoAssignTasks = []
}

// Helper ref for the select
const selectedTimezoneName = ref(agent.value.timezone?.name || '')
const isSaving = ref(false)

const timeOptions = [
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
  '00:00',
]

const dayOptions = [
  'Weekdays',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
  'Weekends',
]

// Keep agent.timezone always as the object
watch(selectedTimezoneName, (newName) => {
  const tz = timezones.find(tz => tz.name === newName)
  if (tz)
    agent.value.timezone = tz
})

// Initialize the select if agent.timezone is already set
watch(
  () => agent.value.timezone,
  (tz) => {
    if (tz && tz.name !== selectedTimezoneName.value) {
      selectedTimezoneName.value = tz.name
    }
  },
  { immediate: true, deep: true },
)

function addWorkingHoursSlot() {
  agent.value.workingHoursSlots.push({ day: '', startTime: '', endTime: '' })
}

function removeWorkingHoursSlot(index: number) {
  agent.value.workingHoursSlots.splice(index, 1)
}

async function saveChanges() {
  isSaving.value = true
  // Validate that each time slot has startTime and endTime
  const invalidSlotIndex = agent.value.workingHoursSlots.findIndex(slot => !slot.startTime || !slot.endTime)
  if (invalidSlotIndex !== -1) {
    isSaving.value = false
    toast.add({
      title: 'Unable to save changes',
      description: 'One or more time slots are missing Start Time or End Time',
      color: 'error',
    })
    return
  }
  try {
    const payload = {
      timezone: agent.value.timezone || null,
      workingHoursSlots: agent.value.workingHoursSlots || [],
      autoAssignTasks: agent.value.autoAssignTasks || [],
    }
    await useApiFetch(`workspace/agents/${route.params.id}`, {
      method: 'PATCH',
      body: payload,
    })
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <UDashboardPanel id="settings-agent">
    <template #header>
      <UDashboardNavbar title="Manage Agent">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton color="primary" :disabled="isSaving" :loading="isSaving" @click="saveChanges">
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UCard>
        <template #header>
          <div>Time Zone & Hours</div>
        </template>
        <USelect
          v-model="selectedTimezoneName"
          placeholder="Time Zone"
          :items="timezones"
          value-key="name"
          label-key="name"
          class="w-full"
        />
        <!-- Working Hours Section -->
        <div class="mt-6">
          <div class="flex items-center justify-between mb-2">
            <div class="font-semibold">
              Working Hours
            </div>
            <UButton size="xs" icon="i-heroicons-plus" color="primary" variant="soft" @click="addWorkingHoursSlot">
              Add Slot
            </UButton>
          </div>
          <div v-for="(slot, index) in agent.workingHoursSlots" :key="index" class="mb-3">
            <div class="flex gap-3 items-center">
              <USelect
                v-model="slot.day"
                :items="dayOptions"
                placeholder="Day"
                class="w-32"
              />
              <USelect
                v-model="slot.startTime"
                :items="timeOptions"
                placeholder="Start Time"
                class="w-28"
              />
              <USelect
                v-model="slot.endTime"
                :items="timeOptions"
                placeholder="End Time"
                class="w-28"
              />
              <UButton size="xs" icon="i-heroicons-trash" color="red" variant="soft" @click="removeWorkingHoursSlot(index)" />
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>Auto Assign Tasks</div>
        </template>
        <USelect
          v-model="agent.autoAssignTasks"
          :items="taskTypes"
          label-key="label"
          value-key="value"
          multiple
          placeholder="Select Task Types"
          class="w-full"
        />
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style scoped>

</style>
