<script lang="ts" setup>
import { useWorkspaceStore } from '#imports'

const workspaceStore = useWorkspaceStore()

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

async function uploadImage(file: File) {
  const extension = file.name.slice(file.name.lastIndexOf('.'))
  const randomId = crypto.randomUUID()
  const newFileName = `workspace/${randomId}${extension}`
  const formData = new FormData()
  formData.append('file', file, newFileName)
  try {
    const response = await useApiFetch('blob/upload', {
      method: 'POST',
      body: formData,
    })
    return response
  }
  catch (e) {
    throw e
  }
}

async function handleImageUpload(event: Event) {
  const files = (event.target as HTMLInputElement)?.files
  if (files) {
    for (const file of Array.from(files)) {
      try {
        const blob = await uploadImage(file)
        workspaceStore.workspace.logo = blob.url
      }
      catch (e) {
        console.error('Failed to upload', file.name, e)
      }
    }
  }
}

function triggerFileInput() {
  document.getElementById('imageUploadInput')?.click()
}

async function saveChanges() {
  // TODO: Replace with actual save logic
  console.log('in this function that will save changes')
  try {
    await useApiFetch('workspace', {
      method: 'PATCH',
      body: {
        companyName: workspaceStore.workspace.companyName,
        bookingSiteUrl: workspaceStore.workspace.bookingSiteUrl,
        aboutUs: workspaceStore.workspace.aboutUs,
        logo: workspaceStore.workspace.logo,
        guestContactEmail: workspaceStore.workspace.guestContactEmail,
        guestContactPhone: workspaceStore.workspace.guestContactPhone,
      },
    })
  }
  catch (e) {
    console.error('Failed to save changes', e)
  }
}
</script>

<template>
  <UDashboardPanel id="settings-brand">
    <template #header>
      <UDashboardNavbar title="Brand Settings">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton color="primary" @click="saveChanges">
            Save Changes
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <UCard>
        <template #header>
          <div>Company Name</div>
        </template>
        <UInput v-model="workspaceStore.workspace.companyName" label="Company Name" class="w-full" />
      </UCard>
      <UCard>
        <template #header>
          <div>Direct Booking Site URL</div>
          <div class="text-sm">
            This will display the direct booking site link on all of your smart guidebooks, along with a note nudging
            visitors to book directly the next time instead of booking through the OTA.
          </div>
        </template>
        <UInput v-model="workspaceStore.workspace.bookingSiteUrl" label="Direct Booking Site URL" class="w-full" />
      </UCard>
      <UCard>
        <template #header>
          <div>Special Offer</div>
          <div class="text-sm">
            Add a special offer or promotion that will be displayed to guests who book through OTAs to encourage direct bookings next time.
          </div>
        </template>
        <UInput v-model="workspaceStore.workspace.offer" label="Direct Booking Site URL" class="w-full" />
      </UCard>
      <UCard>
        <template #header>
          <div>About Us</div>
        </template>
        <UInput v-model="workspaceStore.workspace.aboutUs" label="Direct Booking Site URL" class="w-full" />
      </UCard>
      <UCard>
        <template #header>
          <div>Guest Contact Info</div>
          <div class="text-sm">
            This is the contact information that guests can use to get in touch with you. If not filled out, it will default to the contact information in your admin profile.
          </div>
        </template>
        <UInput v-model="workspaceStore.workspace.guestContactEmail" label="Guest Contact Email" class="w-full mb-2" />
        <UInput v-model="workspaceStore.workspace.guestContactPhone" label="Guest Contact Phone" class="w-full" />
      </UCard>
      <UCard>
        <template #header>
          <div>Logo</div>
        </template>
        <UButton label="Upload Image" icon="i-lucide-upload" size="sm" @click="triggerFileInput" />
        <input
          id="imageUploadInput" type="file" hidden accept="image/jpeg,image/png,image/webp"
          @change="handleImageUpload"
        >
        <NuxtImg v-if="workspaceStore.workspace.logo" :src="workspaceStore.workspace.logo" alt="Image preview" class="max-w-[200px]" />
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
