<script lang="ts" setup>
import { useWorkspaceStore } from '#imports'
import { computed, ref, toRaw, watch } from 'vue'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const workspaceStore = useWorkspaceStore()

// Store a deep copy of the original profile
const originalProfile = ref<any>(null)

watch(
  () => workspaceStore.profile,
  (profile) => {
    if (profile && !originalProfile.value) {
      originalProfile.value = JSON.parse(JSON.stringify(toRaw(profile)))
    }
  },
  { immediate: true, deep: true },
)

const hasChanges = computed(() => {
  if (!originalProfile.value || !workspaceStore.profile)
    return false
  return !isEqual(originalProfile.value, workspaceStore.profile)
})

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

function isEqual(obj1: any, obj2: any) {
  return JSON.stringify(obj1) === JSON.stringify(obj2)
}

function afterSave() {
  // Call this after a successful save to reset originalProfile
  originalProfile.value = JSON.parse(JSON.stringify(toRaw(workspaceStore.profile)))
}

const isSaving = ref(false)

function getChangedFields(original: any, current: any) {
  // Recursively find changed fields
  const changed: Record<string, any> = {}
  for (const key in current) {
    if (typeof current[key] === 'object' && current[key] !== null && original[key] !== null && typeof original[key] === 'object') {
      const nested = getChangedFields(original[key], current[key])
      if (Object.keys(nested).length)
        changed[key] = nested
    }
    else if (current[key] !== original[key]) {
      changed[key] = current[key]
    }
  }
  return changed
}

async function handleSave() {
  if (!originalProfile.value || !workspaceStore.profile)
    return
  isSaving.value = true
  // Only send changed fields
  const changedFields = getChangedFields(originalProfile.value, workspaceStore.profile)

  try {
    await useApiFetch('workspace', {
      method: 'PATCH',
      body: changedFields,
    })
    afterSave()
  }
  catch (e) {
    console.error('Failed to save changes', e)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <UDashboardPanel id="settings-ai">
    <template #header>
      <UDashboardNavbar title="AI Settings">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            color="primary"
            :disabled="!hasChanges || isSaving"
            :loading="isSaving"
            @click="handleSave"
          >
            <span v-if="!isSaving">Save Changes</span>
            <span v-else>Saving...</span>
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <div class="space-y-6">
        <!-- Basic AI Settings Card -->
        <UCard>
          <template #header>
            <div class="text-lg font-medium">
              Basic AI Settings
            </div>
          </template>
          <div class="space-y-8">
            <!-- AI Accuracy Setting -->
            <div class="space-y-3">
              <div class="font-medium text-base">
                AI Accuracy
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                AI Accuracy is a measure of how accurate the AI response is given the data you have provided it.
                <p class="mt-1">
                  Low accuracy thresholds allow many pieces of generated data to be sent to the guest.
                </p>
                <p class="mt-1">
                  High accuracy thresholds will block responses which have anything in them outside of your existing content.
                </p>
                <p class="mt-2 text-xs italic">
                  Note: Most AI generated responses are either 0 or 1, meaning the system will auto-ignore poorly generated responses.
                </p>
              </div>
              <div class="flex items-center gap-4">
                <USlider v-model="workspaceStore.profile.accuracy" :min="0.5" :max="1" :step="0.1" class="flex-1" />
                <span class="text-sm font-mono w-16 text-right">{{ workspaceStore.profile.accuracy }}</span>
              </div>
            </div>

            <!-- AI Backoff Time Setting -->
            <div class="space-y-3">
              <div class="font-medium text-base">
                AI Backoff Time
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Set the duration for how long the AI will be backed off when a human answers a question and gets involved in a conversation. For example, a backoff time of 30 minutes means that the AI will not respond to a guest for 30 minutes after a human has answered a question.
              </div>
              <div class="flex items-center gap-4">
                <USlider v-model="workspaceStore.profile.backoffMinutes" :min="0" :max="30" :step="1" class="flex-1" />
                <span class="text-sm font-mono w-16 text-right">{{ workspaceStore.profile.backoffMinutes }} min</span>
              </div>
            </div>

            <!-- AI SmallTalk Setting -->
            <div class="space-y-3">
              <div class="font-medium text-base">
                AI SmallTalk
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Toggle whether AI Small Talk is enabled or not. Small Talk is a feature that allows the AI to respond to basic messages like gratitude, updates, etc.
              </div>
              <div class="flex items-center">
                <USwitch v-model="workspaceStore.profile.smallTalk" class="mr-3" />
                <span class="text-sm">{{ workspaceStore.profile.smallTalk ? 'Enabled' : 'Disabled' }}</span>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Advanced AI Settings Card -->
        <UCard>
          <template #header>
            <div class="text-lg font-medium">
              Advanced AI Settings
            </div>
          </template>
          <div class="space-y-8">
            <!-- Approve All AI Messages Setting -->
            <div class="space-y-3">
              <div class="font-medium text-base">
                Approve All AI Messages
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Toggle whether all AI messages need to be approved by a human before being sent to guests.
              </div>
              <div class="flex items-center">
                <USwitch v-model="workspaceStore.profile.approveAiMessage" class="mr-3" />
                <span class="text-sm">{{ workspaceStore.profile.approveAiMessage ? 'Enabled' : 'Disabled' }}</span>
              </div>
            </div>

            <!-- Approve Uncertain AI Messages Setting -->
            <div class="space-y-3">
              <div class="font-medium text-base">
                Approve Uncertain AI Messages
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                Toggle whether uncertain AI messages need to be approved by a human before being sent to guests.
              </div>
              <div class="flex items-center">
                <USwitch v-model="workspaceStore.profile.approveUncertainAiMessage" class="mr-3" />
                <span class="text-sm">{{ workspaceStore.profile.approveUncertainAiMessage ? 'Enabled' : 'Disabled' }}</span>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped>
/* Add any scoped styles here if needed */
</style>
