<script lang="ts" setup>
import { useWorkspaceStore } from '#imports'
import { ref } from 'vue'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const workspaceStore = useWorkspaceStore()
const isSaving = ref(false)
const saveError = ref<string | null>(null)

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

async function handleNotificationChange(key: string, value: boolean) {
  try {
    await useApiFetch('workspace/user', {
      method: 'PATCH',
      body: {
        [`notifications.${key}`]: value,
      },
    })
  }
  catch (err: any) {
    saveError.value = err.message || 'Failed to update notification setting.'
  }
}
</script>

<template>
  <UDashboardPanel id="settings-notifications">
    <template #header>
      <UDashboardNavbar title="Notifications">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <div v-if="isSaving" class="text-blue-500 text-sm mb-2">
        Saving...
      </div>
      <div v-if="saveError" class="text-red-500 text-sm mb-2">
        {{ saveError }}
      </div>
      <UCard>
        <template #header>
          <div>Digests</div>
          <div class="text-sm text-gray-500 mt-1">
            Periodic summary emails to keep you updated on your tasks, AI activity, and guidebooks.
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <USwitch
              v-model="workspaceStore.profile.notifications.dailyTaskDigestEmail"
              label="Daily Task Digest Email"
              @change="handleNotificationChange('dailyTaskDigestEmail', workspaceStore.profile.notifications.dailyTaskDigestEmail)"
            />

            <div class="text-xs text-gray-500 mt-1">
              Receive a daily summary of your assigned and tracked tasks.
            </div>
          </div>
          <div>
            <USwitch
              v-model="workspaceStore.profile.notifications.weeklyTaskDigestEmail"
              label="Weekly Task Digest Email"
              @change="handleNotificationChange('weeklyTaskDigestEmail', workspaceStore.profile.notifications.weeklyTaskDigestEmail)"
            />
            <div class="text-xs text-gray-500 mt-1">
              Receive a weekly summary of your assigned and tracked tasks.
            </div>
          </div>
          <div>
            <USwitch
              v-model="workspaceStore.profile.notifications.dailyAiDigestEmail"
              label="Daily AI Digest Email"
              @change="handleNotificationChange('dailyAiDigestEmail', workspaceStore.profile.notifications.dailyAiDigestEmail)"
            />
            <div class="text-xs text-gray-500 mt-1">
              Get a daily overview of AI-related activity.
            </div>
          </div>
          <div>
            <USwitch
              v-model="workspaceStore.profile.notifications.weeklyAiDigestEmail"
              label="Weekly AI Digest Email"
              @change="handleNotificationChange('weeklyAiDigestEmail', workspaceStore.profile.notifications.weeklyAiDigestEmail)"
            />
            <div class="text-xs text-gray-500 mt-1">
              Get a weekly overview of AI-related activity.
            </div>
          </div>
          <div>
            <USwitch
              v-model="workspaceStore.profile.notifications.weeklyGuidebookSmartSpaceDigestEmail"
              label="Weekly Guidebook SmartSpace Digest Email"
              @change="handleNotificationChange('weeklyGuidebookSmartSpaceDigestEmail', workspaceStore.profile.notifications.weeklyGuidebookSmartSpaceDigestEmail)"
            />
            <div class="text-xs text-gray-500 mt-1">
              Weekly summary of guidebook and SmartSpace updates.
            </div>
          </div>
          <div>
            <USwitch
              v-model="workspaceStore.profile.notifications.dailyGuidebookSmartSpaceDigestEmail"
              label="Daily Guidebook SmartSpace Digest Email"
              @change="handleNotificationChange('dailyGuidebookSmartSpaceDigestEmail', workspaceStore.profile.notifications.dailyGuidebookSmartSpaceDigestEmail)"
            />
            <div class="text-xs text-gray-500 mt-1">
              Daily summary of guidebook and SmartSpace updates.
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>AI Interactions</div>
          <div class="text-sm text-gray-500 mt-1">
            Notifications about AI responses, approvals, and related actions across your channels.
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiRespondedSms" label="AI Responded SMS" />
            <div class="text-xs text-gray-500 mt-1">
              Get notified via SMS when AI responds to a message.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiRespondedBrowser" label="AI Responded Browser" />
            <div class="text-xs text-gray-500 mt-1">
              Browser notification when AI responds.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiRespondedEmail" label="AI Responded Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification when AI responds.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiRespondedApp" label="AI Responded App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification when AI responds.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiRespondedSlack" label="AI Responded Slack" />
            <div class="text-xs text-gray-500 mt-1">
              Slack notification when AI responds.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiNoRespondApp" label="AI No Respond App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification if AI does not respond.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiNoRespondSlack" label="AI No Respond Slack" />
            <div class="text-xs text-gray-500 mt-1">
              Slack notification if AI does not respond.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiNoRespondEmail" label="AI No Respond Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification if AI does not respond.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiNoRespondBrowser" label="AI No Respond Browser" />
            <div class="text-xs text-gray-500 mt-1">
              Browser notification if AI does not respond.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.aiNoRespondSms" label="AI No Respond SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification if AI does not respond.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.approveSlack" label="AI Approve Slack" />
            <div class="text-xs text-gray-500 mt-1">
              Slack notification for approval requests.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.approveSms" label="AI Approve SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification for approval requests.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.approveApp" label="AI Approve App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification for approval requests.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.approveBrowser" label="AI Approve Browser" />
            <div class="text-xs text-gray-500 mt-1">
              Browser notification for approval requests.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.approveEmail" label="AI Approve Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification for approval requests.
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>Widget & Live Chat</div>
          <div class="text-sm text-gray-500 mt-1">
            Updates and alerts from your website widget and live chat integrations.
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.webchatSms" label="Webchat SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification for webchat activity.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.webchatApp" label="Webchat App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification for webchat activity.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.webchatGuesty" label="Webchat Guesty" />
            <div class="text-xs text-gray-500 mt-1">
              Guesty integration notification for webchat.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.webchatSlack" label="Webchat Slack" />
            <div class="text-xs text-gray-500 mt-1">
              Slack notification for webchat activity.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.webchatEmail" label="Webchat Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification for webchat activity.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.webchatBrowser" label="Webchat Browser" />
            <div class="text-xs text-gray-500 mt-1">
              Browser notification for webchat activity.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.widgetPopupApp" label="Widget Popup App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification for widget popups.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.widgetPopupSms" label="Widget Popup SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification for widget popups.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.widgetPopupEmail" label="Widget Popup Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification for widget popups.
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>Workflows</div>
          <div class="text-sm text-gray-500 mt-1">
            Notifications for upsell opportunities and workflow conversions.
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.upsellApp" label="Upsell App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification for upsell opportunities.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.upsellEmail" label="Upsell Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification for upsell opportunities.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.upsellSms" label="Upsell SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification for upsell opportunities.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.conversionApp" label="Conversion App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification for workflow conversions.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.conversionSms" label="Conversion SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification for workflow conversions.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.conversionEmail" label="Conversion Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification for workflow conversions.
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>Tasks</div>
          <div class="text-sm text-gray-500 mt-1">
            Stay informed about changes, assignments, and status updates for your tasks.
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskStatusChangedApp" label="Task Status Changed App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification when a task status changes.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskStatusChangedEmail" label="Task Status Changed Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification when a task status changes.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskStatusChangedSms" label="Task Status Changed SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification when a task status changes.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskCreatedApp" label="Task Created App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification when a task is created.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskCreatedEmail" label="Task Created Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification when a task is created.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskCreatedSms" label="Task Created SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification when a task is created.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskAssignedApp" label="Task Assigned App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification when a task is assigned to you.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskAssignedEmail" label="Task Assigned Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification when a task is assigned to you.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.taskAssignedSms" label="Task Assigned SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification when a task is assigned to you.
            </div>
          </div>
        </div>
      </UCard>
      <UCard>
        <template #header>
          <div>Guidebooks</div>
          <div class="text-sm text-gray-500 mt-1">
            Get notified about guidebook submissions and SmartSpace scans.
          </div>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.guidebookEmailSubmittedApp" label="Guidebook Email Submitted App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification when a guidebook email is submitted.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.guidebookEmailSubmittedEmail" label="Guidebook Email Submitted Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification when a guidebook email is submitted.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.guidebookEmailSubmittedSms" label="Guidebook Email Submitted SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification when a guidebook email is submitted.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.smartSpaceScanApp" label="SmartSpace Scan App" />
            <div class="text-xs text-gray-500 mt-1">
              In-app notification for SmartSpace scans.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.smartSpaceScanEmail" label="SmartSpace Scan Email" />
            <div class="text-xs text-gray-500 mt-1">
              Email notification for SmartSpace scans.
            </div>
          </div>
          <div>
            <USwitch v-model="workspaceStore.profile.notifications.smartSpaceScanSms" label="SmartSpace Scan SMS" />
            <div class="text-xs text-gray-500 mt-1">
              SMS notification for SmartSpace scans.
            </div>
          </div>
        </div>
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
