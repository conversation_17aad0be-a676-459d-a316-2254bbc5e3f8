<script lang="ts" setup>
import { IntegrationsCard, IntegrationsGuesty, IntegrationsHostaway, IntegrationsOwnerrez } from '#components'
import { ref } from 'vue'
import { useWorkspaceStore } from '~/stores/workspace' // Assuming a profile store exists

// IntegrationsGuesty, IntegrationsHostaway, IntegrationsOwnerrez

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const overlay = useOverlay()
const workspaceStore = useWorkspaceStore() // Use the profile store
const profile = workspaceStore.profile // Access the profile data

const guestyModal = overlay.create(IntegrationsGuesty, {})
// const hostawayModal = overlay.create(IntegrationsHostaway, {})
const ownerrezModal = overlay.create(IntegrationsOwnerrez, {})

const search = ref('')

const availableIntegrations = [
  {
    name: 'Guesty',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'guesty',
  },
  {
    name: 'OwnerRez',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'ownerrez',
  },
  {
    name: 'Hostaway',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'hostaway',
  },
  {
    name: 'AirBnB',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'airbnb',
  },
  {
    name: 'OpenPhone',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'openphone', // Added route for consistency check
  },
  {
    name: 'Slack',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'slack',
  },
  {
    name: 'Gmail',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'gmail',
  },
  {
    name: 'Sendgrid',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'sendgrid',
  },
  {
    name: 'WhatsApp',
    description: 'A property management platform for short-term rentals',
    type: 'pms',
    icon: 'i-lucide-search',
    route: 'whatsapp',
  },
]

// Function to check if an integration is active
function isIntegrationActive(name: string): boolean {
  if (!profile?.integrations)
    return false

  const lowerCaseName = name.toLowerCase()

  if (lowerCaseName === 'ownerrez') {
    // Check if calry exists and has any object with integrationDefinition.key === 'ownerrez'
    return !!(
      profile.integrations.calry
      && typeof profile.integrations.calry === 'object'
      && Object.values(profile.integrations.calry).some(item =>
        item.integrationDefinition?.key === 'ownerrez' && !item.deleted,
      )
    )
  }
  else {
    // Check other integrations directly by key
    return !!profile.integrations[lowerCaseName]
  }
}

async function handleManage(name: string) {
  switch (name) {
    case 'Guesty': {
      const instance = guestyModal.open()
      const result = await instance.result
      break
    }
    case 'OwnerRez': {
      const instance = ownerrezModal.open()
      const result = await instance.result
      break
    }
    case 'Hostaway': {
      // const instance = hostawayModal.open()
      // const result = await instance.result
      break
    }
    case 'AirBnB': {
      // const instance = airbnbModal.open()
      // const result = await instance.result
      break
    }
    case 'OpenPhone': {
      // const instance = openphoneModal.open()
      // const result = await instance.result
      break
    }
    case 'Slack': {
      // const instance = slackModal.open()
      // const result = await instance.result
      break
    }
    case 'Gmail': {
      // const instance = gmailModal.open()
      // const result = await instance.result
      break
    }
    case 'Sendgrid': {
      // const instance = sendgridModal.open()
      // const result = await instance.result
      break
    }
    case 'WhatsApp': {
      // const instance = whatsappModal.open()
      // const result = await instance.result
      break
    }
    default:
      break
  }
}
</script>

<template>
  <UDashboardPanel id="integrations">
    <template #header>
      <UDashboardNavbar title="Integrations">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <!-- ListingsAddModal can be added here in the future -->
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5">
        <UInput :model-value="search" class="max-w-sm" icon="i-lucide-search" placeholder="Search for Integrations" />
        <!-- <div class="flex flex-wrap items-center gap-1.5">
          <USelect v-model="statusFilter" :items="[
            { label: 'All', value: 'all' },
            { label: 'Active', value: 'active' },
            { label: 'Innactive', value: 'innactive' },
          ]" :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter status" class="min-w-40" />
          <USelect v-model="autopilotFilter" :items="[
            { label: 'All', value: 'all' },
            { label: 'On', value: 'on' },
            { label: 'Off', value: 'off' },
          ]" :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Autopilot" class="min-w-40" />
        </div> -->
      </div>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <IntegrationsCard v-for="integration in availableIntegrations" :key="integration.name" :name="integration.name"
          :description="integration.description" :type="integration.type" :icon="integration.icon"
          :is-active="isIntegrationActive(integration.name)" @manage="handleManage" />
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped></style>
