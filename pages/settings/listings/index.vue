<script lang="ts" setup>
import { useToast } from '#imports'
import { httpsCallable } from 'firebase/functions'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const { $functions } = useNuxtApp()

const toast = useToast()

const syncingListings = ref(false)
const syncingAmenities = ref(false)
const syncingContent = ref(false)
const syncingPhotos = ref(false)
const indexingListings = ref(false)
const overwriteAmenities = ref(false)

// Scaffolded functions with 2s delay
async function getAllNewListings() {
  syncingListings.value = true

  try {
    const getNewListingsCallable = httpsCallable($functions, 'http-call-listing-syncalllistings')
    const { data } = await getNewListingsCallable()
    console.log('finished getting new listings', data)
    toast.add({
      title: 'Synced New Listings',
      description: data.numNewListings === 0 ? 'No new listings' : `Successfully synced ${data.numNewListings} new listings`,
    })
  }
  catch (e) {
    console.error('Unable to get new listings', e)
    toast.add({
      title: 'Unable to get new listings',
      description: e.message,
      color: 'error',
    })
    throw e
  }
  finally {
    syncingListings.value = false
  }
}

async function syncAllAmenities() {
  syncingAmenities.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  syncingAmenities.value = false
  // TODO: actual logic
}

async function syncContentAllListings() {
  syncingContent.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  syncingContent.value = false
  // TODO: actual logic
}

async function syncAllPhotos() {
  syncingPhotos.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  syncingPhotos.value = false
  // TODO: actual logic
}

async function trainAllListings() {
  indexingListings.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  indexingListings.value = false
  // TODO: actual logic
}
</script>

<template>
  <UDashboardPanel id="settings-listings">
    <template #header>
      <UDashboardNavbar title="Global Listings Settings">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UCard>
        <template #header>
          <div>
            PMS Sync & Listings Operations
          </div>
        </template>
        <div class="flex flex-col gap-6">
          <!-- Sync All Listings -->
          <div class="flex items-start justify-between gap-4">
            <div class="flex-1">
              <h3 class="text-md mb-1">
                Get All New Listings
              </h3>
              <p class="text-sm ">
                <span class="text-red-500">Note:</span> This will pull all new listings from your PMS.
              </p>
            </div>
            <UButton
              icon="i-lucide-rotate-cw"
              variant="outline"
              color="primary"
              :loading="syncingListings"
              :disabled="syncingListings"
              @click="getAllNewListings"
            />
          </div>
          <!--          <USeparator /> -->
          <!--          <div class="flex items-start justify-between gap-4"> -->
          <!--            <div class="flex-1"> -->
          <!--              <h3 class="text-md mb-1"> -->
          <!--                Sync Amenities for All Listings -->
          <!--              </h3> -->
          <!--              <p class="text-sm "> -->
          <!--                <span class="text-red-500">Note:</span> This will sync the amenity status for each listing. This will only mark as available <b>new</b> amenities that have been found in your property management system. If you would like to override all amenity statuses in yada and reset them to the PMS amenities, please click the checkbox. -->
          <!--              </p> -->
          <!--              <div class="mt-2 flex items-center gap-2"> -->
          <!--                <UCheckbox id="overwrite-amenities" v-model="overwriteAmenities" /> -->
          <!--                <label for="overwrite-amenities" class="text-sm ">Override existing amenity statuses</label> -->
          <!--              </div> -->
          <!--            </div> -->
          <!--            <UButton -->
          <!--              icon="i-lucide-rotate-cw" -->
          <!--              variant="outline" -->
          <!--              color="primary" -->
          <!--              :loading="syncingAmenities" -->
          <!--              :disabled="syncingAmenities" -->
          <!--              @click="syncAllAmenities" -->
          <!--            /> -->
          <!--          </div> -->
          <!--          <USeparator /> -->
          <!--          <div class="flex items-start justify-between gap-4"> -->
          <!--            <div class="flex-1"> -->
          <!--              <h3 class="text-md mb-1"> -->
          <!--                Sync PMS Content for All Listings -->
          <!--              </h3> -->
          <!--              <p class="text-sm "> -->
          <!--                <span class="text-red-500">Note:</span> This will sync PMS content for all listings. This includes public fields, custom fields, and saved replies. -->
          <!--              </p> -->
          <!--            </div> -->
          <!--            <UButton -->
          <!--              icon="i-lucide-rotate-cw" -->
          <!--              variant="outline" -->
          <!--              color="primary" -->
          <!--              :loading="syncingContent" -->
          <!--              :disabled="syncingContent" -->
          <!--              @click="syncContentAllListings" -->
          <!--            /> -->
          <!--          </div> -->
          <!--          <USeparator /> -->
          <!--          <div class="flex items-start justify-between gap-4"> -->
          <!--            <div class="flex-1"> -->
          <!--              <h3 class="text-md mb-1"> -->
          <!--                Sync Photos for All Listings -->
          <!--              </h3> -->
          <!--              <p class="text-sm "> -->
          <!--                <span class="text-red-500">Note:</span> This will sync the photos for each listing. -->
          <!--              </p> -->
          <!--            </div> -->
          <!--            <UButton -->
          <!--              icon="i-lucide-rotate-cw" -->
          <!--              variant="outline" -->
          <!--              color="primary" -->
          <!--              :loading="syncingPhotos" -->
          <!--              :disabled="syncingPhotos" -->
          <!--              @click="syncAllPhotos" -->
          <!--            /> -->
          <!--          </div> -->
          <!--          <USeparator /> -->
          <!--          <div class="flex items-start justify-between gap-4"> -->
          <!--            <div class="flex-1"> -->
          <!--              <h3 class="text-md mb-1"> -->
          <!--                Train All Listings (Admin Only) -->
          <!--              </h3> -->
          <!--              <p class="text-sm mb-1"> -->
          <!--                <span class="text-red-500">Warning:</span> This will train all your listings and update the AI for all listings. -->
          <!--              </p> -->
          <!--              <p class="text-sm"> -->
          <!--                <span class="text-red-500">Warning:</span> We still recommend that you train your listings <b>one by one</b>. Training all your listings at once may result in an increased error rate during training because of server load. -->
          <!--              </p> -->
          <!--            </div> -->
          <!--            <UButton -->
          <!--              icon="i-lucide-rotate-cw" -->
          <!--              variant="outline" -->
          <!--              color="primary" -->
          <!--              :loading="indexingListings" -->
          <!--              :disabled="indexingListings" -->
          <!--              @click="trainAllListings" -->
          <!--            /> -->
          <!--          </div> -->
          <!--          <USeparator /> -->
          <!--          <div class="flex items-center justify-between gap-4"> -->
          <!--            <div class="flex-1"> -->
          <!--              <h3 class="text-md"> -->
          <!--                Listing Groups -->
          <!--              </h3> -->
          <!--            </div> -->
          <!--            <NuxtLink to="/listing-groups"> -->
          <!--              <UButton -->
          <!--                icon="i-lucide-chevron-right" -->
          <!--                variant="outline" -->
          <!--                color="primary" -->
          <!--                square -->
          <!--              /> -->
          <!--            </NuxtLink> -->
          <!--          </div> -->
        </div>
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
