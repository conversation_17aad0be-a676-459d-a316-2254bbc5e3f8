<script lang="ts" setup>
import { useWorkspaceStore } from '#imports'
import { ref } from 'vue'

const workspaceStore = useWorkspaceStore()
const isSaving = ref(false)

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

// Computed property for slider color based on time value
const sliderColor = computed(() => {
  const time = workspaceStore.workspace?.buyTime?.time || 5
  if (time <= 7)
    return 'success'
  if (time <= 10)
    return 'warning'
  return 'error'
})

async function handleSave() {
  isSaving.value = true
  const payload = { buyTime: {
    time: workspaceStore.workspace.buyTime.time,
    active: workspaceStore.workspace.buyTime.active,
    message: workspaceStore.workspace.buyTime.message,
    guidebook: workspaceStore.workspace.buyTime.guidebook,
  } }
  try {
    await useApiFetch('workspace', {
      method: 'PATCH',
      body: payload,
    })
  }
  catch (e) {
    console.error('Failed to save changes', e)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <UDashboardPanel id="settings-buytime">
    <template #header>
      <UDashboardNavbar title="Buy Time™">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton color="primary" :disabled="isSaving" :loading="isSaving" @click="handleSave">
            Save Changes
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UCard>
        <template #header>
          <div>Manage BuyTime Settings</div>
        </template>
        <div class="flex flex-col">
          <div class="bg-gray-100 dark:bg-gray-800 p-4 mb-4 rounded-lg">
            <p class="text-sm mb-2">
              There are cases in which the AI will not be able to respond when it doesn't have the right information,
              isn't sure enough in the response, or the guest message was out of bounds (complaint, money matters,
              etc.).
            </p>
            <p class="text-sm mb-2">
              This setting will send a generic message and the guidebook link and give you some time to get back
              to the guest while maintaining a 100% response rate and response times under 1 minute.
            </p>
            <p class="text-sm mb-0 font-semibold">
              This module can also work when AI Autorespond is <span class="text-primary">turned off</span>. Instead of trying to generate a
              response, BuyTime will just send them the deflection message.
            </p>
          </div>
        </div>
        <div class="flex flex-col mb-6">
          <div class="flex items-center mb-3">
            <USwitch
              v-model="workspaceStore.workspace.buyTime.active"
              class="mr-2"
            />
            <label class="font-medium">Enable Buy Time Module</label>
          </div>
          <UTextarea
            v-model="workspaceStore.workspace.buyTime.message"
            :rows="2"
            placeholder="Enter your Buy Time message here..."
            class="w-full"
          />
        </div>
        <div class="flex flex-col">
          <div class="flex items-center mb-3">
            <USwitch
              v-model="workspaceStore.workspace.buyTime.guidebook"
              class="mr-2"
            />
            <label class="font-medium">Include Guidebook Link</label>
          </div>
          <UAlert
            icon="i-heroicons-information-circle"
            color="primary"
            variant="soft"
            title="Preview"
            description="PS: Most of the information about this property can be found in this guidebook: {guidebookLink}"
            class="mb-2"
          />
        </div>
        <div class="flex flex-col">
          <div class="flex items-center justify-between mb-3">
            <span class="font-medium mr-3">Delay Duration</span>
            <span
              :class="{
                'text-green-500': workspaceStore.workspace.buyTime.time <= 7,
                'text-yellow-500': workspaceStore.workspace.buyTime.time > 7 && workspaceStore.workspace.buyTime.time <= 10,
                'text-red-500': workspaceStore.workspace.buyTime.time > 10,
              }"
              class="font-medium"
            >
              {{ workspaceStore.workspace.buyTime.time }} Minutes
            </span>
          </div>
          <USlider
            v-model="workspaceStore.workspace.buyTime.time"
            size="sm"
            :min="5"
            :max="15"
            :step="1"
            :color="sliderColor"
            class="mt-2"
          />
          <p class="text-sm text-gray-500 mt-2 mb-0 flex items-center">
            <UIcon name="i-lucide-info" class="mr-1" />
            Recommended: Keep the delay under 10 minutes for optimal response times
          </p>
        </div>
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>
</style>
