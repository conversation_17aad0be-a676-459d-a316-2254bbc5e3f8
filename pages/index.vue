<script lang="ts" setup>
import { DashboardAutopilot, DashboardContacts, DashboardGuidebook, DashboardRevenue, DashboardSmartSpaces, DashboardStatsCard, DashboardWebsite } from '#components'
import { useWorkspaceStore } from '#imports'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const workspaceStore = useWorkspaceStore()

const allTimeMetrics = computed(() => {
  const allTimes = workspaceStore?.workspace?.allTimes || {}
  return [
    {
      label: 'Website Views',
      amount: allTimes.numWebsiteViews || 0,
    },
    {
      label: 'Buy Times',
      amount: allTimes.numBuyTimes || 0,
    },
    {
      label: 'Guidebook Views',
      amount: allTimes.numGuidebookViews || 0,
    },
    {
      label: 'Tasks Created',
      amount: allTimes.numTasks || 0,
    },
    {
      label: 'Contacts Created',
      amount: allTimes.numContacts || 0,
    },
  ]
})
</script>

<template>
  <UDashboardPanel>
    <template #body>
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          All Time Statistics
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <DashboardStatsCard v-for="(metric, index) in allTimeMetrics" :key="index" :amount="metric.amount"
            :label="metric.label" />
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <DashboardGuidebook />
        <DashboardSmartSpaces />
        <DashboardContacts />
        <DashboardRevenue />
        <DashboardWebsite />
        <DashboardAutopilot />
      </div>
    </template>
  </UDashboardPanel>
</template>

<style></style>
