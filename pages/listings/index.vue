<script lang="ts" setup>
import type { TableColumn } from '@nuxt/ui'
import { useListingStore } from '#imports'
import { h, resolveComponent } from 'vue'
import { useRouter } from 'vue-router'

const listingStore = useListingStore()
const router = useRouter()

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const UAvatar = resolveComponent('UAvatar')

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: ({ row }) => {
      const url = row.getValue('thumbnail')
      return url ? h(UAvatar, { src: url, size: 'md', square: true }) : ''
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => h('span', { class: 'max-w-[200px] whitespace-normal break-words inline-block align-middle' }, `${row.getValue('name')}`),
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'address',
    header: 'Address',
    cell: ({ row }) => {
      const address = row.getValue('address')
      return h('span', { class: 'max-w-[200px] whitespace-normal break-words inline-block align-middle' }, address || 'No Address')
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'completionPercentage',
    header: 'Completion',
    cell: ({ row }) => {
      const value = row.getValue('completionPercentage')
      if (typeof value === 'number') {
        return h(resolveComponent('UProgress'), {
          modelValue: value,
          max: 100,
          class: 'max-w-[80px]',
        })
      }
      else {
        return 'View Listing to Refresh'
      }
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'active',
    header: 'Active',
    cell: ({ row }) => {
      const isActive = row.getValue('active')
      if (isActive) {
        return h(resolveComponent('UBadge'), {
          color: 'success',
          variant: 'subtle',
          class: 'capitalize',
        }, () => 'Active')
      }
      else {
        return h(resolveComponent('UButton'), {
          color: 'primary',
          size: 'xs',
          onClick: () => { /* TODO: implement activate logic */ },
        }, () => 'Activate')
      }
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'respondingActive',
    header: 'Autopilot',
    cell: ({ row }) => {
      const isActive = row.getValue('active')
      const respondingActive = row.getValue('respondingActive')
      const disabled = !isActive && !respondingActive
      return h(resolveComponent('USwitch'), {
        'modelValue': respondingActive,
        disabled,
        'onUpdate:modelValue': (val: boolean) => {
          listingStore.updateListing(row.original.id, {
            respondingActive: val,
          })
          row.original.respondingActive = val
        },
        'class': disabled ? 'opacity-50 cursor-not-allowed' : '',
      })
    },
    enableSorting: false,
    enableHiding: false,
  },
]

const table = useTemplateRef('table')
const globalFilter = ref('')
const statusFilter = ref()
const autopilotFilter = ref()

const filteredListings = computed(() => {
  return listingStore.listings.filter((listing) => {
    // Status filter
    let statusPass = true
    if (statusFilter.value && statusFilter.value !== 'all') {
      if (statusFilter.value === 'active')
        statusPass = listing.active === true
      else if (statusFilter.value === 'innactive')
        statusPass = listing.active !== true
    }
    // Autopilot filter
    let autopilotPass = true
    if (autopilotFilter.value && autopilotFilter.value !== 'all') {
      if (autopilotFilter.value === 'on')
        autopilotPass = listing.respondingActive === true
      else if (autopilotFilter.value === 'off')
        autopilotPass = listing.respondingActive !== true
    }
    return statusPass && autopilotPass
  })
})

function onSelect(row: any) {
  const id = row.original.id
  if (id) {
    router.push(`/listings/${id}`)
  }
}
</script>

<template>
  <UDashboardPanel id="listings">
    <template #header>
      <UDashboardNavbar title="Listings">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <!-- ListingsAddModal can be added here in the future -->
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5">
        <UInput v-model="globalFilter" class="max-w-sm" placeholder="Filter..." icon="i-lucide-search" />
        <div class="flex flex-wrap items-center gap-1.5">
          <USelect
            v-model="statusFilter" :items="[
              { label: 'All', value: 'all' },
              { label: 'Active', value: 'active' },
              { label: 'Innactive', value: 'innactive' },
            ]" :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter status" class="min-w-40"
          />
          <USelect
            v-model="autopilotFilter" :items="[
              { label: 'All', value: 'all' },
              { label: 'On', value: 'on' },
              { label: 'Off', value: 'off' },
            ]" :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Autopilot" class="min-w-40"
          />
        </div>
      </div>
      <UTable
        ref="table" v-model:global-filter="globalFilter" :data="filteredListings" :columns="columns"
        @select="onSelect"
      />
    </template>
  </UDashboardPanel>
</template>
