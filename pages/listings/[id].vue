<script lang="ts" setup>
import type { AccordionItem, TableColumn, TabsItem } from '@nuxt/ui'
import { FormsShare, ListingAddLink, ListingContentCard, ListingDemoBot } from '#components'
import { useListingStore, useWorkspaceStore } from '#imports'
import Editor from '@hugerte/hugerte-vue'
import { collection, query, where } from 'firebase/firestore'
import debounce from 'lodash/debounce'
import { computed, h, ref, resolveComponent, watch } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'

import { useRoute, useRouter } from 'vue-router'

import { useApi } from '~/composables/useApi'
import { listingTags, taskStatusTypes } from '~/shared/index'

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const db = useFirestore()
const toast = useToast()
const route = useRoute()
const router = useRouter()
const overlay = useOverlay()

const addLinkModal = overlay.create(ListingAddLink, {
})
const formsShareModal = overlay.create(FormsShare, {
  props: {
  },
})

const { getListing } = useApi()
const listing = ref<any>((await getListing(route.params.id as string)) as any)
const forms = useCollection(() =>
  workspaceStore.workspace
    ? query(
      collection(db, 'forms'),
      where('userId', '==', workspaceStore.workspace.id),
      where('listingId', '==', route.params.id),
    )
    : null,
)

const formsColumns: TableColumn<any>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => `${row.getValue('name')}`,
  },
  {
    header: 'Share',
    cell: ({ row }) => h(resolveComponent('UButton'), {
      icon: 'i-heroicons-share',
      size: 'xs',
      variant: 'ghost',
      onClick: async () => {
        const formShareModalInstance = formsShareModal.open()
        const result = await formShareModalInstance.result
        if (typeof result === 'string') {
          await shareForm(row.original.id, { email: result, listingId: route.params.id })
        }
      },
    }),
  },
  {
    header: 'Copy',
    cell: ({ row }) => h(resolveComponent('UButton'), {
      icon: 'i-heroicons-clipboard-document',
      size: 'xs',
      variant: 'ghost',
      onClick: () => handleCopy(`https://forms.yada.ai/${row.original.id}`), // Placeholder
    }),
  },
  // {
  //   header: 'Delete',
  //   cell: ({ row }) => h(resolveComponent('UButton'), {
  //     icon: 'i-heroicons-trash',
  //     size: 'xs',
  //     variant: 'ghost',
  //     color: 'red',
  //     // onClick: () => handleDelete(row.original.id) // Placeholder
  //   }),
  // },
]

const tasks = useCollection(() =>
  workspaceStore.workspace
    ? query(
      collection(db, 'tasks'),
      where('userId', '==', workspaceStore.workspace.id),
      where('listing.id', '==', route.params.id),
    )
    : null,
)

const sops = await useApiFetch('sops', {
  query: { listingId: route.params.id },
})

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const tabItems: TabsItem[] = [
  { label: 'Content', slot: 'content' as const },
  { label: 'Text Editor', slot: 'editor' as const },
  { label: 'Gallery', slot: 'gallery' as const },
  { label: 'Forms', slot: 'forms' as const },
  { label: 'Tasks', slot: 'tasks' as const },
  { label: 'SOPs', slot: 'sops' as const },
  { label: 'Settings', slot: 'settings' as const },
]

const pmsContentItems: AccordionItem[] = [
  {
    label: 'Public Content',
    icon: 'i-lucide-globe',
    slot: 'publicContent',
  },
  {
    label: 'Custom Fields',
    icon: 'i-lucide-braces',
    slot: 'customFields',
  },
  {
    label: 'Saved & Template Replies',
    icon: 'i-lucide-message-square-quote',
    slot: 'savedReplies',
  },
]

const activeTab = ref('0')

const UBadge = resolveComponent('UBadge')
const USelect = resolveComponent('USelect')

const statusItems = taskStatusTypes.map(({ label, value, variant }) => ({
  label,
  value,
  chip: { color: variant },
}))

function getStatusChip(value: string) {
  return statusItems.find(item => item.value === value)?.chip
}

// Prepare assignee items for the select (label: email, value: userId)
const assigneeItems = computed(() =>
  (workspaceStore.workspace?.users || []).map((u: any) => ({
    label: u.email,
    value: u.userId || u.id,
  })),
)

const taskColumns = [
  { accessorKey: 'title', header: 'Title' },
  { accessorKey: 'description', header: 'Description' },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      return h(USelect, {
        'modelValue': row.getValue('status'),
        'onUpdate:modelValue': (val: string) => {
          updateTask(row.original.id, { status: val })
        },
        'items': statusItems,
        'value-key': 'value',
        'class': 'w-32',
      }, {
        leading: ({ modelValue, ui }: any) =>
          modelValue
            ? h(resolveComponent('UChip'), {
              ...getStatusChip(modelValue),
              inset: true,
              standalone: true,
              size: ui?.itemLeadingChipSize?.(),
              class: ui?.itemLeadingChip?.(),
            })
            : null,
      })
    },
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      return h(UBadge, { label: row.getValue('priority').charAt(0).toUpperCase() + row.getValue('priority').slice(1), color: row.getValue('priority') === 'high' ? 'error' : row.getValue('priority') === 'medium' ? 'warning' : 'success' })
    },
  },
  {
    accessorKey: 'assignees',
    header: 'Assignees',
    cell: ({ row }) => {
      // Set modelValue to array of ids for initial state
      const value = Array.isArray(row.original.assignees)
        ? row.original.assignees.map((a: any) => a.id)
        : []
      return h(USelect, {
        'modelValue': value,
        'multiple': true,
        'items': assigneeItems.value,
        'onUpdate:modelValue': (val: string[]) => {
          const selected = assigneeItems.value.filter(i => val.includes(i.value)).map(i => ({ id: i.value, email: i.label }))
          updateTask(row.original.id, { assignees: selected })
        },
        'class': 'w-40',
      })
    },
  },
  { accessorKey: 'createdAt', header: 'Created At' },
]

const selectedTab = ref('content')

const isActivating = ref(false)
const isTraining = ref(false)
const isAutoGenerating = ref(false)
const isSyncing = ref(false)
const isDeactivating = ref(false)
const isArchiving = ref(false)

function handleOpenAddressDialog() {
  // Implement address dialog functionality
}

function copyGuidebookLink() {
  // Implement copy functionality
}

async function handleAutogenerate() {
  isAutoGenerating.value = true
  let res
  try {
    res = await useApiFetch(`listings/${route.params.id}/autogenerate`)
  }
  catch (e) {
    toast.add({
      title: 'Unable to autogenerate',
      description: e.message,
      color: 'error',
    })
  }
  finally {
    isAutoGenerating.value = false
  }
}

async function handleTrainListing() {
  isTraining.value = true
  // console.log(`Starting this function: ${isTraining.value}`)
  try {
    await useApiFetch(`listings/${route.params.id}/train`)
  }
  catch (e) {
    toast.add({
      title: 'Unable to train listing',
      description: e.message,
      color: 'error',
    })
  }
  finally {
    isTraining.value = false
  }
}

async function handleAddLink() {
  const addLinkModalInstance = addLinkModal.open()
  const result = await addLinkModalInstance.result
  if (!result)
    return
  listing.value.links = [...(listing.value.links ?? []), result]

  try {
    await updateListing({ links: listing.value.links })
    toast.add({
      title: 'Link added successfully',
      color: 'success',
    })
  }
  catch (e) {
    toast.add({
      title: 'Unable to add link',
      description: e.message,
      color: 'error',
    })
  }
}

const canActivate = ref({
  status: true,
  message: '',
})

function activateAi() {
  isActivating.value = true
  // Implement AI activation
}

async function handleDeactivateAi() {
  isDeactivating.value = true
  try {
    // Call API endpoint to deactivate AI
    await useApiFetch(`listings/${listing.value.id}`, {
      method: 'PATCH',
      body: {
        active: false,
        respondingActive: false,
      },
    })

    // Update local state to reflect changes
    listing.value.active = false
    listing.value.respondingActive = false

    toast.add({
      title: 'Listing AI deactivated',
      description: 'AI features have been turned off for this listing',
      color: 'success',
    })
  }
  catch (error) {
    toast.add({
      title: 'Error deactivating AI',
      description: error.message || 'An unexpected error occurred',
      color: 'error',
    })
  }
  finally {
    isDeactivating.value = false
  }
}

async function handleArchiveListing() {
  isArchiving.value = true
  try {
    // Call API endpoint to archive listing
    await useApiFetch(`listings/${listing.value.id}`, {
      method: 'PATCH',
      body: { archived: true },
    })

    toast.add({
      title: 'Listing archived',
      description: 'The listing has been archived successfully',
      color: 'success',
    })

    // Redirect to listings page after successful archiving
    router.push('/listings')
  }
  catch (error) {
    toast.add({
      title: 'Error archiving listing',
      description: error.message || 'An unexpected error occurred',
      color: 'error',
    })
  }
  finally {
    isArchiving.value = false
  }
}

function handleCopy(text) {
  navigator.clipboard
    .writeText(text)
    .then(() => { })
    .catch((error) => { })
  toast.add({
    title: 'Copied to Clipboard',
    description: 'Copied to clipboard',
    color: 'success',
  })
}

async function shareForm(id: string, payload = {}) {
  try {
    await useApiFetch(`forms/${id}/share`, {
      method: 'post',
      body: payload,
    })
    toast.add({
      title: 'Form shared',
      description: `Form shared successfully to ${payload.email}`,
      color: 'success',
    })
  }
  catch (e) {
    toast.add({
      title: 'Unable to update task',
      description: e.message,
      color: 'error',
    })
  }
}

async function updateTask(id, payload) {
  try {
    await useApiFetch(`tasks/${id}`, {
      method: 'PATCH',
      body: payload,
    })
  }
  catch (e) {
    toast.add({
      title: 'Unable to update task',
      description: e.message,
      color: 'error',
    })
  }
}

async function updateListing(payload) {
  try {
    await useApiFetch(`listings/${route.params.id}`, {
      method: 'PATCH',
      body: payload,
    })
  }
  catch (e) {
    toast.add({
      title: 'Unable to update task',
      description: e.message,
      color: 'error',
    })
  }
}

async function onCardChanged({ id, payload }) {
  const idx = listing.value.content.findIndex(c => c.id === id)
  if (idx !== -1) {
    // Update the local listing first
    Object.keys(payload).forEach((key) => {
      listing.value.content[idx][key] = payload[key]
    })

    try {
      // Create a structured payload with both the update and the entire card
      const structuredPayload = {
        update: payload, // Original update payload
        card: listing.value.content[idx], // The entire card after local update
      }

      // Make the API call with the structured payload
      await useApiFetch(`listings/${route.params.id}/content-cards/${id}`, {
        method: 'PATCH',
        body: structuredPayload,
      })
    }
    catch (e) {
      toast.add({
        title: 'Unable to update content card',
        description: e.message,
        color: 'error',
      })
    }
  }
}

async function onCardArchived(id) {
  const idx = listing.value.content.findIndex(c => c.id === id)
  if (idx !== -1) {
    // Optimistically remove from UI
    listing.value.content.splice(idx, 1)
    try {
      // Update backend
      await useApiFetch(`listings/${route.params.id}/content-cards/${id}`, {
        method: 'PATCH',
        body: { archived: true },
      })
    }
    catch (e) {
      // Rollback UI change if API call fails
      // Note: This simple rollback might not preserve original order if multiple operations happen quickly.
      // Consider fetching the card data again or a more robust state management for complex scenarios.
      // listing.value.content.splice(idx, 0, originalCard); // Need originalCard reference
      toast.add({
        title: 'Unable to archive content card',
        description: e.message,
        color: 'error',
      })
    }
  }
}

watch(
  () => listing.value?.kb,
  debounce((newVal) => {
    updateListing({ kb: newVal })
  }, 1250),
)

const otaContent = computed(() =>
  (listing.value.content || []).filter(c => c.integration?.type === 'publicDescription' && !c.archived),
)

const customFieldContent = computed(() =>
  (listing.value.content || []).filter(c => c.integration?.type === 'customField' && !c.archived),
)

const savedReplyContent = computed(() =>
  (listing.value.content || []).filter(c => c.integration?.type === 'savedReply' && !c.archived),
)

// --- Filtering and Grouping Logic ---

// Refs for filters (implement UI later)
const search = ref('')
const filterStatus = ref([]) // e.g., ['Complete', 'Incomplete', 'Unknown']
const filterImportance = ref(['High']) // e.g., ['High', 'Medium', 'Low']
const filterAmenityStatus = ref('all') // Default to 'all'

// Options for filters
const statusOptions = ['Complete', 'Incomplete', 'Unknown']
const importanceOptions = ['High', 'Medium', 'Low'] // Corresponds to keys in importanceMapping
const amenityStatusOptions = [
  { label: 'All', value: 'all' },
  { label: 'Available', value: 'available' },
  { label: 'Unavailable', value: 'unavailable' },
]

// Placeholder for importance mapping if needed (adjust based on actual priority values)
const importanceMapping = {
  High: [1, 2], // Updated mapping
  Medium: [3], // Updated mapping
  Low: [4, 5], // Updated mapping
}

// Processed cards including filtering, grouping, and sorting
const groupedAndSortedCards = computed(() => {
  const excludedTypes = ['publicDescription', 'savedReply', 'customField', 'templateMessage']
  let tempContent = (listing.value.content || []).filter(card =>
    !card.archived
    && (!card.integration || !excludedTypes.includes(card.integration.type)),
  )

  // 1. Apply Search Filter (implement Fuse logic if needed)
  if (search.value.length > 1) {
    // Placeholder: Implement Fuse.js search if required
    // tempContent = ... apply search ...
    tempContent = tempContent.filter(card =>
      card.title?.toLowerCase().includes(search.value.toLowerCase())
      || card.text?.toLowerCase().includes(search.value.toLowerCase()),
    )
  }

  // 2. Apply Status, Importance, Amenity Filters (using logic from your example)
  const filterFunction = (item) => {
    return (
      (filterStatus.value.length === 0
        || filterStatus.value.some((status) => {
          // Actual status filtering logic based on your example
          if (status === 'Unknown') {
            // Assuming 'amenityKnown' exists on the item
            return item.template === 'amenity' && item.amenityKnown !== true
          }
          else if (status === 'Complete') {
            return item.text && item.text.length > 0
          }
          else if (status === 'Incomplete') {
            // Assuming 'oldText' exists. Adjust if property names differ.
            if (!item.oldText || item.oldText.length === 0) {
              if (item.amenityKnown === true || item.amenityKnown === undefined) {
                return true
              }
            }
            return false
          }
          return false // Default if status doesn't match known values
        }))
      && (filterImportance.value.length === 0
        || filterImportance.value.some((importance) => {
          // Actual importance filtering logic
          const priorityValue = importanceMapping[importance]
          if (priorityValue) {
            // Assuming 'priority' exists on the item
            if (Array.isArray(priorityValue)) { // Handle if mapping could be array (unlikely based on current map)
              return priorityValue.includes(item.priority)
            }
            return item.priority === priorityValue
          }
          return false // Importance value not found in mapping
        }))
      && (filterAmenityStatus.value === 'all' // Check against the ref value
        // Actual amenity status filtering
        || (filterAmenityStatus.value === 'available' && item.amenityAvailable === true) // Assuming 'amenityAvailable' exists
        || (filterAmenityStatus.value === 'unavailable' && item.amenityAvailable === false)
      ) // Closing parenthesis for the main boolean expression
    )
  }

  tempContent = tempContent.filter(filterFunction)

  // 3. Grouping
  const groups = {}
  listingTags.forEach((tag) => {
    groups[tag.id] = {
      name: tag.name,
      priority: tag.priority ?? 999, // Use nullish coalescing for default priority
      items: [],
    }
  })
  groups.misc = { name: 'Miscellaneous', priority: 1000, items: [] } // Ensure misc group with high priority

  tempContent.forEach((card) => {
    const firstTagId = card.tags && card.tags.length > 0 ? card.tags[0] : 'misc'
    const groupKey = groups[firstTagId] ? firstTagId : 'misc' // Fallback to misc if tag ID not found
    groups[groupKey].items.push(card)
  })

  // 4. Sort groups and sort items within groups
  const sortedGroups = Object.values(groups)
    .filter(group => group.items.length > 0)
    .sort((a, b) => a.priority - b.priority)

  // Sort items within each group
  sortedGroups.forEach((group) => {
    group.items.sort((a, b) => {
      const templateOrder = { faq: 0, customFaq: 1, amenity: 2 }
      const aOrder = templateOrder[a.template] ?? 999
      const bOrder = templateOrder[b.template] ?? 999
      if (aOrder !== bOrder)
        return aOrder - bOrder
      if (a.template === 'amenity' && b.template === 'amenity') {
        const aPriority = a.priority ?? 999
        const bPriority = b.priority ?? 999
        if (aPriority !== bPriority)
          return aPriority - bPriority
      }
      return (a.title || '').localeCompare(b.title || '')
    })
  })

  return sortedGroups // Return the array of groups, not flattened
})

function handleCreateSop() {
  router.push({
    path: '/sops/create',
    query: { listingId: route.params.id },
  })
}
function handleViewSop(sopId) {
  router.push(`/sops/${sopId}`)
}
function handleGalleryOrderUpdate() {
  updateListing({ pictures: listing.value.pictures })
}

function handleSetThumbnail(image) {
  listing.value.thumbnail = image.src
  updateListing({ thumbnail: image.src })
}

function handleSetFloorPlan(image) {
  listing.value.pictures = listing.value.pictures.map(p => ({
    ...p,
    floorPlan: p.src === image.src,
  }))
  updateListing({ pictures: listing.value.pictures })
  toast.add({ title: 'Floor plan updated.', color: 'success' })
}

function handleDeleteImage(image) {
  listing.value.pictures = listing.value.pictures.filter(p => p.src !== image.src)
  updateListing({ pictures: listing.value.pictures }) // Consider if needed immediately
  toast.add({ title: 'Image deleted.', color: 'info' })
}

function generateImageDropdownItems(image) {
  return [
    {
      label: 'Set as Thumbnail',
      icon: 'i-lucide-images',
      onSelect: () => {
        handleSetThumbnail(image)
      },
    },
    {
      label: 'Set as Floor Plan',
      icon: 'i-lucide-map',
      onSelect: () => {
        handleSetFloorPlan(image)
      },
    },
    {
      label: 'Delete',
      icon: 'i-lucide-trash',
      onSelect: () => {
        handleDeleteImage(image)
      },
    },
  ]
}

function handleImageUpload(event) {
  const file = event.target.files[0]
  if (!file)
    return

  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    toast.add({ title: 'Invalid file type', description: 'Please select a JPG, PNG, or WEBP image.', color: 'error' })
    return
  }

  // --- Optimistic Update ---
  const reader = new FileReader()
  reader.onload = (e) => {
    const newImage = {
      id: `temp-${Date.now()}`,
      src: e.target.result,
      // Add other potential default properties if needed
      floorPlan: false,
    }
    listing.value.pictures.push(newImage)
  }
  reader.readAsDataURL(file)
  // --- End Optimistic Update ---

  // --- Upload Logic ---
  listingStore.uploadImage(route.params.id, file)
  // Reset the file input after selection (allows selecting the same file again)
  event.target.value = null
  // --- End Upload Logic ---
}

function triggerFileInput() {
  document.getElementById('imageUploadInput')?.click()
}

async function handleSyncPms() {
  isSyncing.value = true
  try {
    await useApiFetch(`listings/${route.params.id}/sync`)
    toast.add({
      title: 'Sync started',
      description: 'PMS sync has started.',
      color: 'success',
    })
    // Reload the listing after sync
    try {
      const updated = await getListing(route.params.id)
      listing.value = updated
    }
    catch (reloadErr) {
      toast.add({
        title: 'Unable to reload listing',
        description: reloadErr.message,
        color: 'error',
      })
    }
  }
  catch (e) {
    toast.add({
      title: 'Unable to sync with PMS',
      description: e.message,
      color: 'error',
    })
  }
  finally {
    isSyncing.value = false
  }
}
</script>

<template>
  <UDashboardPanel :default-size="20">
    <UDashboardNavbar title="Back to Listings">
      <template #leading>
        <UDashboardSidebarCollapse />
      </template>
    </UDashboardNavbar>
    <div class="flex flex-col h-[calc(100vh-64px)]">
      <div class="flex-1 overflow-y-auto">
        <UCard class="m-2 p-0">
          <div class="relative mb-2" style="padding-top: 66.67%">
            <NuxtImg :src="listing?.thumbnail || 'https://via.placeholder.com/150'" alt="Listing Cover"
              class="absolute inset-0 w-full h-full object-cover rounded-lg" />
          </div>

          <h2 class="text-l font-bold mb-1">
            {{ listing?.name || 'Listing Name' }}
          </h2>

          <div class="flex items-center gap-1">
            <p class="text-sm text-gray-500">
              {{ listing.v === 4 ? `${listing.address.line1} ${listing.address.city}` : listing?.address?.fullAddress }}
            </p>
            <UButton icon="i-heroicons-pencil" variant="ghost" size="xs" class="rounded-full"
              @click="handleOpenAddressDialog" />
          </div>

          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium">Autopilot</span>
            <USwitch v-model="listing.respondingActive"
              :disabled="listing.respondingActive === false && !listing.active" />
          </div>

          <div class="flex items-center justify-between mb-2">
            <div class="flex flex-col">
              <span class="text-sm font-medium">Completion</span>
              <span class="text-sm text-gray-500">{{ listing.completionPercentage }}%</span>
            </div>
            <UProgress v-model="listing.completionPercentage" class="w-16" />
          </div>

          <div class="flex items-center justify-between mb-2">
            <template v-if="listing.active">
              <div class="text-center text-green-600 font-medium">
                Listing Active
              </div>
            </template>
            <template v-else>
              <UButton block :disabled="!canActivate.status || isActivating" :loading="isActivating"
                @click="activateAi()">
                {{ isActivating ? 'Activating...' : 'Activate Listing' }}
              </UButton>
            </template>
          </div>

          <USeparator class="mb-2" />

          <div>
            <h3 class="text-sm font-medium mb-2">
              Smart Guidebook
            </h3>
            <div class="flex flex-col space-y-2">
              <ULink :to="`https://guide.yada.ai/${listing.id}`" target="_blank" class="text-sm">
                Preview Guidebook
              </ULink>
              <ULink href="#" class="text-sm" @click.prevent="copyGuidebookLink">
                Copy Guidebook Link
              </ULink>
            </div>
          </div>

          <USeparator class="my-2" />

          <div class="mt-2 w-full">
            <UButton block variant="soft" :loading="isAutoGenerating" class="mb-2" size="sm" @click="handleAutogenerate">
              {{ isAutoGenerating ? 'Autogenerating...' : 'Autogenerate Content' }}
            </UButton>

            <UButton block variant="soft" :loading="isTraining" size="sm" @click="handleTrainListing">
              {{ isTraining ? 'Training...' : 'Train AI' }}
            </UButton>

            <div class="mt-2 text-sm text-center">
              <span v-if="listing?.lastIndexed" class="text-gray-500">
                Last trained {{ new Date(listing.lastIndexed).toLocaleString() }}
              </span>
              <span v-else class="text-orange-500">
                Never Trained
              </span>
            </div>
          </div>
        </UCard>
        <ListingDemoBot :listing-id="route.params.id" />
      </div>
    </div>
  </UDashboardPanel>

  <UDashboardPanel>
    <div class="px-4 py-6">
      <UTabs v-model="activeTab" :items="tabItems" size="md" variant="link">
        <template #content="{ item }">
          <div class="overflow-y-auto max-h-[90vh]">
            <!-- Filter Row -->
            <div
              class="flex items-center gap-4 p-4 border-b border-gray-200 dark:border-gray-700 mb-4 sticky top-0 z-20 bg-white dark:bg-gray-800">
              <UInput v-model="search" placeholder="Search content..." icon="i-heroicons-magnifying-glass-20-solid"
                class="flex-1" />
              <USelect v-model="filterStatus" :items="statusOptions" multiple placeholder="Status" class="w-40" />
              <USelect v-model="filterImportance" :items="importanceOptions" multiple placeholder="Importance"
                class="w-40" />
              <USelect v-model="filterAmenityStatus" :items="amenityStatusOptions" placeholder="Amenity Status"
                class="w-48" />
            </div>

            <UAccordion :items="pmsContentItems">
              <template #publicContent>
                <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-3 gap-4 m-2">
                  <ListingContentCard v-for="card in otaContent" :key="card.id" :card="card" @card-changed="onCardChanged"
                    @archive-card="onCardArchived" />
                </div>
              </template>
              <template #customFields>
                <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-3 gap-4 m-2">
                  <ListingContentCard v-for="card in customFieldContent" :key="card.id" :card="card"
                    @card-changed="onCardChanged" @archive-card="onCardArchived" />
                </div>
              </template>
              <template #savedReplies>
                <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-3 gap-4 m-2">
                  <ListingContentCard v-for="card in savedReplyContent" :key="card.id" :card="card"
                    @card-changed="onCardChanged" @archive-card="onCardArchived" />
                </div>
              </template>
            </UAccordion>

            <div v-if="groupedAndSortedCards.length > 0" class="mt-6">
              <div v-for="group in groupedAndSortedCards" :key="group.name">
                <h3 class="text-lg font-semibold my-4 px-2 sticky top-16 bg-white dark:bg-gray-900 py-2 z-10">
                  {{ group.name }}
                </h3>
                <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-3 gap-4 m-2">
                  <ListingContentCard v-for="card in group.items" :key="card.id" :card="card"
                    @card-changed="onCardChanged" @archive-card="onCardArchived" />
                </div>
              </div>
            </div>
            <div
              v-else-if="search.length > 1 || filterStatus.length > 0 || filterImportance.length > 0 || filterAmenityStatus.value"
              class="mt-6 text-center text-gray-500">
              No content cards match your filters.
            </div>
          </div>
        </template>
        <template #editor="{ item }">
          <Editor v-model="listing.kb" class="h-[90vh]" :init="{
            plugins: 'lists link image table code help wordcount',
          }" />
        </template>
        <template #gallery="{ item }">
          <div class="h-[90vh] overflow-y-auto">
            <!-- Add Upload Button Row -->
            <div
              class="sticky top-0 z-10 flex justify-end p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <UButton label="Upload Image" icon="i-lucide-upload" size="sm" @click="triggerFileInput" />
              <input id="imageUploadInput" type="file" hidden accept="image/jpeg,image/png,image/webp"
                @change="handleImageUpload">
            </div>
            <!-- Existing VueDraggable -->
            <VueDraggable v-model="listing.pictures" :animation="150" ghost-class="ghost"
              class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-2 p-2" @update="handleGalleryOrderUpdate">
              <div v-for="item in listing.pictures" :key="item.id" class="relative group">
                <NuxtImg :src="item.src" class="w-full h-auto object-cover rounded aspect-video" />
                <div class="absolute top-1 right-1 opacity-500 group-hover:opacity-100 transition-opacity">
                  <UDropdownMenu :items="generateImageDropdownItems(item)">
                    <UButton color="neutral" variant="solid" icon="i-heroicons-ellipsis-vertical" size="xs"
                      class="rounded-full shadow-md" />
                  </UDropdownMenu>
                </div>
              </div>
            </VueDraggable>
          </div>
        </template>
        <template #forms="{ item }">
          <UTable :columns="formsColumns" :data="forms" class="flex-1" />
        </template>
        <template #tasks>
          <UTable :columns="taskColumns" :data="(tasks || []).map((task: any) => ({
            ...task,
            displayAssignees: Array.isArray(task.assignees) ? task.assignees.map((a: any) => a.email).join(', ') : '',
            createdAt: task.createdAt ? new Date(task.createdAt).toLocaleString() : '',
            id: task.id,
          }))" class="w-full" />
        </template>
        <template #sops>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <UButton label="Create SOP" icon="i-lucide-plus" color="primary" size="sm" @click="handleCreateSop" />
            </div>
            <div v-if="!sops || sops?.length === 0" class="text-center text-gray-500">
              No SOPs available for this listing
            </div>
            <div v-else class="space-y-2">
              <UCard v-for="sop in sops" :key="sop.id" class="cursor-pointer hover:shadow-md transition-shadow"
                @click="handleViewSop(sop.id)">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h3 class="text-xl font-semibold">
                      {{ sop.title }}
                    </h3>
                    <div class="flex gap-2">
                      <UIcon v-if="sop.images?.length" name="i-lucide-image" />
                      <UIcon v-if="sop.videos?.length" name="i-lucide-video" />
                    </div>
                  </div>
                </template>
                <div>
                  <p class="text-gray-600 mb-4">
                    {{ sop.description }}
                  </p>
                  <div class="text-sm text-gray-500">
                    <p>
                      Updated: {{ sop.updatedAt ? new Date(sop.updatedAt?.toDate ? sop.updatedAt.toDate()
                        : sop.updatedAt).toLocaleString() : '' }}
                    </p>
                    <p>
                      Created: {{ sop.createdAt ? new Date(sop.createdAt?.toDate ? sop.createdAt.toDate()
                        : sop.createdAt).toLocaleString() : '' }}
                    </p>
                  </div>
                  <div class="mt-2">
                    <span class="text-sm text-gray-600">
                      {{ sop.applyToAllListings ? 'Applied to all listings' : 'Applied to selected listings' }}
                    </span>
                  </div>
                </div>
              </UCard>
            </div>
          </div>
        </template>
        <template #settings="{ item }">
          <div class="overflow-y-auto max-h-[90vh]">
            <div class="grid grid-cols-1 gap-4 p-2">
              <UCard>
                <template #header>
                  <div class="text-lg font-semibold">
                    Autogenerate Content
                  </div>
                </template>
                <p class="text-sm text-gray-500 mb-4">
                  Use AI to automatically generate descriptions, replies, and other content based on your listing details.
                </p>
                <template #footer>
                  <UButton :loading="isAutoGenerating" :disabled="isAutoGenerating" @click="handleAutogenerate">
                    {{ isAutoGenerating ? 'Generating...' : 'Autogenerate Content' }}
                  </UButton>
                </template>
              </UCard>
              <UCard>
                <template #header>
                  <div class="text-lg font-semibold">
                    Sync with PMS
                  </div>
                </template>
                <p class="text-sm text-gray-500 mb-4">
                  This function syncs the listing content with the PMS.
                </p>
                <template #footer>
                  <UButton :loading="isSyncing" :disabled="isSyncing" @click="handleSyncPms">
                    {{ isSyncing ? 'Syncing...' : 'Sync with PMS' }}
                  </UButton>
                </template>
              </UCard>
              <UCard>
                <template #header>
                  <div class="text-lg font-semibold">
                    Websites and Links
                  </div>
                </template>
                <p class="text-sm text-gray-500 mb-4">
                  Add and manage external links related to this listing.
                </p>
                <div>
                  <!-- Links List -->
                  <div v-if="!listing.links || listing.links.length === 0" class="text-center text-gray-500 py-4">
                    No links added yet
                  </div>
                  <div v-else class="mb-4">
                    <div v-for="(link, index) in listing.links" :key="index"
                      class="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700">
                      <div class="flex-1">
                        <div class="font-medium">
                          {{ link.title }}
                        </div>
                        <div class="text-sm text-gray-500 truncate">
                          <a :href="link.url" target="_blank" class="hover:underline">{{ link.url }}</a>
                        </div>
                        <div v-if="link.description" class="text-sm text-gray-500 mt-1">
                          {{ link.description }}
                        </div>
                      </div>
                      <UButton icon="i-lucide-external-link" size="xs" variant="ghost" class="ml-2"
                        @click="window.open(link.url, '_blank')" />
                    </div>
                  </div>
                </div>
                <template #footer>
                  <UButton icon="i-lucide-plus" label="Add New Link" @click="handleAddLink()" />
                </template>
              </UCard>
              <UCard v-if="workspaceStore?.profile?.powerups?.slack && workspaceStore?.profile?.type === 'admin'">
                <template #header>
                  <div class="text-lg font-semibold">
                    Connect this Listing to a Slack Channel
                  </div>
                </template>
                <p class="text-sm text-gray-500 mb-4">
                  Please refer to the documentation
                  <a href="https://docs.yada.ai/powerups/slack/connecting-a-listing-with-a-specific-slack-channel"
                    target="_blank" class="text-primary hover:underline">here</a>
                </p>
                <!-- <div>{{ workspaceStore?.workspace?.powerups?.slack?.channels }}</div> -->
                <USelect v-model="listing.slackChannel" :items="workspaceStore?.workspace?.powerups?.slack?.channels"
                  placeholder="Select a Slack channel" class="w-full" label-key="name" value-key="name"
                  @update:model-value="(value: string) => updateListing({ slackChannel: value })" />
              </UCard>
              <UCard>
                <template #header>
                  <div class="text-lg font-semibold">
                    {{ listing.active ? 'Deactivate AI on this Listing' : 'Delete this Listing' }}
                  </div>
                </template>
                <template #footer>
                  <UButton :label="listing.active ? 'Deactivate Listing' : 'Delete Listing'" color="error"
                    :loading="listing.active ? isDeactivating : isArchiving"
                    :disabled="listing.active ? isDeactivating : isArchiving"
                    @click="listing.active ? updateListing({ active: false }) : updateListing({ archived: true })" />
                </template>
              </UCard>
            </div>
          </div>
        </template>
      </UTabs>
    </div>
  </UDashboardPanel>
</template>

<style></style>
