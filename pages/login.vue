<script setup lang="ts">
import type { FormSubmitEvent } from '@nuxt/ui'
import { signInWithEmailAndPassword } from 'firebase/auth'
import * as z from 'zod'

definePageMeta({
  middleware: 'authenticated',
  layout: 'auth',
})

const auth = useFirebaseAuth()
const toast = useToast()

useSeoMeta({
  title: 'Login',
})

const fields = [{
  name: 'email',
  type: 'text' as const,
  label: 'Email',
  placeholder: 'Enter your email',
  required: true,
}, {
  name: 'password',
  label: 'Password',
  type: 'password' as const,
  placeholder: 'Enter your password',
}, {
  name: 'remember',
  label: 'Remember me',
  type: 'checkbox' as const,
}]

const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(1, 'Must be at least 1 character'),
})

type Schema = z.output<typeof schema>

async function onSubmit(payload: FormSubmitEvent<Schema>) {
  try {
    await signInWithEmailAndPassword(auth!, payload.data.email, payload.data.password)
    try {
      const _hsq = window._hsq = window._hsq || []
      _hsq.push(['identify', {
        email: payload.data.email,
      }])
    }
    catch (e) {
      console.error('unable to identify user (login)')
    }
    const hubspotutk = useCookie('hubspotutk')
    useFetch('api/hubspot/form', {
      method: 'POST',
      body: {
        formId: '47dfce8a-5d53-457e-9f1c-e8c5055f7e89',
        payload: {
          email: payload.data.email,
        },
        hutk: hubspotutk.value,
      },
    }).catch((e) => {
      console.error('Error submitting HubSpot form:', e)
    })
    return await navigateTo('/')
  }
  catch (e: any) {
    console.error(e)
    toast.add({
      title: 'Login Failed',
      description: e.message,
      color: 'error',
    })
  }
}
</script>

<template>
  <UAuthForm :fields="fields" :schema="schema" title="Welcome back" icon="i-lucide-lock" @submit="onSubmit">
    <template #description>
      Don't have an account? <ULink to="/signup" class="text-(--ui-primary) font-medium">
        Sign up
      </ULink>.
    </template>

    <template #password-hint>
      <ULink to="/" class="text-(--ui-primary) font-medium" tabindex="-1">
        Forgot password?
      </ULink>
    </template>

    <template #footer>
      By signing in, you agree to our <ULink to="/" class="text-(--ui-primary) font-medium">
        Terms of Service
      </ULink>.
    </template>
  </UAuthForm>
</template>
