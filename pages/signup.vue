<script setup lang="ts">
import type { FormSubmitEvent } from '@nuxt/ui'
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth'
import * as z from 'zod'

definePageMeta({
  middleware: 'authenticated',
  layout: 'auth',
})

const auth = useFirebaseAuth()
const toast = useToast()

useSeoMeta({
  title: 'Sign up',
})

const fields = [{
  name: 'name',
  type: 'text' as const,
  label: 'Name',
  placeholder: 'Enter your name',
}, {
  name: 'email',
  type: 'text' as const,
  label: 'Email',
  placeholder: 'Enter your email',
}, {
  name: 'password',
  label: 'Password',
  type: 'password' as const,
  placeholder: 'Enter your password',
}, {
  name: 'confirmPassword',
  label: 'Confirm Password',
  type: 'password' as const,
  placeholder: 'Confirm your password',
}]

const schema = z.object({
  name: z.string().optional(),
  email: z.string().email('Invalid email'),
  password: z.string().min(8, 'Must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Must be at least 8 characters'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords must match',
  path: ['confirmPassword'],
})

type Schema = z.output<typeof schema>

async function onSubmit(payload: FormSubmitEvent<Schema>) {
  try {
    const { user } = await createUserWithEmailAndPassword(auth!, payload.data.email, payload.data.password)
    await updateProfile(user, { displayName: payload.data.name })
    try {
      let _hsq = window._hsq = window._hsq || []
      _hsq.push(['identify', {
        email: payload.data.email,
      }])
    }
    catch (e) {
      console.error('unable to identify user (signup)')
    }
    const hubspotutk = useCookie('hubspotutk')
    useFetch('api/hubspot/form', {
      method: 'POST',
      body: {
        formId: 'e3d2e85e-a845-478c-a9ad-f53d5fe983f4',
        payload: {
          email: payload.data.email,
        },
        hutk: hubspotutk.value,
      },
    }).catch((e) => {
      console.error('Error submitting HubSpot form:', e)
    })
  }
  catch (e: any) {
    toast.add({
      title: 'Signup Failed',
      description: e.message,
      color: 'error',
    })
  }
}
</script>

<template>
  <UAuthForm :fields="fields" :schema="schema" title="Create an account" :submit="{ label: 'Create account' }"
    @submit="onSubmit">
    <template #description>
      Already have an account? <ULink to="/login" class="text-(--ui-primary) font-medium">
        Login
      </ULink>.
    </template>

    <template #footer>
      By signing up, you agree to our <ULink to="/" class="text-(--ui-primary) font-medium">
        Terms of Service
      </ULink> and our <ULink to="/" class="text-(--ui-primary) font-medium">
        Privacy Policy
      </ULink>.
    </template>
  </UAuthForm>
</template>
