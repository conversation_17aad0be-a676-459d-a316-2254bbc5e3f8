<script lang="ts" setup>
import type { DropdownMenuItem } from '@nuxt/ui'
import { TasksKanbanBoard, TasksNew, TasksTable, TasksView } from '#components'
import { useListingStore, useWorkspaceStore } from '#imports'
import Fuse from 'fuse.js'
import { computed, ref } from 'vue'
import { taskStatusTypes } from '@/shared/webapp'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const { getTasks, updateTask } = useApi()
const tasks = ref(await getTasks())
const overlay = useOverlay()

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const newTaskModal = overlay.create(TasksNew, {
})

const selectedTask = ref(null)

// Store selected task IDs
const selectedTaskIds = ref<string[]>([])

const smartViewItems = ref<DropdownMenuItem[]>([
  {
    label: 'AI Generated Tasks',
    value: 'ai',
    icon: 'i-lucide-sparkles',
  },
])

const selectedSmartView = ref<string | null>(null)

const viewMode = ref('list') // 'list' or 'column' view mode
const showDone = ref(true)
const globalFilter = ref('')
const assigneesFilter = ref([])
const statusFilter = ref('all')
const listingFilter = ref('all')

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

const listingItems = computed(() => [
  { label: 'All Listings', value: 'all' },
  ...listingStore.listings.map((l: any) => ({ label: l.name, value: l.id })),
])

const statusItems = computed(() => [
  { label: 'All Statuses', value: 'all' },
  ...taskStatusTypes.map((s: any) => ({ label: s.label, value: s.value })),
])

const filteredTasks = computed(() => {
  let filtered = tasks.value

  // Smart View: AI Generated Tasks
  if (selectedSmartView.value === 'ai') {
    filtered = filtered.filter((task: any) => task.type === 'automated')
  }

  // Filter by assignees
  if (assigneesFilter.value && assigneesFilter.value.length > 0) {
    filtered = filtered.filter((task: any) => {
      if (!task.assignees || task.assignees.length === 0)
        return false
      const assigneeIds = task.assignees.map((a: any) => a.userId || a.id)
      return assigneesFilter.value.some((selectedId: string) => assigneeIds.includes(selectedId))
    })
  }

  // Filter by status
  if (statusFilter.value && statusFilter.value !== 'all') {
    filtered = filtered.filter((task: any) => task.status === statusFilter.value)
  }

  // Filter by listing
  if (listingFilter.value && listingFilter.value !== 'all') {
    filtered = filtered.filter((task: any) => task.listing && task.listing.id === listingFilter.value)
  }

  // Fuzzy search by title/description
  if (globalFilter.value && globalFilter.value.trim().length > 0) {
    const fuse = new Fuse(filtered, {
      keys: ['title', 'description'],
      threshold: 0.2,
      ignoreLocation: true,
    })
    const results = fuse.search(globalFilter.value.trim())
    filtered = results.map((result: any) => result.item)
  }

  return filtered
})

function onSelectionChange(ids: string[]) {
  selectedTaskIds.value = ids
}

const assigneeItems = computed(() =>
  (workspaceStore.workspace?.users || []).map((u: any) => ({
    label: u.email,
    value: u.userId || u.id,
  })),
)

// Optimistic deletion
async function deleteSelectedTasks() {
  if (!selectedTaskIds.value.length)
    return
  // Remove from UI optimistically
  const idsToDelete = [...selectedTaskIds.value]
  tasks.value = tasks.value.filter((t: any) => !idsToDelete.includes(t.id))
  selectedTaskIds.value = []

  // Call server endpoint for each deleted task
  for (const id of idsToDelete) {
    try {
      await useApiFetch(`tasks/${id}`, { method: 'DELETE' })
    }
    catch (err) {
      // Optionally handle error (e.g., show notification, retry, etc.)
      console.error(`Failed to delete task ${id}:`, err)
    }
  }
}

async function updateTaskStatus({ id, status }: { id: string, status: string }) {
  const idx = tasks.value.findIndex((t: any) => t.id === id)
  if (idx !== -1) {
    tasks.value[idx].status = status
  }
  updateTask(id, { status })
}

async function updateAssignees({ id, assignees }: { id: string, assignees: any[] }) {
  const idx = tasks.value.findIndex((t: any) => t.id === id)
  if (idx !== -1) {
    tasks.value[idx].assignees = assignees
  }
  updateTask(id, { assignees })
}

function onTaskEditSave(editedTask: any) {
  // Find and update the task in tasks array
  const idx = tasks.value.findIndex((t: any) => t.id === editedTask.id)
  if (idx !== -1) {
    tasks.value[idx] = { ...editedTask }
    // updateTask(editedTask.id, editedTask)
  }
  selectedTask.value = null
}

function onSmartViewSelect(val: string | null) {
  // Toggle off if clicking the already-selected value
  if (selectedSmartView.value === val) {
    selectedSmartView.value = null
  }
  else {
    selectedSmartView.value = val
  }
}

async function handleCreateTask() {
  const newTaskModalInstance = newTaskModal.open()
  const result = await newTaskModalInstance.result
  if (!result) {
    return
  }
  tasks.value.push(result)
  await useApiFetch('tasks', {
    method: 'POST',
    body: { ...result, userId: workspaceStore.workspace.id },
  })
}

function openViewTaskModal(task: any) {
  const viewTaskModal = overlay.create(TasksView, { props: { task } })
  const viewTaskModalInstance = viewTaskModal.open()
  viewTaskModalInstance.result.then((result) => {
    if (result) {
      onTaskEditSave(result)
    }
  })
}
</script>

<template>
  <UDashboardPanel id="tasks">
    <template #header>
      <UDashboardNavbar title="Tasks">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <div class="flex flex-wrap items-center justify-end gap-1 mb-3">
            <UButton v-if="selectedTaskIds.length" icon="i-lucide-trash" color="error" variant="subtle"
              :disabled="!selectedTaskIds.length" @click="deleteSelectedTasks">
              Delete {{ selectedTaskIds.length }} tasks
            </UButton>
            <UButton icon="i-lucide-plus" color="primary" @click="handleCreateTask()">
              New Task
            </UButton>
            <UButtonGroup>
              <UButton color="neutral" :variant="viewMode === 'list' ? 'solid' : 'subtle'" label="Table"
                icon="i-lucide-list" @click="viewMode = 'list'" />
              <UButton color="neutral" :variant="viewMode === 'column' ? 'solid' : 'subtle'" label="Board"
                icon="i-lucide-kanban" @click="viewMode = 'column'" />
            </UButtonGroup>
          </div>
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5">
        <UInput v-model="globalFilter" class="max-w-sm" placeholder="Filter..." icon="i-lucide-search" />
        <div class="flex flex-wrap items-center gap-1.5">
          <USelect :model-value="selectedSmartView" :items="smartViewItems" placeholder="All Tasks" class="min-w-40"
            clearable label-key="label" value-key="value"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            @update:model-value="onSmartViewSelect" />
          <USelect v-model="assigneesFilter" :items="assigneeItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Assignees" class="min-w-40" clearable multiple label-key="label" value-key="value" />
          <USelect v-model="statusFilter" :items="statusItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Status" class="min-w-40" clearable label-key="label" value-key="value" />
          <USelect v-model="listingFilter" :items="listingItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Listing" class="min-w-40" clearable label-key="label" value-key="value" />
          <!-- <USwitch v-model="showDone" label="Show Done Tasks" /> -->
        </div>
      </div>
      <div v-if="viewMode === 'list'">
        <TasksTable :tasks="filteredTasks" :show-done="showDone" @update-status="updateTaskStatus"
          @update-assignees="updateAssignees" @selection-change="onSelectionChange" @view-task="openViewTaskModal" />
      </div>
      <div v-if="viewMode === 'column'">
        <TasksKanbanBoard :tasks="filteredTasks" :show-done="showDone" @update-status="updateTaskStatus"
          @view-task="openViewTaskModal" />
      </div>
    </template>
  </UDashboardPanel>
</template>

<style></style>
