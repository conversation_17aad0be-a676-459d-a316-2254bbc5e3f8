<template>
  <div style="padding: 2rem; font-family: sans-serif;">
    <h1 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem;">Minimal Radio Group Test</h1>

    <div style="border: 1px solid #ccc; padding: 1rem; background-color: #f9f9f9;">
      <UFormField label="Select Test Option" name="testRadioMinimal">
        <URadioGroup
          v-model="selectedValue"
          :options="[
            { value: 'min_opt_1', label: 'Minimal Option 1' },
            { value: 'min_opt_2', label: 'Minimal Option 2' },
            { value: 'min_opt_3', label: 'Minimal Option 3' }
          ]"
          class="grid grid-cols-1 gap-3 sm:grid-cols-3"
        />
      </UFormField>
      <p style="margin-top: 1rem;">Selected Value: <strong>{{ selectedValue }}</strong></p>
    </div>

    <div style="margin-top: 2rem; border-top: 1px dashed #eee; padding-top: 1rem;">
      <p><strong>Instructions:</strong></p>
      <ol style="margin-left: 2rem;">
        <li>Save this code as `pages/radio-test.vue`.</li>
        <li>Navigate to `/radio-test` in your browser.</li>
        <li>Report whether the 'Minimal Option 1', 'Minimal Option 2', etc., radio buttons appear.</li>
      </ol>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// Explicitly import Nuxt UI components to be absolutely sure, though auto-import should work.
// You might need to adjust the import path if using a different structure,
// but for Nuxt 3 with components auto-discovery, '#components' is standard.
// import { URadioGroup, UFormField } from '#components'; // Usually not needed but can be a diagnostic

const selectedValue = ref('min_opt_1');
</script>