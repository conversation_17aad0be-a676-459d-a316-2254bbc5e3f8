<script lang="ts" setup>
import type { TableColumn } from '@nuxt/ui'
import { useListingStore, useToast, useWorkspaceStore } from '#imports'
import { h, resolveComponent } from 'vue'
import { useRouter } from 'vue-router'

const listingStore = useListingStore()
const workspaceStore = useWorkspaceStore()
const router = useRouter()
const toast = useToast()

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const UAvatar = resolveComponent('UAvatar')

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: ({ row }) => {
      const url = row.getValue('thumbnail')
      return url ? h(UAvatar, { src: url, size: 'md', square: true }) : ''
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => h('span', { class: 'max-w-[200px] whitespace-normal break-words inline-block align-middle' }, `${row.getValue('name')}`),
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'address',
    header: 'Address',
    cell: ({ row }) => {
      const address = row.getValue('address')
      return h('span', { class: 'max-w-[200px] whitespace-normal break-words inline-block align-middle' }, address || 'No Address')
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'views',
    header: 'Views (30d)',
    cell: ({ row }) => {
      const views = row.getValue('views')
      return h('span', { class: 'max-w-[200px] whitespace-normal break-words inline-block align-middle' }, views || 'No Views')
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const id = row.original.id
      return h('div', { class: 'flex gap-2' }, [
        h(resolveComponent('UButton'), {
          icon: 'i-lucide-copy',
          size: 'xs',
          variant: 'ghost',
          onClick: (e) => {
            e.stopPropagation()
            navigator.clipboard.writeText(`https://guide.yada.ai/${id}`)
            toast.add({
              title: 'Copied',
              description: 'Guidebook link copied to clipboard',
              color: 'success',
            })
          },
          title: 'Copy Guidebook Link',
        }),
        h(resolveComponent('UButton'), {
          icon: 'i-lucide-external-link',
          size: 'xs',
          variant: 'ghost',
          onClick: (e) => {
            e.stopPropagation()
            window.open(`https://guide.yada.ai/${id}`, '_blank')
          },
          title: 'Preview Guidebook',
        }),
      ])
    },
    enableSorting: false,
    enableHiding: false,
  },
]

const table = useTemplateRef('table')
const globalFilter = ref('')

const filteredListings = computed(() => {
  const baseListings = listingStore.listings || []
  const viewsMap = new Map(
    (workspaceStore.listingLeaderboard || []).map((item: any) => [
      item.listing_id,
      {
        views: item.last_30_days_views,
        prevViews: item.prev_30_days_views,
      },
    ]),
  )

  return baseListings.map((listing) => {
    const viewData: any = viewsMap.get(listing.id) || { views: 0, prevViews: 0 }
    const percentChange = calculatePercentageChange(viewData.views, viewData.prevViews)

    return {
      ...listing,
      views: viewData.views,
      viewsChange: percentChange ? formatChange(percentChange) : null,
    }
  })
})

function calculatePercentageChange(current: any, previous: any) {
  if (!previous) {
    return current > 0 ? '∞' : 0
  }
  return ((current - previous) / previous) * 100
}

function formatChange(change: any) {
  return typeof change === 'number' ? `${change.toFixed(1)}%` : `${change}`
}

function onSelect(row: any) {
  const id = row.original.id
  if (id) {
    router.push(`/listings/${id}`)
  }
}
</script>

<template>
  <UDashboardPanel id="guidebooks">
    <template #header>
      <UDashboardNavbar title="Guidebooks">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right />
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5">
        <UInput v-model="globalFilter" class="max-w-sm" placeholder="Filter..." icon="i-lucide-search" />
      </div>
      <UTable
        ref="table" v-model:global-filter="globalFilter" :data="filteredListings" :columns="columns"
        @select="onSelect"
      />
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
