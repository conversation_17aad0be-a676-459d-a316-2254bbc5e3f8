<script lang="ts" setup>
import { useWorkspaceStore } from '#imports'
import { highlight, languages } from 'prismjs/components/prism-core'
import { ref } from 'vue'
import { PrismEditor } from 'vue-prism-editor'
import { getChatCode } from '~/helpers'
import 'vue-prism-editor/dist/prismeditor.min.css'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-javascript'
import 'prismjs/themes/prism-tomorrow.css'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})
const workspaceStore = useWorkspaceStore()

const widget = ref(await useApiFetch('widget'))
const isSaving = ref(false)

const widgetCode = computed(() => getChatCode())
const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

function highlighter(code) {
  return highlight(code, languages.js)
}

async function copyCode() {
  try {
    await navigator.clipboard.writeText(widgetCode.value)
    alert('Text copied to clipboard')
  }
  catch (err) {
    alert('Failed to copy text')
  }
}

function triggerFileInput() {
  document.getElementById('imageUploadInput')?.click()
}

async function handleImageUpload(event: Event) {
  const files = (event.target as HTMLInputElement)?.files
  if (files) {
    for (const file of Array.from(files)) {
      try {
        const blob = await uploadImage(file)
        console.log('uploaded avatar: ', blob.url)
        widget.value.avatar = blob.url
      }
      catch (e) {
        console.error('Failed to upload', file.name, e)
      }
    }
  }
}

async function uploadImage(file: File) {
  const extension = file.name.slice(file.name.lastIndexOf('.'))
  const randomId = crypto.randomUUID()
  const newFileName = `workspace/${randomId}${extension}`
  const formData = new FormData()
  formData.append('file', file, newFileName)
  try {
    const response = await useApiFetch('blob/upload', {
      method: 'POST',
      body: formData,
    })
    return response
  }
  catch (e) {
    throw e
  }
}

async function handleSave() {
  isSaving.value = true
  try {
    await useApiFetch(`widget/${widget.value.id}`, {
      method: 'PATCH',
      body: widget.value,
    })
  }
  catch (error) {
    // Optionally handle error here
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <UDashboardPanel id="integrations">
    <template #header>
      <UDashboardNavbar title="Direct Booking Widget">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <UCard>
        <template #header>
          <div>Chat Widget Code</div>
        </template>
        <div>
          <PrismEditor v-model="widgetCode" class="my-editor rounded-lg" :highlight="highlighter" line-numbers readonly />
        </div>
        <template #footer>
          <UButton @click="copyCode">
            Click Here to Copy Code
          </UButton>
        </template>
      </UCard>
      <UCard>
        <template #header>
          <div>Customize Display and Appearance</div>
        </template>
        <div class="space-y-4">
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Header Text
            </div>
            <UInput v-model="widget.headerText" type="text" placeholder="Enter header text" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Subheader Text
            </div>
            <UInput v-model="widget.subheaderText" type="text" placeholder="Enter subheader text" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Primary Color
            </div>
            <UColorPicker v-model="widget.primaryColor" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Secondary Color
            </div>
            <UColorPicker v-model="widget.secondaryColor" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Avatar
            </div>
            <NuxtImg v-if="widget.avatar" :src="widget.avatar" class="max-w-[100px]" />
            <div>
              <UButton label="Upload Avatar" icon="i-lucide-upload" size="sm" variant="subtle" @click="triggerFileInput" />
            </div>
            <input
              id="imageUploadInput" type="file" hidden accept="image/jpeg,image/png,image/webp"
              @change="handleImageUpload"
            >
          </div>
        </div>
        <template #footer>
          <UButton :disabled="isSaving" :loading="isSaving" @click="handleSave">
            <template #default>
              <span v-if="!isSaving">Save Changes</span>
              <span v-else>Saving...</span>
            </template>
          </UButton>
        </template>
      </UCard>
      <UCard>
        <template #header>
          <div>Manage Widget Behavior</div>
        </template>
        <div class="space-y-4">
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Require Email
            </div>
            <USwitch v-model="widget.getEmail" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Require Phone
            </div>
            <USwitch v-model="widget.getPhone" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Require Name
            </div>
            <USwitch v-model="widget.getName" />
          </div>
          <div class="flex flex-col gap-2">
            <div class="font-medium">
              Default to Open
            </div>
            <USwitch v-model="widget.defaultOpen" />
          </div>
        </div>
        <template #footer>
          <UButton :disabled="isSaving" :loading="isSaving" @click="handleSave">
            <template #default>
              <span v-if="!isSaving">Save Changes</span>
              <span v-else>Saving...</span>
            </template>
          </UButton>
        </template>
      </UCard>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
