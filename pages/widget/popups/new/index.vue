<script lang="ts" setup>
import type { FormSubmitEvent } from '@nuxt/ui'
import { useToast } from '#imports'
import * as v from 'valibot'
import { useRouter } from 'vue-router'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const router = useRouter()

const schema = v.object({
  title: v.pipe(v.string(), v.minLength(5, 'Title must be at least 5 characters')),
  subtitle: v.pipe(v.string(), v.minLength(5, 'Subtitle must be at least 5 characters')),
  image: v.pipe(v.string(), v.minLength(1, 'Image is required')),
  seconds: v.pipe(v.number(), v.minValue(10, 'Seconds must be at least 10'), v.maxValue(30, 'Seconds must be at most 30')),
  desktop: v.boolean(),
  mobile: v.boolean(),
  active: v.boolean(),
})

type Schema = v.InferOutput<typeof schema>

const state = reactive({
  title: '',
  subtitle: '',
  image: '',
  seconds: 15,
  desktop: true,
  mobile: true,
  active: false,
})

const toast = useToast()
async function onSubmit(event: FormSubmitEvent<Schema>) {
  try {
    await useApiFetch('widget/popups', {
      method: 'POST',
      body: event.data,
    })
    toast.add({ title: 'Success', description: 'Popup successfully created', color: 'success' })
    router.push('/widget/popups')
  }
  catch (e) {
    toast.add({ title: 'Error', description: 'Unable to create popup', color: 'danger' })
  }
}

async function uploadImage(file: File) {
  const extension = file.name.slice(file.name.lastIndexOf('.'))
  const randomId = crypto.randomUUID()
  const newFileName = `workspace/${randomId}${extension}`
  const formData = new FormData()
  formData.append('file', file, newFileName)
  try {
    const response = await useApiFetch('blob/upload', {
      method: 'POST',
      body: formData,
    })
    return response
  }
  catch (e) {
    throw e
  }
}

async function handleImageUpload(event: Event) {
  const files = (event.target as HTMLInputElement)?.files
  if (files) {
    for (const file of Array.from(files)) {
      try {
        const blob = await uploadImage(file)
        state.image = blob.url
      }
      catch (e) {
        console.error('Failed to upload', file.name, e)
      }
    }
  }
}

function triggerFileInput() {
  document.getElementById('imageUploadInput')?.click()
}
</script>

<template>
  <UDashboardPanel id="popups-new">
    <template #header>
      <UDashboardNavbar title="Create New Popup">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UForm :schema="schema" :state="state" @submit="onSubmit">
        <div class="space-y-6">
          <UCard>
            <template #header>
              <div>Popup Details</div>
            </template>
            <UFormField label="Title" name="title">
              <UInput v-model="state.title" />
            </UFormField>
            <UFormField label="Subtitle" name="subtitle">
              <UInput v-model="state.subtitle" />
            </UFormField>
          </UCard>
          <UCard>
            <template #header>
              <div>Popup Image</div>
            </template>
            <UFormField name="image">
              <UButton label="Upload Image" icon="i-lucide-upload" size="sm" @click="triggerFileInput" />
              <input
                id="imageUploadInput" type="file" hidden accept="image/jpeg,image/png,image/webp"
                @change="handleImageUpload"
              >
              <NuxtImg v-if="state.image" :src="state.image" alt="Image preview" />
            </UFormField>
          </UCard>
          <UCard>
            <template #header>
              <div>Popup Settings</div>
            </template>
            <UFormField label="Seconds" name="seconds">
              <UInput v-model="state.seconds" type="number" />
            </UFormField>
            <UFormField label="Desktop" name="desktop">
              <USwitch v-model="state.desktop" />
            </UFormField>
            <UFormField label="Mobile" name="mobile">
              <USwitch v-model="state.mobile" />
            </UFormField>
          </UCard>
          <UButton type="submit">
            Create new Popup
          </UButton>
        </div>
      </UForm>
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
