<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import { h, ref, resolveComponent } from 'vue'
import { useRouter } from 'vue-router'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const router = useRouter()

// Popup type
interface Popup {
  id: string
  title: string
  subtitle: string
  image: string
  seconds: number
  desktop: boolean
  mobile: boolean
  active: boolean
  userId: string
  createdAt: { _seconds: number, _nanoseconds: number }
}

const UBadge = resolveComponent('UBadge')
const UAvatar = resolveComponent('UAvatar')

const popups = ref<Popup[]>(await useApiFetch('widget/popups'))

const columns: TableColumn<Popup>[] = [
  {
    accessorKey: 'image',
    header: 'Image',
    cell: ({ row }) =>
      h('img', {
        src: row.getValue('image'),
        alt: row.getValue('title'),
        style: 'width: 48px; height: 48px; object-fit: cover; border-radius: 8px;',
      }),
  },
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => row.getValue('title'),
  },
  {
    accessorKey: 'subtitle',
    header: 'Subtitle',
    cell: ({ row }) => row.getValue('subtitle'),
  },
  {
    accessorKey: 'seconds',
    header: 'Seconds',
    cell: ({ row }) => row.getValue('seconds'),
  },
  {
    accessorKey: 'desktop',
    header: 'Desktop',
    cell: ({ row }) =>
      h(UBadge, { color: row.getValue('desktop') ? 'primary' : 'gray', variant: 'subtle' }, () =>
        row.getValue('desktop') ? 'Yes' : 'No'),
  },
  {
    accessorKey: 'mobile',
    header: 'Mobile',
    cell: ({ row }) =>
      h(UBadge, { color: row.getValue('mobile') ? 'primary' : 'gray', variant: 'subtle' }, () =>
        row.getValue('mobile') ? 'Yes' : 'No'),
  },
  {
    accessorKey: 'active',
    header: 'Active',
    cell: ({ row }) => {
      // Use USwitch for toggling active
      const USwitch = resolveComponent('USwitch')
      // Get the popup object for two-way binding
      const popup = row.original
      return h(USwitch, {
        'modelValue': popup.active,
        'onUpdate:modelValue': (val: boolean) => handleActiveChange(popup, val),
        'color': popup.active ? 'success' : 'gray',
        'class': 'min-w-[32px] flex justify-center',
      })
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created',
    cell: ({ row }) => {
      const val = row.getValue('createdAt')
      const seconds = typeof val === 'object' && val?._seconds ? val._seconds : 0
      return new Date(seconds * 1000).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    },
  },
]

function handleCreatePopup() {
  router.push('/widget/popups/new')
}

async function handleActiveChange(popup: Popup, value: boolean) {
  // Call backend to update active state
  try {
    await useApiFetch(`widget/popups/${popup.id}`, {
      method: 'PATCH',
      body: { active: value },
    })
    // Refresh popups list from backend to ensure only one is active
    popups.value = await useApiFetch('widget/popups')
  }
  catch (e) {
    console.error('Failed to update popup active state:', e)
  }
}
</script>

<template>
  <UDashboardPanel id="popups">
    <template #header>
      <UDashboardNavbar title="Popups">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-wrap items-center justify-end gap-1 mb-3">
        <UButton icon="i-lucide-plus" color="primary" @click="handleCreatePopup()">
          New Popup
        </UButton>
      </div>
      <UTable
        :data="popups"
        :columns="columns"
        :ui="{ tr: 'data-[expanded=true]:bg-elevated/50' }"
        class="flex-1"
      />
    </template>
  </UDashboardPanel>
</template>

<style>

</style>
