<script setup lang="ts">
import { computed, ref } from 'vue'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

// State for the calendar and selected date
const currentDate = ref(new Date(2025, 3, 10)) // April 10, 2025 (month is 0-indexed)
const selectedCalendarDate = ref(new Date(2025, 3, 10)) // To link with UCalendar or SimpleCalendar

const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString(undefined, {
    month: 'long',
    year: 'numeric',
  })
})

const selectedDayFormatted = computed(() => {
  if (!selectedCalendarDate.value)
    return 'No date selected'
  return selectedCalendarDate.value.toLocaleDateString(undefined, {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  })
})

// Calendar navigation functions
function prevMonth() {
  currentDate.value = new Date(currentDate.value.setMonth(currentDate.value.getMonth() - 1))
  // You might want to update selectedCalendarDate or other logic here too
}

function nextMonth() {
  currentDate.value = new Date(currentDate.value.setMonth(currentDate.value.getMonth() + 1))
}

function goToToday() {
  const today = new Date()
  currentDate.value = today
  selectedCalendarDate.value = today
}

// Function to handle date selection from SimpleCalendar
function handleDateSelect(date: Date) {
  selectedCalendarDate.value = date
  currentDate.value = new Date(date) // Optionally sync the month view
}
</script>

<template>
  <UDashboardPanel id="workflow-calendar">
    <template #header>
      <UDashboardNavbar title="Workflow Messages Schedule">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="grid gap-4 md:grid-cols-[350px_1fr]">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">
              Calendar
            </h3>
          </template>
          <WorkflowsCalendarSimpleCalendar @date-selected="handleDateSelect" />
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">
              {{ selectedDayFormatted }}
            </h3>
          </template>
          <WorkflowsCalendarDaySchedule :selected-day="selectedCalendarDate" />
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped>
/* You can add page-specific styles here if needed,
   though Tailwind utility classes are preferred for Nuxt UI. */
.text-muted-foreground {
  color: #6b7280;
  /* Example, adjust based on your Tailwind config */
}

/* Ensure Tailwind classes are available */
</style>
