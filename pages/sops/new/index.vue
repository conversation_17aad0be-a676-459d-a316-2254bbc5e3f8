<script lang="ts" setup>
import { useListingStore, useWorkspaceStore } from '#imports'
import { taskTypes } from '@/shared/webapp'
import Editor from '@hugerte/hugerte-vue'
import * as v from 'valibot'
import { useRouter } from 'vue-router'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const router = useRouter()
const listingStore = useListingStore()
const workspaceStore = useWorkspaceStore()

const isSaving = ref(false)

const schema = v.object({
  title: v.pipe(v.string(), v.minLength(5, 'Title must be at least 5 characters')),
  description: v.pipe(v.string(), v.minLength(10, 'Description must be at least 10 characters')),
  sopType: v.pipe(v.string(), v.minLength(1, 'Type is required')),
  text: v.pipe(v.string(), v.minLength(100, 'Instructions must be at least 100 characters')),
})

type Schema = v.InferOutput<typeof schema>

const state = reactive({
  title: '',
  description: '',
  text: '',
  images: [],
  videos: [],
  selectedListings: [],
  applyToAllListings: true,
  sopType: '',
})

const videoInput = ref('')
const videoInputError = ref('')

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

// Watch selectedListings to update applyToAllListings
watch(
  () => state.selectedListings,
  (newVal) => {
    if (Array.isArray(newVal) && newVal.length > 0) {
      state.applyToAllListings = false
    }
    else {
      state.applyToAllListings = true
    }
  },
  { deep: true },
)

const listingOptions = computed(() =>
  listingStore.listings.map((l: any) => ({ label: l.name, value: l.id })),
)

// Compute typeItems for SOP Type select, filtering out hidden types
const typeItems = computed(() =>
  taskTypes.filter((t: any) => !t.hidden && t.label && t.value).map((t: any) => ({ label: t.value, value: t.label, description: t.description })),
)

function getEmbedUrl(url: string) {
  try {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      let videoId = ''
      if (url.includes('youtube.com/watch')) {
        videoId = new URL(url).searchParams.get('v') || ''
      }
      else if (url.includes('youtu.be/')) {
        videoId = url.split('youtu.be/')[1].split('?')[0]
      }
      return videoId ? `https://www.youtube.com/embed/${videoId}` : null
    }
    else if (url.includes('vimeo.com')) {
      const vimeoId = url.split('vimeo.com/')[1]?.split('?')[0]
      return vimeoId ? `https://player.vimeo.com/video/${vimeoId}` : null
    }
    else if (url.includes('loom.com')) {
      const loomId = url.includes('/share/') ? url.split('/share/')[1]?.split('?')[0] : null
      return loomId ? `https://www.loom.com/embed/${loomId}` : null
    }
    return null
  }
  catch {
    return null
  }
}

async function uploadImage(file: File) {
  const extension = file.name.slice(file.name.lastIndexOf('.'))
  const randomId = crypto.randomUUID()
  const newFileName = `sops/${randomId}${extension}`
  const formData = new FormData()
  formData.append('file', file, newFileName)
  try {
    const response = await useApiFetch('blob/upload', {
      method: 'POST',
      body: formData,
    })
    return response
  }
  catch (e) {
    throw e
  }
}

async function handleImageUpload(event: Event) {
  const files = (event.target as HTMLInputElement)?.files
  if (files) {
    for (const file of Array.from(files)) {
      try {
        const blob: any = await uploadImage(file)
        state.images.push(blob.url)
      }
      catch (e) {
        console.error('Failed to upload', file.name, e)
      }
    }
  }
}

function triggerFileInput() {
  document.getElementById('imageUploadInput')?.click()
}

function handleAddVideo() {
  const url = videoInput.value.trim()
  if (!url)
    return
  if (!getEmbedUrl(url)) {
    videoInputError.value = 'Please enter a valid YouTube, Vimeo, or Loom URL.'
    return
  }
  state.videos.push(url)
  videoInput.value = ''
  videoInputError.value = ''
}
function handleRemoveVideo(idx: number) {
  state.videos.splice(idx, 1)
}

async function handleSave() {
  isSaving.value = true
  const payload = {
    title: state.title,
    description: state.description,
    text: state.text,
    type: state.sopType,
    images: state.images,
    videos: state.videos,
    applyToAllListings: state.applyToAllListings,
    selectedListings: state.selectedListings,
  }
  try {
    await useApiFetch('sops', {
      method: 'POST',
      body: payload,
    })
    isSaving.value = false
    router.push('/sops')
  }
  catch (e) {
    console.error(e)
    isSaving.value = false
  }
}
</script>

<template>
  <UDashboardPanel id="new-sop">
    <template #header>
      <UDashboardNavbar title="Create a New SOP">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <div class="mx-auto w-full max-w-2xl px-2">
        <UForm :schema="schema" :state="state" @submit="handleSave">
          <!-- Card 1: Title and Description -->
          <UCard class="mb-6">
            <UFormField label="Title (required)" name="title">
              <UInput v-model="state.title" class="w-full" />
            </UFormField>
            <UFormField label="Description (required)" name="description">
              <UTextarea v-model="state.description" class="w-full" />
            </UFormField>
            <UFormField label="SOP Type" name="sopType">
              <USelect
                v-model="state.sopType"
                :items="typeItems"
                :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
                class="w-full"
                clearable
                label-key="label"
                value-key="value"
                :option-description="item => item.description"
              />
            </UFormField>
          </UCard>

          <!-- Text Editor remains outside cards -->
          <UFormField label="Detailed Instructions (required)" name="text">
            <Editor
              v-model="state.text" class="h-[50vh]" :init="{
                plugins: 'lists link image table code help wordcount',
              }"
            />
          </UFormField>

          <!-- Card 2: Listings -->
          <UCard class="my-6">
            <UFormField label="Apply to All Listings">
              <USwitch v-model="state.applyToAllListings" />
            </UFormField>
            <UFormField label="Listings">
              <USelectMenu v-model="state.selectedListings" :items="listingOptions" multiple placeholder="Select listings" class="w-full" />
            </UFormField>
          </UCard>

          <!-- Card 3: Images -->
          <UCard class="mb-6">
            <UFormField label="Images">
              <UButton label="Upload Image" icon="i-lucide-upload" size="sm" @click="triggerFileInput" />
              <input
                id="imageUploadInput" type="file" hidden accept="image/jpeg,image/png,image/webp"
                @change="handleImageUpload"
              >
              <div v-if="state.images.length" class="flex flex-wrap gap-2 mt-2">
                <div v-for="(img, idx) in state.images" :key="idx">
                  <NuxtImg :src="img" alt="Image preview" />
                </div>
              </div>
            </UFormField>
          </UCard>

          <!-- Card 4: Videos -->
          <UCard>
            <UFormField label="Videos">
              <div class="space-y-2">
                <div class="flex items-center gap-2">
                  <UInput v-model="videoInput" placeholder="Paste Vimeo, Loom, or YouTube URL" class="flex-1" />
                  <UButton icon="i-lucide-plus" size="xs" :disabled="!videoInput.trim()" @click="handleAddVideo" />
                </div>
                <div v-if="videoInputError" class="text-xs text-red-500 mt-1">
                  {{ videoInputError }}
                </div>
                <div v-if="state.videos.length" class="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-2">
                  <div v-for="(video, index) in state.videos" :key="index" class="relative">
                    <div v-if="getEmbedUrl(video)" class="rounded-lg overflow-hidden aspect-video">
                      <iframe
                        :src="getEmbedUrl(video)"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowfullscreen
                        class="w-full h-full border-0"
                      />
                    </div>
                    <div v-else class="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-500">
                      Invalid video URL
                    </div>
                    <UButton icon="i-lucide-x" color="red" size="xs" variant="ghost" class="absolute top-2 right-2" @click="handleRemoveVideo(index)" />
                  </div>
                </div>
                <small class="text-gray-500 text-sm">Supports YouTube, Vimeo, and Loom video links</small>
              </div>
            </UFormField>
          </UCard>
          <UButton
            type="submit"
            label="Create New SOP"
            color="primary"
            class="mt-6"
            :loading="isSaving"
            :disabled="isSaving"
          />
        </UForm>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped>
</style>
