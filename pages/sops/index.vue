<script lang="ts" setup>
import { SopsGrid, SopsTable } from '#components'
import { useListingStore, useWorkspaceStore } from '#imports'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { taskTypes } from '~/shared/webapp'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const listingStore = useListingStore()
const workspaceStore = useWorkspaceStore()

// Use a ref for full reactivity
const sops = ref(await useApiFetch('sops'))

const viewMode = ref('list')
const typeFilter = ref('all')
const listingFilter = ref('all')
const globalFilter = ref('')

const isReady = computed(() => !!workspaceStore.profile && !!workspaceStore.workspace)

const typeItems = computed(() => [
  { label: 'All Types', value: 'all' },
  ...taskTypes.filter((t: any) => !t.hidden && t.label && t.value).map((t: any) => ({ label: t.value, value: t.label, description: t.description })),
])

const listingItems = computed(() => [
  { label: 'All Listings', value: 'all' },
  ...listingStore.listings.map((l: any) => ({ label: l.name, value: l.id })),
])

const filteredSops = computed(() => {
  let filtered = sops.value
  if (typeFilter.value && typeFilter.value !== 'all') {
    filtered = filtered.filter((sop: any) => sop.type === typeFilter.value)
  }
  if (listingFilter.value && listingFilter.value !== 'all') {
    filtered = filtered.filter((sop: any) => {
      if (sop.applyToAllListings)
        return true
      return sop.selectedListings && sop.selectedListings.includes(listingFilter.value)
    })
  }
  if (globalFilter.value && globalFilter.value.trim().length > 0) {
    const filter = globalFilter.value.trim().toLowerCase()
    filtered = filtered.filter((sop: any) =>
      (sop.title && sop.title.toLowerCase().includes(filter))
      || (sop.description && sop.description.toLowerCase().includes(filter))
      || (sop.text && sop.text.toLowerCase().includes(filter)),
    )
  }
  return filtered
})

const router = useRouter()
function handleCreateSop() {
  router.push('/sops/new')
}

function handleViewSop(sop: any) {
  // Navigate to the SOP detail page
  router.push(`/sops/${sop.id}`)
}
</script>

<template>
  <UDashboardPanel id="tasks">
    <template #header>
      <UDashboardNavbar title="SOPs">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <div class="flex flex-wrap items-center justify-end gap-1 mb-3">
            <UButton icon="i-lucide-plus" color="primary" @click="handleCreateSop()">
              New SOP
            </UButton>
            <UButtonGroup>
              <UButton color="neutral" :variant="viewMode === 'list' ? 'solid' : 'subtle'" label="Table"
                icon="i-lucide-list" @click="viewMode = 'list'" />
              <UButton color="neutral" :variant="viewMode === 'column' ? 'solid' : 'subtle'" label="Grid"
                icon="i-lucide-kanban" @click="viewMode = 'column'" />
            </UButtonGroup>
          </div>
        </template>
      </UDashboardNavbar>
    </template>
    <template v-if="isReady" #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5">
        <UInput v-model="globalFilter" class="max-w-sm" placeholder="Filter..." icon="i-lucide-search" />
        <div class="flex flex-wrap items-center gap-1.5">
          <USelect v-model="listingFilter" :items="listingItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Listing" class="w-[200px]" clearable label-key="label" value-key="value" />
          <USelect v-model="typeFilter" :items="typeItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            placeholder="Filter Type" class="w-[200px]" clearable label-key="label" value-key="value" />
        </div>
      </div>
      <div v-if="viewMode === 'list'">
        <SopsTable :sops="filteredSops" @view-sop="handleViewSop" />
      </div>
      <div v-else-if="viewMode === 'column'">
        <SopsGrid :sops="filteredSops" @view-sop="handleViewSop" />
      </div>
    </template>
  </UDashboardPanel>
</template>

<style></style>
