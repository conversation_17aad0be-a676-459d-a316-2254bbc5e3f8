<script lang="ts" setup>
import { SmartSpacesAmenitiesModal, SmartSpacesPdfSuccessModal } from '#components'
import { useListingStore } from '#imports'

const listingStore = useListingStore()

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const toast = useToast()
const overlay = useOverlay()

const globalFilter = ref('')
const downloadingCoverIds = ref<Set<string>>(new Set())

const isAnyCoverDownloading = computed(() => downloadingCoverIds.value.size > 0)

const filteredListings = computed(() => {
  const baseListings = listingStore.listings || []
  if (!globalFilter.value) {
    return baseListings
  }
  const searchTerm = globalFilter.value.toLowerCase()
  return baseListings.filter((listing) => {
    if (listing.name && listing.name.toLowerCase().includes(searchTerm)) {
      return true
    }
    if (listing.address && listing.address.toLowerCase().includes(searchTerm)) {
      return true
    }
    return false
  })
})


async function handleDownloadCover(listingId: string) {
  downloadingCoverIds.value.add(listingId)
  downloadingCoverIds.value = new Set(downloadingCoverIds.value)

  try {
    const pdfUrl: any = await useApiFetch(`smart-spaces/${listingId}`, {
      method: 'POST',
      body: {
        type: 'cover',
      },
    })
    showPdfSuccessModal(pdfUrl, 'cover', listingStore.listings.find(l => l.id === listingId)?.name || 'listing')
  }
  catch (error) {
    console.error('Error downloading cover card:', error)
    toast.add({
      title: 'Error',
      description: error.statusMessage,
      color: 'error',
    })
  }
  finally {
    downloadingCoverIds.value.delete(listingId)
    downloadingCoverIds.value = new Set(downloadingCoverIds.value)
  }
}

async function handleDownloadAmenities(listingId: string) {
  const amenitiesModal = overlay.create(SmartSpacesAmenitiesModal, {
    props: {
      listingId,
    },
  })
  const amenitiesModalInstance = amenitiesModal.open()
  const result: any = await amenitiesModalInstance.result
  if (!result)
    return
  showPdfSuccessModal(result, 'amenities', listingStore.listings.find(l => l.id === listingId)?.name || 'listing')
}

async function showPdfSuccessModal(pdfUrl: string, pdfType: string, listingName: string) {
  const downloadUrlModal = overlay.create(SmartSpacesPdfSuccessModal, {
    props: {
      pdfUrl,
      pdfType,
      listingName,
    },
  })
  const downloadPdfUrlModalInstance = downloadUrlModal.open()
  await downloadPdfUrlModalInstance.result
}
</script>

<template>
  <UDashboardPanel id="smartspaces">
    <template #header>
      <UDashboardNavbar title="Smart Spaces">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right />
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-wrap items-center justify-between gap-1.5 mb-4">
        <UInput v-model="globalFilter" class="max-w-sm" placeholder="Filter..." icon="i-lucide-search" />
      </div>
      <div>
        <div v-if="filteredListings.length === 0" class="text-center py-8 text-gray-500">
          No listings match your search criteria.
        </div>
        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <UCard v-for="listing in filteredListings" :key="listing.id"
            class="cursor-pointer hover:shadow-sm transition-shadow duration-100">
            <div class="relative mb-2" style="padding-top: 66.67%">
              <NuxtImg :src="listing.thumbnail || 'https://via.placeholder.com/150'" alt="Listing Thumbnail"
                class="absolute inset-0 w-full h-full object-cover rounded-t-lg" />
            </div>
            <div>
              <h3 class="text-lg font-semibold mb-1 truncate">
                {{ listing.name }}
              </h3>
              <p class="text-sm text-gray-500 mb-2 truncate">
                {{ listing.address }}
              </p>
              <!-- Smart Spaces Buttons -->
              <!-- <div v-if="!listing.active" class="mt-2">
                <UButton
                  block
                  size="sm"
                  color="success"
                  label="Activate Listing"
                  :disabled="isAnyCoverDownloading"
                  @click.stop="handleActivateListing(listing.id, $event)"
                />
              </div> -->
              <!-- v-else  -->
              <div class="flex gap-2 mt-2">
                <UButton block size="sm" color="primary" icon="i-lucide-download" label="Cover"
                  :loading="downloadingCoverIds.has(listing.id)"
                  :disabled="downloadingCoverIds.has(listing.id) || (isAnyCoverDownloading && !downloadingCoverIds.has(listing.id))"
                  @click.stop="handleDownloadCover(listing.id)" />
                <UButton block size="sm" color="primary" icon="i-lucide-download" label="Amenities"
                  :disabled="isAnyCoverDownloading" @click="handleDownloadAmenities(listing.id)" />
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style></style>
