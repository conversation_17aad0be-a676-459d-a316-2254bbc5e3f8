export const ClaudeData = [
    {
        model: 'claude',
        prompt_tokens: 150,
        completion_tokens: 300,
        total_tokens: 450,
        timestamp: '2023-10-26T10:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 50,
        completion_tokens: 100,
        total_tokens: 150,
        timestamp: '2023-10-26T10:15:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 200,
        completion_tokens: 50,
        total_tokens: 0.0,
        timestamp: '2023-10-26T10:30:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 100,
        completion_tokens: 200,
        total_tokens: 0,
        timestamp: '2023-10-27T11:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 300,
        completion_tokens: 150,
        total_tokens: 0,
        timestamp: '2023-10-28T12:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 75,
        completion_tokens: 125,
        total_tokens: 0,
        timestamp: '2023-10-29T13:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 120,
        completion_tokens: 180,
        total_tokens: 300,
        timestamp: '2023-10-30T14:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 250,
        completion_tokens: 350,
        total_tokens: 320,
        timestamp: '2023-10-31T15:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 80,
        completion_tokens: 90,
        total_tokens: 0,
        timestamp: '2023-11-01T16:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 400,
        completion_tokens: 200,
        total_tokens: 0,
        timestamp: '2023-11-02T17:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 60,
        completion_tokens: 140,
        total_tokens: 0,
        timestamp: '2023-11-03T18:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 180,
        completion_tokens: 220,
        total_tokens: 0,
        timestamp: '2023-11-04T19:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 350,
        completion_tokens: 450,
        total_tokens: 300,
        timestamp: '2023-11-05T20:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 90,
        completion_tokens: 110,
        total_tokens: 0,
        timestamp: '2023-11-06T21:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 220,
        completion_tokens: 280,
        total_tokens: 0,
        timestamp: '2023-11-07T22:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 450,
        completion_tokens: 550,
        total_tokens: 680,
        timestamp: '2023-11-08T23:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 110,
        completion_tokens: 190,
        total_tokens: 0,
        timestamp: '2023-11-09T00:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 280,
        completion_tokens: 320,
        total_tokens: 0,
        timestamp: '2023-11-10T01:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 550,
        completion_tokens: 450,
        total_tokens: 0,
        timestamp: '2023-11-11T02:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 190,
        completion_tokens: 210,
        total_tokens: 0,
        timestamp: '2023-11-12T03:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 320,
        completion_tokens: 380,
        total_tokens: 0,
        timestamp: '2023-11-13T04:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 650,
        completion_tokens: 350,
        total_tokens: 250,
        timestamp: '2023-11-14T05:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 210,
        completion_tokens: 290,
        total_tokens: 0,
        timestamp: '2023-11-15T06:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 380,
        completion_tokens: 420,
        total_tokens: 380,
        timestamp: '2023-11-16T07:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 700,
        completion_tokens: 300,
        total_tokens: 410,
        timestamp: '2023-11-17T08:00:00Z',
    },
    {
        model: 'claude',
        prompt_tokens: 290,
        completion_tokens: 310,
        total_tokens: 600,
        timestamp: '2023-11-18T09:00:00Z',
    },
]

export const OpenAIData = [
    {
        model: 'open-ai',
        prompt_tokens: 75,
        completion_tokens: 125,
        total_tokens: 0,
        timestamp: '2023-10-26T11:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 300,
        completion_tokens: 200,
        total_tokens: 0,
        timestamp: '2023-10-26T11:15:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 1000,
        completion_tokens: 700,
        total_tokens: 0,
        timestamp: '2023-10-26T11:30:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 150,
        completion_tokens: 250,
        total_tokens: 0,
        timestamp: '2023-10-27T12:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 400,
        completion_tokens: 300,
        total_tokens: 0,
        timestamp: '2023-10-28T13:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 100,
        completion_tokens: 150,
        total_tokens: 0,
        timestamp: '2023-10-29T14:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 200,
        completion_tokens: 200,
        total_tokens: 0,
        timestamp: '2023-10-30T15:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 500,
        completion_tokens: 400,
        total_tokens: 0,
        timestamp: '2023-10-31T16:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 120,
        completion_tokens: 180,
        total_tokens: 0,
        timestamp: '2023-11-01T17:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 600,
        completion_tokens: 350,
        total_tokens: 0,
        timestamp: '2023-11-02T18:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 80,
        completion_tokens: 170,
        total_tokens: 0,
        timestamp: '2023-11-03T19:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 350,
        completion_tokens: 250,
        total_tokens: 0,
        timestamp: '2023-11-04T20:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 700,
        completion_tokens: 500,
        total_tokens: 0,
        timestamp: '2023-11-05T21:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 130,
        completion_tokens: 220,
        total_tokens: 0,
        timestamp: '2023-11-06T22:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 450,
        completion_tokens: 350,
        total_tokens: 0,
        timestamp: '2023-11-07T23:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 900,
        completion_tokens: 600,
        total_tokens: 0,
        timestamp: '2023-11-08T00:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 170,
        completion_tokens: 230,
        total_tokens: 0,
        timestamp: '2023-11-09T01:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 550,
        completion_tokens: 450,
        total_tokens: 0,
        timestamp: '2023-11-10T02:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 1100,
        completion_tokens: 800,
        total_tokens: 0,
        timestamp: '2023-11-11T03:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 230,
        completion_tokens: 270,
        total_tokens: 0,
        timestamp: '2023-11-12T04:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 650,
        completion_tokens: 550,
        total_tokens: 0,
        timestamp: '2023-11-13T05:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 1300,
        completion_tokens: 700,
        total_tokens: 0,
        timestamp: '2023-11-14T06:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 270,
        completion_tokens: 330,
        total_tokens: 0,
        timestamp: '2023-11-15T07:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 750,
        completion_tokens: 650,
        total_tokens: 210,
        timestamp: '2023-11-16T08:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 1500,
        completion_tokens: 500,
        total_tokens: 260,
        timestamp: '2023-11-17T09:00:00Z',
    },
    {
        model: 'open-ai',
        prompt_tokens: 330,
        completion_tokens: 370,
        total_tokens: 190,
        timestamp: '2023-11-18T10:00:00Z',
    },
]

export const GeminiData = [
    {
        model: 'gemini',
        prompt_tokens: 500,
        completion_tokens: 1000,
        total_tokens: 0,
        timestamp: '2023-10-26T12:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 100,
        completion_tokens: 200,
        total_tokens: 0,
        timestamp: '2023-10-26T12:15:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 2000,
        completion_tokens: 1500,
        total_tokens: 0,
        timestamp: '2023-10-26T12:30:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 700,
        completion_tokens: 1200,
        total_tokens: 0,
        timestamp: '2023-10-27T13:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 300,
        completion_tokens: 500,
        total_tokens: 0,
        timestamp: '2023-10-28T14:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1200,
        completion_tokens: 900,
        total_tokens: 0,
        timestamp: '2023-10-29T15:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 600,
        completion_tokens: 800,
        total_tokens: 0,
        timestamp: '2023-10-30T16:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 400,
        completion_tokens: 600,
        total_tokens: 0,
        timestamp: '2023-10-31T17:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1500,
        completion_tokens: 1100,
        total_tokens: 0,
        timestamp: '2023-11-01T18:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 800,
        completion_tokens: 1300,
        total_tokens: 0,
        timestamp: '2023-11-02T19:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 250,
        completion_tokens: 450,
        total_tokens: 0,
        timestamp: '2023-11-03T20:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1800,
        completion_tokens: 1200,
        total_tokens: 0,
        timestamp: '2023-11-04T21:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 900,
        completion_tokens: 1400,
        total_tokens: 20,
        timestamp: '2023-11-05T22:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 350,
        completion_tokens: 550,
        total_tokens: 21,
        timestamp: '2023-11-06T23:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 2200,
        completion_tokens: 1300,
        total_tokens: 10,
        timestamp: '2023-11-07T00:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1000,
        completion_tokens: 1500,
        total_tokens: 0,
        timestamp: '2023-11-08T01:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 450,
        completion_tokens: 650,
        total_tokens: 0,
        timestamp: '2023-11-09T02:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 2500,
        completion_tokens: 1400,
        total_tokens: 0,
        timestamp: '2023-11-10T03:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1100,
        completion_tokens: 1600,
        total_tokens: 30,
        timestamp: '2023-11-11T04:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 550,
        completion_tokens: 750,
        total_tokens: 40,
        timestamp: '2023-11-12T05:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 2800,
        completion_tokens: 1500,
        total_tokens: 50,
        timestamp: '2023-11-13T06:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1200,
        completion_tokens: 1700,
        total_tokens: 0,
        timestamp: '2023-11-14T07:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 650,
        completion_tokens: 850,
        total_tokens: 0,
        timestamp: '2023-11-15T08:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 3100,
        completion_tokens: 1600,
        total_tokens: 0,
        timestamp: '2023-11-16T09:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 1300,
        completion_tokens: 1800,
        total_tokens: 0,
        timestamp: '2023-11-17T10:00:00Z',
    },
    {
        model: 'gemini',
        prompt_tokens: 750,
        completion_tokens: 950,
        total_tokens: 0,
        timestamp: '2023-11-18T11:00:00Z',
    },
]

export const UsageData = [
    {
        'date': 'Aug 01',
        'Sonnet 3.7': 2100.2,
        'GPT-4o': 4434.1,
        'Gemini 2.5 Pro': 7943.2,
    },
    {
        'date': 'Aug 02',
        'Sonnet 3.7': 2943.0,
        'GPT-4o': 4954.1,
        'Gemini 2.5 Pro': 8954.1,
    },
    {
        'date': 'Aug 03',
        'Sonnet 3.7': 4889.5,
        'GPT-4o': 6100.2,
        'Gemini 2.5 Pro': 9123.7,
    },
    {
        'date': 'Aug 04',
        'Sonnet 3.7': 3909.8,
        'GPT-4o': 4909.7,
        'Gemini 2.5 Pro': 7478.4,
    },
    {
        'date': 'Aug 05',
        'Sonnet 3.7': 5778.7,
        'GPT-4o': 7103.1,
        'Gemini 2.5 Pro': 9504.3,
    },
    {
        'date': 'Aug 06',
        'Sonnet 3.7': 5900.9,
        'GPT-4o': 7534.3,
        'Gemini 2.5 Pro': 9943.4,
    },
    {
        'date': 'Aug 07',
        'Sonnet 3.7': 4129.4,
        'GPT-4o': 7412.1,
        'Gemini 2.5 Pro': 10112.2,
    },
    {
        'date': 'Aug 08',
        'Sonnet 3.7': 6021.2,
        'GPT-4o': 7834.4,
        'Gemini 2.5 Pro': 10290.2,
    },
    {
        'date': 'Aug 09',
        'Sonnet 3.7': 6279.8,
        'GPT-4o': 8159.1,
        'Gemini 2.5 Pro': 10349.6,
    },
    {
        'date': 'Aug 10',
        'Sonnet 3.7': 6224.5,
        'GPT-4o': 8260.6,
        'Gemini 2.5 Pro': 10415.4,
    },
    {
        'date': 'Aug 11',
        'Sonnet 3.7': 6380.6,
        'GPT-4o': 8965.3,
        'Gemini 2.5 Pro': 10636.3,
    },
    {
        'date': 'Aug 12',
        'Sonnet 3.7': 6414.4,
        'GPT-4o': 7989.3,
        'Gemini 2.5 Pro': 10900.5,
    },
    {
        'date': 'Aug 13',
        'Sonnet 3.7': 6540.1,
        'GPT-4o': 7839.6,
        'Gemini 2.5 Pro': 11040.4,
    },
    {
        'date': 'Aug 14',
        'Sonnet 3.7': 6634.4,
        'GPT-4o': 7343.8,
        'Gemini 2.5 Pro': 11390.5,
    },
    {
        'date': 'Aug 15',
        'Sonnet 3.7': 7124.6,
        'GPT-4o': 6903.7,
        'Gemini 2.5 Pro': 11423.1,
    },
    {
        'date': 'Aug 16',
        'Sonnet 3.7': 7934.5,
        'GPT-4o': 6273.6,
        'Gemini 2.5 Pro': 12134.4,
    },
    {
        'date': 'Aug 17',
        'Sonnet 3.7': 10287.8,
        'GPT-4o': 5900.3,
        'Gemini 2.5 Pro': 12034.4,
    },
    {
        'date': 'Aug 18',
        'Sonnet 3.7': 10323.2,
        'GPT-4o': 5732.1,
        'Gemini 2.5 Pro': 11011.7,
    },
    {
        'date': 'Aug 19',
        'Sonnet 3.7': 10511.4,
        'GPT-4o': 5523.1,
        'Gemini 2.5 Pro': 11834.8,
    },
    {
        'date': 'Aug 20',
        'Sonnet 3.7': 11043.9,
        'GPT-4o': 5422.3,
        'Gemini 2.5 Pro': 12387.1,
    },
    {
        'date': 'Aug 21',
        'Sonnet 3.7': 6700.7,
        'GPT-4o': 5334.2,
        'Gemini 2.5 Pro': 11032.2,
    },
    {
        'date': 'Aug 22',
        'Sonnet 3.7': 6900.8,
        'GPT-4o': 4943.4,
        'Gemini 2.5 Pro': 10134.2,
    },
    {
        'date': 'Aug 23',
        'Sonnet 3.7': 7934.5,
        'GPT-4o': 4812.1,
        'Gemini 2.5 Pro': 9921.2,
    },
    {
        'date': 'Aug 24',
        'Sonnet 3.7': 9021.0,
        'GPT-4o': 2729.1,
        'Gemini 2.5 Pro': 10549.8,
    },
    {
        'date': 'Aug 25',
        'Sonnet 3.7': 9198.2,
        'GPT-4o': 2178.0,
        'Gemini 2.5 Pro': 10968.4,
    },
    {
        'date': 'Aug 26',
        'Sonnet 3.7': 9557.1,
        'GPT-4o': 2158.3,
        'Gemini 2.5 Pro': 11059.1,
    },
    {
        'date': 'Aug 27',
        'Sonnet 3.7': 9959.8,
        'GPT-4o': 2100.8,
        'Gemini 2.5 Pro': 11903.6,
    },
    {
        'date': 'Aug 28',
        'Sonnet 3.7': 10034.6,
        'GPT-4o': 2934.4,
        'Gemini 2.5 Pro': 12143.3,
    },
    {
        'date': 'Aug 29',
        'Sonnet 3.7': 10243.8,
        'GPT-4o': 3223.4,
        'Gemini 2.5 Pro': 12930.1,
    },
    {
        'date': 'Aug 30',
        'Sonnet 3.7': 10078.5,
        'GPT-4o': 3779.1,
        'Gemini 2.5 Pro': 13420.5,
    },
    {
        'date': 'Aug 31',
        'Sonnet 3.7': 11134.6,
        'GPT-4o': 4190.3,
        'Gemini 2.5 Pro': 14443.2,
    },
    {
        'date': 'Sep 01',
        'Sonnet 3.7': 12347.2,
        'GPT-4o': 4839.1,
        'Gemini 2.5 Pro': 14532.1,
    },
    {
        'date': 'Sep 02',
        'Sonnet 3.7': 12593.8,
        'GPT-4o': 5153.3,
        'Gemini 2.5 Pro': 14283.5,
    },
    {
        'date': 'Sep 03',
        'Sonnet 3.7': 12043.4,
        'GPT-4o': 5234.8,
        'Gemini 2.5 Pro': 14078.9,
    },
    {
        'date': 'Sep 04',
        'Sonnet 3.7': 12144.9,
        'GPT-4o': 5478.4,
        'Gemini 2.5 Pro': 13859.7,
    },
    {
        'date': 'Sep 05',
        'Sonnet 3.7': 12489.5,
        'GPT-4o': 5741.1,
        'Gemini 2.5 Pro': 13539.2,
    },
    {
        'date': 'Sep 06',
        'Sonnet 3.7': 12748.7,
        'GPT-4o': 6743.9,
        'Gemini 2.5 Pro': 13643.2,
    },
    {
        'date': 'Sep 07',
        'Sonnet 3.7': 12933.2,
        'GPT-4o': 7832.8,
        'Gemini 2.5 Pro': 14629.2,
    },
    {
        'date': 'Sep 08',
        'Sonnet 3.7': 13028.8,
        'GPT-4o': 8943.2,
        'Gemini 2.5 Pro': 13611.2,
    },
    {
        'date': 'Sep 09',
        'Sonnet 3.7': 13412.4,
        'GPT-4o': 9932.2,
        'Gemini 2.5 Pro': 12515.2,
    },
    {
        'date': 'Sep 10',
        'Sonnet 3.7': 13649.0,
        'GPT-4o': 10139.2,
        'Gemini 2.5 Pro': 11143.8,
    },
    {
        'date': 'Sep 11',
        'Sonnet 3.7': 13748.5,
        'GPT-4o': 10441.2,
        'Gemini 2.5 Pro': 8929.2,
    },
    {
        'date': 'Sep 12',
        'Sonnet 3.7': 13148.1,
        'GPT-4o': 10933.8,
        'Gemini 2.5 Pro': 8943.2,
    },
    {
        'date': 'Sep 13',
        'Sonnet 3.7': 12839.6,
        'GPT-4o': 11073.4,
        'Gemini 2.5 Pro': 7938.3,
    },
    {
        'date': 'Sep 14',
        'Sonnet 3.7': 12428.2,
        'GPT-4o': 11128.3,
        'Gemini 2.5 Pro': 7533.4,
    },
    {
        'date': 'Sep 15',
        'Sonnet 3.7': 12012.8,
        'GPT-4o': 11412.3,
        'Gemini 2.5 Pro': 7100.4,
    },
    {
        'date': 'Sep 16',
        'Sonnet 3.7': 11801.3,
        'GPT-4o': 10501.1,
        'Gemini 2.5 Pro': 6532.1,
    },
    {
        'date': 'Sep 17',
        'Sonnet 3.7': 10102.9,
        'GPT-4o': 8923.3,
        'Gemini 2.5 Pro': 4332.8,
    },
    {
        'date': 'Sep 18',
        'Sonnet 3.7': 12132.5,
        'GPT-4o': 10212.1,
        'Gemini 2.5 Pro': 7847.4,
    },
    {
        'date': 'Sep 19',
        'Sonnet 3.7': 12901.1,
        'GPT-4o': 10101.7,
        'Gemini 2.5 Pro': 7223.3,
    },
    {
        'date': 'Sep 20',
        'Sonnet 3.7': 13132.6,
        'GPT-4o': 12132.3,
        'Gemini 2.5 Pro': 6900.2,
    },
    {
        'date': 'Sep 21',
        'Sonnet 3.7': 14132.2,
        'GPT-4o': 13212.5,
        'Gemini 2.5 Pro': 5932.2,
    },
    {
        'date': 'Sep 22',
        'Sonnet 3.7': 14245.8,
        'GPT-4o': 12163.4,
        'Gemini 2.5 Pro': 5577.1,
    },
    {
        'date': 'Sep 23',
        'Sonnet 3.7': 14328.3,
        'GPT-4o': 10036.1,
        'Gemini 2.5 Pro': 5439.2,
    },
    {
        'date': 'Sep 24',
        'Sonnet 3.7': 14949.9,
        'GPT-4o': 8985.1,
        'Gemini 2.5 Pro': 4463.1,
    },
    {
        'date': 'Sep 25',
        'Sonnet 3.7': 15967.5,
        'GPT-4o': 9700.1,
        'Gemini 2.5 Pro': 4123.2,
    },
    {
        'date': 'Sep 26',
        'Sonnet 3.7': 17349.3,
        'GPT-4o': 10943.4,
        'Gemini 2.5 Pro': 3935.1,
    },
]
