<script setup lang="ts">
const route = useRoute()

const open = ref(false)

const links = [[{
  label: 'Home',
  icon: 'i-lucide-house',
  to: '/',
  onSelect: () => {
    open.value = false
  },
}, {
  label: 'Inbox',
  icon: 'i-lucide-inbox',
  to: '/inbox',
  badge: '4',
  onSelect: () => {
    open.value = false
  },
}, {
  label: 'Listings',
  icon: 'i-lucide-building',
  // onSelect: () => {
  //   open.value = false
  // },
  defaultOpen: false,
  children: [
    {
      label: 'View All',
      // icon: 'i-lucide-building',
      to: '/listings',
      exact: true,
      onSelect: () => {
        open.value = false
      },
    },
    {
      label: 'Listing Groups',
      // icon: 'i-lucide-building-2',
      to: '/listings/groups',
      onSelect: () => {
        open.value = false
      },
    },
    {
      label: 'Guidebooks',
      // icon: 'i-lucide-book-open-text',
      to: '/guidebooks',
      onSelect: () => {
        open.value = false
      },
    },
    {
      label: 'Smart Spaces',
      // icon: 'i-lucide-scan-qr-code',
      to: '/smart-spaces',
      onSelect: () => {
        open.value = false
      },
    },
  ],
}, {
  label: 'Operations',
  icon: 'i-lucide-clipboard',
  defaultOpen: false,
  children: [
    {
      label: 'SOPs',

      to: '/sops',
      onSelect: () => {
        open.value = false
      },
    },
    {
      label: 'Tasks',

      to: '/tasks',
      onSelect: () => {
        open.value = false
      },
    },
    {
      label: 'Logs',
      // icon: 'i-lucide-logs',
      to: '/logs',
      onSelect: () => {
        open.value = false
      },
    },
    // {
    //   label: 'Analytics',
    //   // icon: 'i-lucide-file-text',
    //   to: '/analytics',
    //   onSelect: () => {
    //     open.value = false
    //   },
    // },
  ],
}, {
  label: 'Growth',
  icon: 'i-lucide-pie-chart',
  defaultOpen: false,
  children: [{
    label: 'Contacts',
    icon: 'i-lucide-users',
    to: '/contacts',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Workflows',
    icon: 'i-lucide-workflow',
    to: '/workflows',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Campaigns',
    icon: 'i-lucide-megaphone',
    to: '/campaigns',
    onSelect: () => {
      open.value = false
    },
  }],
}, {
  label: 'Website',
  icon: 'i-lucide-globe',
  defaultOpen: false,
  children: [{
    label: 'Manage Widget',
    icon: 'i-lucide-grid',
    to: '/widget',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Popups',
    icon: 'i-lucide-message-square-share',
    to: '/widget/popups',
    onSelect: () => {
      open.value = false
    },
  }],
}, {
  label: 'Integrations',
  icon: 'i-lucide-plug',
  to: '/settings/integrations',
  onSelect: () => {
    open.value = false
  },
}, {
  label: 'Settings',
  icon: 'i-lucide-settings',
  defaultOpen: false,
  children: [{
    label: 'Profile',
    to: '/settings/profile',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Notifications',
    to: '/settings/notifications',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Billing',
    to: '/settings/billing',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Agents',
    to: '/settings/agents',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Brand',
    to: '/settings/brand',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Listings',
    to: '/settings/listings',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'Buy Time',
    to: '/settings/buytime',
    onSelect: () => {
      open.value = false
    },
  }, {
    label: 'AI',
    to: '/settings/ai',
    onSelect: () => {
      open.value = false
    },
  }],
}], [
  {
    label: 'Notifications',
    icon: 'i-lucide-bell',
    to: 'https://github.com/nuxt-ui-pro/dashboard',
    onSelect: () => {
      open.value = false
    },
  },
  {
    label: 'Copilot',
    icon: 'i-lucide-sparkles',
    to: 'https://github.com/nuxt-ui-pro/dashboard',
    onSelect: () => {
      open.value = false
    },
  },
  {
    label: 'Help & Support',
    icon: 'i-lucide-info',
    to: 'https://yada.ai/kb/how-to-get-started-with-yada',
    target: '_blank',
  },
]]

const groups = computed(() => [{
  id: 'links',
  label: 'Go to',
  items: links.flat(),
}, {
  id: 'code',
  label: 'Code',
  items: [{
    id: 'source',
    label: 'View page source',
    icon: 'i-simple-icons-github',
    to: `https://github.com/nuxt-ui-pro/dashboard/blob/main/app/pages${route.path === '/' ? '/index' : route.path}.vue`,
    target: '_blank',
  }],
}])
</script>

<template>
  <UDashboardGroup unit="rem">
    <UDashboardSidebar id="default" v-model:open="open" collapsible resizable class="bg-(--ui-bg-elevated)/25"
      :ui="{ footer: 'lg:border-t lg:border-(--ui-border)' }">
      <template #header="{ collapsed }">
        <TeamsMenu :collapsed="collapsed" />
      </template>

      <template #default="{ collapsed }">
        <UDashboardSearchButton :collapsed="collapsed" class="bg-transparent ring-(--ui-border)" />

        <UNavigationMenu :collapsed="collapsed" :items="links[0]" orientation="vertical" />

        <UNavigationMenu :collapsed="collapsed" :items="links[1]" orientation="vertical" class="mt-auto" />
      </template>

      <template #footer="{ collapsed }">
        <UserMenu :collapsed="collapsed" />
      </template>
    </UDashboardSidebar>

    <UDashboardSearch :groups="groups" />

    <slot />

    <NotificationsSlideover />
  </UDashboardGroup>
</template>
