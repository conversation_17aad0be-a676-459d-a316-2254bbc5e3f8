import Chargebee from 'chargebee'

const isProd = process.env.NODE_ENV === 'production'
const chargebee = new Chargebee({
  site: isProd ? process.env.CHARGEBEE_SITE_PROD as string : process.env.CHARGEBEE_SITE_DEV as string,
  apiKey: isProd ? process.env.CHARGEBEE_API_KEY_PROD as string : process.env.CHARGEBEE_API_KEY_DEV as string,
})

async function loadSubscription(user: any): Promise<any> {
  const sub: any = null

  if (user?.billing?.customer?.id) {
    try {
      const result = await chargebee.subscription.list({
        customer_id: { is: user.billing.customer.id },
      })
      result.list.forEach((entry) => {
      })
      return result.list[0].subscription
    }
    catch (err) {
      console.error('Error fetching subscriptions:', err)
    }
  }
  return sub
}

async function loadInvoices(user: any): Promise<any> {
  const customerId = user?.billing?.customer?.id
  if (!customerId) {
    return []
  }
  try {
    const response = await chargebee.invoice.list({
      customer_id: { is: user.billing.customer.id },
    })

    if (response.list.length === 0) {
      return []
    }

    // Map through the results to extract useful invoice details
    const invoices = response.list.map(item => item.invoice)

    return invoices
  }
  catch (error) {
    console.warn('Error fetching invoices:', error)
    return []
  }
}

async function loadCustomer(user: any): Promise<any> {
  const customerId = user?.billing?.customer?.id
  if (!customerId) {
    return null
  }
  try {
    const response = await chargebee.customer.retrieve(customerId)

    const customer = response.customer
    return customer
  }
  catch (error) {
    console.error('Error retrieving customer:', error)
    return null
  }
}

async function loadPaymentMethod(user: any): Promise<any> {
  const customerId = user?.billing?.customer?.id
  if (!customerId) {
    return null
  }
  try {
    const response = await chargebee.paymentSource.list({
      customer_id: { is: customerId },
    })

    if (response.list.length === 0) {
      return []
    }

    // Map through the results to extract useful information
    const paymentMethods = response.list.map(item => item.payment_source)

    return paymentMethods
  }
  catch (error) {
    console.error('Error fetching payment methods:', error)
    return null
  }
}

async function createCustomer(firstName: string, lastName: string, email: string, address: string, city: string, state: string, postalCode: string, country: string): Promise<any> {
  try {
    const result = await chargebee.customer.create({
      first_name: firstName,
      last_name: lastName,
      email,
      billing_address: {
        first_name: firstName,
        last_name: lastName,
        line1: address,
        city,
        state,
        zip: postalCode,
        country,
      },
    })
    return result.customer
  }
  catch (err) {
    console.error('Unable to create customer', { error: err })
  }
}

async function createPaymentMethod(customerId: string, token: string): Promise<any> {
  try {
    const result = await chargebee.paymentSource.createUsingToken({
      customer_id: customerId,
      token_id: token,
      replace_primary_payment_source: true,
    })
    return result.customer
  }
  catch (err) {
    console.error('Unable to create payment method', { error: err })
  }
}

async function createSubscription(customerId: string, numListings: number, annual: any): Promise<any> {
  const hostMonthly = 'yada-host-2023-USD-Monthly'
  const hostYearly = 'yada-host-2023-USD-Yearly'

  let plan
  if (annual) {
    plan = hostYearly
  }
  else {
    plan = hostMonthly
  }
  try {
    const subscription = await chargebee.subscription.createWithItems(customerId, {
      subscription_items: [
        {
          item_price_id: plan,
          quantity: numListings,
        },
      ],
    })
    return subscription.subscription
  }
  catch (e) {
    console.error('Unable to create the subscription', {
      error: e,
    })
  }
}

async function updateSubscription(subscription: any, newQuantity: number): Promise<any> {
  try {
    const sub = await chargebee.subscription.updateForItems(subscription.id, {
      subscription_items: [
        {
          item_price_id: subscription.subscription_items[0].item_price_id,
          quantity: newQuantity,
        },
      ],
    })
    return sub.subscription
  }
  catch (e) {
    console.error('Unable to update the subscription', {
      error: e,
    })
  }
}

export {
  createCustomer,
  createPaymentMethod,
  createSubscription,
  loadCustomer,
  loadInvoices,
  loadPaymentMethod,
  loadSubscription,
  updateSubscription,
}
