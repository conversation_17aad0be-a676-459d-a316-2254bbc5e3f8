import { firestore } from '~/helpers/firebase';
import { calryReservationStatuses, calryChannelTypes } from '~/shared/enums';

export async function handleReservationUpdated(body: any) {
    console.log('Handling reservation.updated event');
    console.log('Reservation data:', JSON.stringify(body, null, 2));

    try {
        const reservationData = body?.data?.record;

        if (!reservationData) {
            console.error('No reservation record found in webhook data');
            return { success: false, error: 'No reservation record found' };
        }

        console.log('Property ID:', reservationData.propertyId);
        console.log('Room type IDs:', reservationData.roomTypeIds?.[0] || reservationData.propertyId);

        // Find user, listing, existing contact and stay
        const [userSnap, listingSnap, contactSnap, staySnap] = await Promise.all([
            firestore.collection('users').where(`integrations.calry.${body?.data?.integrationAccountId}`, '!=', null).get(),
            firestore.collection('listings')
                .where('calryPropertyId', '==', `${reservationData.propertyId}`)
                .where('calryRoomTypeId', '==', `${reservationData.roomTypeIds?.[0] || reservationData.propertyId}`)
                .get(),
            firestore.collection('contacts')
                .where('calryId', '==', `${reservationData.primaryGuest?.id}`)
                .get(),
            firestore.collection('stays')
                .where('calryId', '==', `${reservationData.id}`)
                .get()
        ]);

        // Validate user and listing exist
        if (userSnap.empty) {
            console.error(`No user found for integration: integrations.calry.${body?.data?.integrationAccountId}`);
            return { success: false, error: 'User not found' };
        }

        if (listingSnap.empty) {
            console.error(`No listing found for Calry Property ID: ${reservationData.propertyId}`);
            return { success: false, error: 'Listing not found' };
        }

        // Extract user and listing data
        const user = { ...userSnap.docs[0].data(), id: userSnap.docs[0].id };
        const listing = { ...listingSnap.docs[0].data(), id: listingSnap.docs[0].id };

        console.log(`User found: ${user.id}, Listing found: ${listing.id}`);

        // Convert arrival and departure dates to millisecond Unix timestamps
        const arrivalDate = reservationData.arrivalDate;
        const departureDate = reservationData.departureDate;

        if (!arrivalDate || !departureDate) {
            console.error('Missing arrival or departure date in reservation data');
            return { success: false, error: 'Missing arrival or departure date' };
        }

        const checkInTimestamp = new Date(arrivalDate).getTime();
        const checkOutTimestamp = new Date(departureDate).getTime();

        // Prepare contact payload
        const contactPayload = {
            userId: user.id,
            calryId: String(reservationData.primaryGuest?.id || ''),
            email: reservationData.primaryGuest?.emails?.[0] || '',
            firstName: reservationData.primaryGuest?.nameFirst || '',
            lastName: reservationData.primaryGuest?.nameLast || '',
            fullName: reservationData.primaryGuest?.name || '',
            lastActive: Date.now(),
            lastActivityAt: Date.now(),
            notes: [],
            phone: (reservationData.primaryGuest?.mobileNumbers?.[0] || '').replace(/\s/g, ''),
            sources: ['calry']
        };

        // Prepare stay payload
        const stayPayload = {
            userId: user.id,
            calryId: String(reservationData.id || ''),
            checkIn: checkInTimestamp,
            checkOut: checkOutTimestamp,
            totalPaid: reservationData.finances?.totalPaid || reservationData.totalPrice || 0,
            listingId: listing.id,
            status: calryReservationStatuses[reservationData.status as keyof typeof calryReservationStatuses] || 'unknown',
            channel: calryChannelTypes[reservationData.source as keyof typeof calryChannelTypes] || 'unknown',
        };

        console.log('Contact payload:', contactPayload);
        console.log('Stay payload:', stayPayload);

        let contactRef;
        let contactId;

        // Handle contact creation or update
        if (contactSnap.empty) {
            // Create new contact
            contactRef = await firestore.collection('contacts').add(contactPayload);
            contactId = contactRef.id;
            console.log('Contact created with ID:', contactId);
        } else {
            // Update existing contact with only specific fields
            const existingContact = contactSnap.docs[0];
            contactRef = existingContact.ref;
            contactId = existingContact.id;

            const updatePayload = {
                firstName: contactPayload.firstName,
                lastName: contactPayload.lastName,
                fullName: contactPayload.fullName,
                phone: contactPayload.phone,
                email: contactPayload.email,
                lastActive: Date.now(),
                lastActivityAt: Date.now(),
            };

            console.log('Contact update payload:', updatePayload);

            await contactRef.update(updatePayload);
            console.log('Contact updated with ID:', contactId);
        }

        let stayRef;
        let stayId;

        // Handle stay creation or update
        if (staySnap.empty) {
            // Create new stay with contact reference
            const finalStayPayload = {
                ...stayPayload,
                contactId: contactId
            };

            stayRef = await firestore.collection('stays').add(finalStayPayload);
            stayId = stayRef.id;
            console.log('Stay created with ID:', stayId);

            // Update contact to include the stay ID in the stayIds array
            const existingStayIds = contactSnap.empty ? [] : (contactSnap.docs[0].data().stayIds || []);
            await contactRef.update({
                stayIds: [...existingStayIds, stayId]
            });
        } else {
            // Update existing stay with only specific fields
            const existingStay = staySnap.docs[0];
            stayRef = existingStay.ref;
            stayId = existingStay.id;

            const stayUpdatePayload = {
                checkIn: stayPayload.checkIn,
                checkOut: stayPayload.checkOut,
                totalPaid: stayPayload.totalPaid,
                status: stayPayload.status,
                contactId: contactId
            };

            console.log('Stay update payload:', stayUpdatePayload);

            await stayRef.update(stayUpdatePayload);
            console.log('Stay updated with ID:', stayId);

            // Always ensure contact has this stay in stayIds array (sync relationship)
            const existingContact = contactSnap.empty ? null : contactSnap.docs[0];
            const existingStayIds = existingContact?.data().stayIds || [];

            if (!existingStayIds.includes(stayId)) {
                await contactRef.update({
                    stayIds: [...existingStayIds, stayId]
                });
                console.log('Added stayId to contact stayIds array');
            }
        }

        console.log('Successfully processed contact and stay with proper relationships');
        console.log('Contact ID:', contactId, 'Stay ID:', stayId);

        return {
            success: true,
            contactId,
            stayId,
            message: 'Contact and stay processed successfully'
        };

    } catch (error) {
        console.error('Error handling reservation.updated event:', error);
        return { success: false, error: 'Failed to process reservation update' };
    }
}
