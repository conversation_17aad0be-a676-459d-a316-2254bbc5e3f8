import axios from 'axios'
import { listingAmenities } from '../../shared/listingAmenities'

// Interface for the amenity object structure
interface AmenityObject {
  internalId: string
  title: string
  text: string
  showInGuidebook: boolean
  requireConfirmedReservation: boolean
  private: boolean
  tags: string[]
  photos: any[]
  videos: any[]
  template: string
  type: string
  amenityAvailable: boolean
  amenityKnown: boolean
  metadata: {
    updatedAt: number
    updatedBy: string
  }
}

class Calry {
  calryAccountId: string

  constructor(calryAccountId: string) {
    console.log('Initializing Calry service with account ID:', calryAccountId)
    this.calryAccountId = calryAccountId
  }

  async getAccount() {
    console.log('Fetching Calry account with ID:', this.calryAccountId)
    const url = `https://dev.calry.app/api/v1/integration_account/${this.calryAccountId}`
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
        },
      })
      return response.data
    }
    catch (error) {
      console.error('Error fetching Calry account:', error)
      // Handle specific error codes (400, 401) if needed
      throw error // Re-throw the error for the caller to handle
    }
  }

  async setupWebhooks() {
    const url = 'https://dev.calry.app/api/v1/integration_account_webhook'
    const body = {
      name: 'Yada Webhook',
      receiverUrl: 'https://nuxt-yada-ai.vercel.app/api/webhooks/calry/pms',
      integrationAccountId: this.calryAccountId,
      enabled: true,
      eventSubscription: {
        'reservation.created': true,
        'reservation.updated': true,
        'reservation.deleted': true,
        'conversation.updated': true,
        'property.updated': true,
        'property.deleted': true,
        'calendar.updated': true,
      },
    }

    try {
      const response = await axios.post(url, body, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
        },
      })
      return response.data
    }
    catch (error) {
      console.error('Error setting up Calry webhooks:', error)
      throw error
    }
  }

  async getAllProperties() {
    const url = 'https://dev.calry.app/api/v2/vrs/properties'
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
          workspaceId: process.env.CALRY_WORKSPACE_ID,
          integrationAccountId: this.calryAccountId,
        },
      })
      return response.data.data
    }
    catch (error) {
      console.error('Error fetching Calry listings:', error)
      throw error
    }
  }

  async getRoomTypesForProperty(propertyId: string) {
    const url = `https://dev.calry.app/api/v2/vrs/room-types/${propertyId}`
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
          workspaceId: process.env.CALRY_WORKSPACE_ID,
          integrationAccountId: this.calryAccountId,
        },
      })
      return response.data.data
    }
    catch (error) {
      console.error('Error fetching Calry room types:', error)
      throw error
    }
  }

  /**
   * Generates amenity objects for OwnerRez amenities
   * @param amenities Array of amenities from OwnerRez with {name: string} structure
   * @returns Record of amenity objects with keys in the format 'amenity_{id}'
   */
  initOwnerrezAmenities(amenities: Array<{ name: string }>): Record<string, AmenityObject> {
    // Use the imported global amenities list

    // Create a map of amenity names for easier lookup
    const amenityNameMap = new Map()
    amenities.forEach((amenity) => {
      if (amenity.name) {
        amenityNameMap.set(amenity.name, true)
      }
    })

    // Generate amenities array based on the global list
    const result: Record<string, AmenityObject> = {}

    listingAmenities.forEach((amenity) => {
      // Check if this amenity is known in OwnerRez
      const isKnown = Array.isArray(amenity.ownerrezAmenityNames)
        && amenity.ownerrezAmenityNames.length > 0

      // Check if this amenity is available in the provided amenities
      const isAvailable = (isKnown
        && amenity.ownerrezAmenityNames?.some(name => amenityNameMap.has(name))) || false

      // Create the amenity object with the required structure
      result[`amenity_${amenity.id}`] = {
        internalId: amenity.id,
        title: amenity.name,
        text: '',
        showInGuidebook: true,
        requireConfirmedReservation: false,
        private: false,
        tags: amenity.tags || [],
        photos: [],
        videos: [],
        template: 'amenity',
        type: 'default',
        amenityAvailable: isAvailable,
        amenityKnown: isKnown,
        metadata: {
          updatedAt: Date.now(),
          updatedBy: 'system',
        },
      }
    })

    return result
  }

  async sendMessage(reservationId: string, conversationId: string, body: string, type: string) {
    console.log('Sending message to Calry conversation:', { reservationId, conversationId, type })
    const url = `https://dev.calry.app/api/v2/vrs/conversations`

    try {
      const response = await axios.post(url, {
        reservationId,
        conversationId,
        body,
        type,
      }, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
          workspaceId: process.env.CALRY_WORKSPACE_ID,
          integrationAccountId: this.calryAccountId,
        },
      })
      return response.data
    }
    catch (error) {
      console.error('Error sending message to Calry:', error)
      // Handle specific error codes if needed
      throw error // Re-throw the error for the caller to handle
    }
  }

  async getReservation(reservationId: string) {
    const url = `https://dev.calry.app/api/v2/vrs/reservations/${reservationId}`
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
          workspaceId: process.env.CALRY_WORKSPACE_ID,
          integrationAccountId: this.calryAccountId,
        },
      })
      return response.data
    } catch (error) {
      console.error('Error fetching Calry reservation:', error)
      // Handle specific error codes if needed
      throw error // Re-throw the error for the caller to handle
    }
  }

  async getConversation(conversationId: string) {
    const url = `https://dev.calry.app/api/v2/vrs/conversations/${conversationId}`
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${process.env.CALRY_API_KEY}`,
          workspaceId: process.env.CALRY_WORKSPACE_ID,
          integrationAccountId: this.calryAccountId,
        },
      })
      return response.data.data
    } catch (error) {
      console.error('Error fetching Calry conversation:', error)
      // Handle specific error codes if needed
      throw error // Re-throw the error for the caller to handle
    }
  }
}

export default Calry

export { handleConversationUpdated } from './conversationUpdatedHandler'
export { handleReservationCreated } from './reservationCreatedHandler'
export { handleReservationUpdated } from './reservationUpdatedHandler'
