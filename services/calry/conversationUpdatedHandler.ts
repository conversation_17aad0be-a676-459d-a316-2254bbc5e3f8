import { firestore } from '~/helpers/firebase'
import { scheduleQstash } from '~/services/upstash'
import { Redis } from "@upstash/redis"
import { convertCalryToAiMessages } from '~/services/yada'
import { detectRoute, query } from '~/services/ai'
import { createOpenAI } from '@ai-sdk/openai'
import { generateText } from 'ai'
import { getAccuracy, getRelevance, getCitations } from '~/services/ai'
import Calry from '~/services/calry'




async function processMessage(message: any, firestoreConversationId: string, calryConversationId: string, userId: string, redis: Redis) {
    // Set the message as processed in Redis (using Calry conversation ID for consistency)
    await redis.set(`${userId}:${calryConversationId}:${message.id}`, 'processed'); // 24 hours TTL
    console.log('Message marked as processed in Redis:', message.id);

    // Store message in Firestore (using Firestore conversation ID)
    await firestore.collection(`conversations/${firestoreConversationId}/messages`).doc(message.id).set({
        conversationId: firestoreConversationId,
        userId: userId,
        body: message.body || '',
        createdAt: message.createdAt ? new Date(message.createdAt).getTime() : Date.now(),
        type: message.senderType === 'Owner' ? 'us' : 'them',
        yadaSent: false,
        sent: message.senderType === 'Owner' ? true : false,
    }, { merge: true });

    console.log('Message processed and stored in Firestore and Redis:', message.id);
}

export async function handleConversationUpdated(body: any) {

    const redis = new Redis({
        url: process.env.CALRY_MESSAGES_REDIS_URL,
        token: process.env.CALRY_MESSAGES_REDIS_PASSWORD,
    })

    const calry = new Calry(body.data.integrationAccountId)

    const userSnap = await firestore.collection('users')
        .where(`integrations.calry.${body.data.integrationAccountId}`, '!=', null)
        .get();
    if (userSnap.empty) {
        console.error(`No user found for integration: integrations.calry.${body.data.integrationAccountId}`);
        return;
    }
    const user = { ...userSnap.docs[0].data(), id: userSnap.docs[0].id };
    console.log('User found:', user.id);

    // Run all queries in parallel: user, conversation, stay, contact
    const [conversationSnap, staySnap, contactSnap, listingSnap] = await Promise.all([
        firestore.collection('conversations').where('userId', '==', user.id).where('calryId', '==', body.data.record.id).get(),
        firestore.collection('stays').where('userId', '==', user.id).where('calryId', '==', `${body.data.record.reservationId[0]}`).get(),
        firestore.collection('contacts').where('userId', '==', user.id).where('calryId', '==', `${body.data.record.recipient.id}`).get(),
        firestore.collection('listings').where('userId', '==', user.id).where('calryPropertyId', '==', `${body.data.record.propertyId[0]}`).where('calryRoomTypeId', '==', `${body.data.record.roomTypeId[0]}`).get()
    ]);

    /**
     * Step 1 - Sync stays + contacts
     */

    await (
        scheduleQstash('https://nuxt-yada-ai.vercel.app/api/qstash/sync-calry-reservation', {
            userId: user.id,
            integrationAccountId: body.data.integrationAccountId,
            calryReservationId: body.data.record.reservationId[0]
        }, 5)
    );

    let conversationId: string;

    if (conversationSnap.empty) {
        console.log('need to write conversation')
        // Create a new conversation if it doesn't exist
        const conversationPayload = {
            ai: false,
            userId: user.id,
            calryId: body.data.record.id,
            contactId: contactSnap.empty ? null : contactSnap.docs[0].id,
            contactName: body.data.record.recipient.name || '',
            stayId: staySnap.empty ? null : staySnap.docs[0].id,
            address: '',
            integrationChannel: 'calry',
            isRead: false,
            isStarred: false,
            lastActivityAt: Date.now(),
            lastMessage: {
                "body": body.data.record.messages[0].body || '',
                "createdAt": body.data.record.messages[0].createdAt ? new Date(body.data.record.messages[0].createdAt).getTime() : Date.now(),
                "type": body.data.record.messages[0].senderType === 'Owner' ? 'us' : 'them',
                "yadaSent": false
            },
            listingId: listingSnap.empty ? null : listingSnap.docs[0].data().id,
            listingName: listingSnap.empty ? '' : listingSnap.docs[0].data().name,
        }

        const res = await firestore.collection('conversations').add(conversationPayload);
        conversationId = res.id;

        console.log('New conversation created with ID:', conversationId);
    } else {
        // Use existing conversation ID
        conversationId = conversationSnap.docs[0].id;
        console.log('Using existing conversation with ID:', conversationId);
    }

    try {
        await scheduleQstash('https://nuxt-yada-ai.vercel.app/api/qstash/enrich-calry', {
            userId: user.id,
            conversationId: conversationId,
            calryReservationId: body.data.record.reservationId[0],
            calryGuestId: body.data.record.recipient.id,
        }, 10)
    } catch (error) {
        console.warn('unable to start the enrichment code')

    }

    // Check Redis for each message in the messages array (using Calry conversation ID for Redis consistency)
    const messages = body.data.record.messages || [];
    const redisChecks = await Promise.all(
        messages.map(async (message: any) => {
            const msgRedis = await redis.get(`${user.id}:${body.data.record.id}:${message.id}`);
            return {
                messageId: message.id,
                processed: !!msgRedis
            };
        })
    );

    // Filter out already processed messages
    const unprocessedMessages = messages.filter((message: any, index: number) =>
        !redisChecks[index].processed
    );

    if (unprocessedMessages.length === 0) {
        console.log('All messages already processed, checking if chatbot processing needed for conversation:', conversationId);
    } else {
        console.log(`Found ${unprocessedMessages.length} unprocessed messages out of ${messages.length} total messages`);

        // Process all unprocessed messages in parallel
        await Promise.all(
            unprocessedMessages.map(async (message: any) => {
                return processMessage(message, conversationId, body.data.record.id, user.id, redis);
            })
        );
    }

    // Check if listing is active before proceeding with chatbot logic
    if (!listingSnap.empty && !listingSnap.docs[0].data().active) {
        console.log('Listing is not active, skipping chatbot processing for conversation:', listingSnap.docs[0].id);
        return;
    }

    // Check if the first message in the conversation is from a guest (not owner)
    const firstMessage = messages[0];
    if (firstMessage.senderType === 'Owner') {
        return
    }
    console.log('First message is from a guest, proceeding with chatbot processing for conversation:', conversationId);


    // Generate AI messages array from the conversation messages
    const aiMessages = convertCalryToAiMessages(messages);

    /**
     * Check the rules and decide if we need to run the chatbot.
     */
    const apiKey = useRuntimeConfig().openaiApiKey
    if (!apiKey)
        throw new Error('Missing OpenAI API key')
    const openai = createOpenAI({
        apiKey,
    })

    /**
     * Generate a chatbot response
     */

    const route = await detectRoute(aiMessages[aiMessages.length - 1].content, aiMessages.map(msg => msg.content));

    console.log('route: ', route)

    switch (route) {
        case 'smalltalk': {
            const { text } = await generateText({
                model: openai('gpt-4o-mini'),
                messages: aiMessages,
                system: `You are a helpful assistant. Your job is to respond to chitchat type messages. Please do so succinctly and in the language of the incoming message.`,
            })
            break
        }
        case 'faq': {
            // You are a friendly Airbnb host assistant. Provide your complete, helpful response to the user in a warm, conversational tone. Do not add any unnecessarry fluff or ask any follow up questions.

            const context = await query('listing', `listingId = '${listingSnap.docs[0].id}'`, aiMessages[aiMessages.length - 1].content)
            const contextString = context.map((c: any) => c.text).join(' ')
            const { text } = await generateText({
                model: openai('gpt-4.1-mini'),
                messages: aiMessages,
                // system: `You are a customer support agent for an Airbnb host, helping guests by following directives and answering questions. You are friendly, empathetic, and helpful, but you don't use emojis. Respond only in English unless the message is obviously not in English. Follow these steps to generate your response: 1. Recursively break down the message into smaller questions/statements. 2 For each atomic question/statement: 2a. Select the most relevant information from the context, considering the conversation history. 2b. Add only extremely relevant content from the context even if it is not directly asked but enhances the guest's experience. 3. Generate a draft response using the selected information. 4. Remove duplicate content from the draft response. 5. Improve accuracy, clarity, and friendliness before finalizing the response. Ensure that responses provide all relevant details available in the context. 6. Only show the final response. Do not provide explanations or details. CONTEXT: ${context}`,
                system: `You are a friendly hotel and airbnb hosts assistant. Answer using only the context. Make your response warm and friendly! Context: ${contextString}`,
                temperature: 0.2,
            })

            console.log('Context retrieved for FAQ chatbot:', context)
            console.log('FAQ chatbot response:', text)

            const [relevance, accuracy, citations] = await Promise.all([
                getRelevance(
                    aiMessages[aiMessages.length - 1].content,
                    text,
                    contextString
                ),
                getAccuracy(
                    aiMessages[aiMessages.length - 1].content,
                    text,
                    contextString
                ),
                getCitations(
                    text,
                    aiMessages[aiMessages.length - 1].content,
                    context.map((c: any) => ({ id: c.id, text: c.text }))
                )
            ])

            console.log('Relevance:', relevance, 'Accuracy:', accuracy, 'Citations:', citations);

            if (accuracy > 0.7 && relevance > 0.7) {
                console.log('Let us send the response back to the OTA via Calry via PMS');
                try {
                    const res = await calry.sendMessage(body.data.record.reservationId[0], body.data.record.id, text, messages[0].type || 'CHANNEL')
                    console.log('Response sent to Calry:', res);
                    await redis.set(`${user.id}:${res.data.conversationId}:${res.data.messageId}`, 'processed');
                    await firestore.collection(`conversations/${conversationId}/messages`).doc(res.data.messageId).set({
                        conversationId: conversationId,
                        userId: user.id,
                        body: text || '',
                        createdAt: res.data.createdAt ? new Date(res.data.createdAt).getTime() : Date.now(),
                        type: 'us',
                        yadaSent: true,
                        sent: true,
                        sender: 'autopilot'
                    }, { merge: true });
                } catch (error) {
                    console.error('Error sending response to Calry:', error);
                }
            }

            break
        }
        default: {
            const { text } = await generateText({
                model: openai('gpt-4o-mini'),
                messages: aiMessages,
                system: `You are a helpful assistant. Check your knowledge base before answering any questions. Only respond to questions using information from tool calls. If no relevant information is found in the tool calls, respond, "Sorry, I don't know."`,
            })
            break
        }
    }

    return;
}