import { firestore } from '~/helpers/firebase';
import { calryReservationStatuses, calryChannelTypes } from '~/shared/enums';

export async function handleReservationCreated(body: any) {
    console.log('Handling reservation.created event');

    console.log('property id', body?.data?.record?.propertyId);
    console.log('room type ids', body?.data?.record?.roomTypeIds?.[0] || body?.data?.record?.propertyId);

    const [userSnap, listingSnap] = await Promise.all([
        firestore.collection('users').where(`integrations.calry.${body?.data?.integrationAccountId}`, '!=', null).get(),
        firestore.collection('listings').where('calryPropertyId', '==', `${body?.data?.record?.propertyId}`).where('calryRoomTypeId', '==', `${body?.data?.record?.roomTypeIds?.[0] || body?.data?.record?.propertyId}`).get(),
    ]);

    // Validate both user and listing exist
    if (userSnap.empty) {
        console.error(`No user found for integration: integrations.calry.${body?.data?.integrationAccountId}`);
        return;
    }

    if (listingSnap.empty) {
        console.error(`No listing found for Calry Property ID: ${body?.data?.record?.propertyId}`);
        return;
    }

    // Extract user and listing data
    const user = { ...userSnap.docs?.[0]?.data(), id: userSnap.docs?.[0]?.id };
    const listing = { ...listingSnap.docs?.[0]?.data(), id: listingSnap.docs?.[0]?.id };

    console.log(`User found: ${user.id}, Listing found: ${listing.id}`);

    // Convert arrival and departure dates to millisecond Unix timestamps
    const arrivalDate = body?.data?.record?.arrivalDate;
    const departureDate = body?.data?.record?.departureDate;

    if (!arrivalDate || !departureDate) {
        console.error('Missing arrival or departure date in reservation data');
        return;
    }

    const checkInTimestamp = new Date(arrivalDate).getTime();
    const checkOutTimestamp = new Date(departureDate).getTime();

    const stayPayload = {
        userId: user.id,
        calryId: String(body?.data?.record?.id || ''),
        checkIn: checkInTimestamp,
        checkOut: checkOutTimestamp,
        totalPaid: body?.data?.record?.totalPrice || 0,
        listingId: listing.id,
        status: calryReservationStatuses[body?.data?.record?.status as keyof typeof calryReservationStatuses] || 'unknown',
        channel: calryChannelTypes[body?.data?.record?.channel as keyof typeof calryChannelTypes] || 'unknown',
    };

    console.log('Stay payload:', stayPayload);

    const contactPayload = {
        userId: user.id,
        calryId: String(body?.data?.record?.primaryGuest?.id || ''),
        email: body?.data?.record?.primaryGuest?.emails?.[0] || '',
        firstName: body?.data?.record?.primaryGuest?.nameFirst || '',
        lastName: body?.data?.record?.primaryGuest?.nameLast || '',
        fullName: body?.data?.record?.primaryGuest?.name || '',
        lastActive: Date.now(),
        lastActivityAt: Date.now(),
        notes: [],
        phone: body?.data?.record?.primaryGuest?.mobileNumbers?.[0] || '',
        sources: ['calry']
    }

    console.log('Contact payload:', contactPayload);

    try {
        // First, create the contact document
        const contactRef = await firestore.collection('contacts').add(contactPayload);
        console.log('Contact created with ID:', contactRef.id);

        // Update the stay payload with the contact ID
        const finalStayPayload = {
            ...stayPayload,
            contactId: contactRef.id
        };

        // Create the stay document
        const stayRef = await firestore.collection('stays').add(finalStayPayload);
        console.log('Stay created with ID:', stayRef.id);

        // Update the contact to include the stay ID in the stayIds array
        await contactRef.update({
            stayIds: [stayRef.id]
        });

        console.log('Successfully created contact and stay with proper relationships');
        console.log('Contact ID:', contactRef.id, 'Stay ID:', stayRef.id);

    } catch (error) {
        console.error('Error creating contact and stay:', error);
        throw error;
    }
}
