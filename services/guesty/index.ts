import axios from 'axios'
import { firestore } from '~/helpers/firebase'

class Guesty {
    private readonly apiOptions: {
        baseURL: string
        headers: {
            'Accept': string
            'Content-Type': string
            'Authorization': string
        }
    }

    private readonly userId?: string

    constructor(accessToken: string, userId?: string) {
        this.userId = userId
        this.apiOptions = {
            baseURL: 'https://partners.guesty.com/v1',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`,
            },
        }
    }

    public async heartbeat(): Promise<boolean> {
        try {
            await axios.get('accounts/me', this.apiOptions)
            await this.updateTokenStatus(true)
            return true
        }
        catch (error) {
            console.warn('Unable to interact with Guesty API', { error })
            await this.updateTokenStatus(false)
            return false
        }
    }

    private async updateTokenStatus(isValid: boolean): Promise<void> {
        if (!this.userId)
            return
        try {
            await firestore.doc(`users/${this.userId}`).update({
                'integrations.guesty.validToken': isValid,
            })
        }
        catch (error) {
            console.error('Error updating Firestore document', { error })
        }
    }

    public async sendMessage(
        conversationId: string,
        message: string,
        sender = 'inbox',
    ): Promise<string | undefined> {
        let posts: any[]
        try {
            const response = await axios.get(
                `communication/conversations/${conversationId}/posts?limit=10`,
                this.apiOptions,
            )
            posts = response.data.data.posts
        }
        catch (e) {
            console.error('unable to get posts', { error: e })
            return
        }
        if (!posts.length)
            return

        // Find the first guest post's module type, fallback to 'email'
        const guestPost = posts.find(post => post.sentBy === 'guest')
        const moduleType = guestPost?.module?.type || 'email'

        const data = {
            module: { type: moduleType },
            body: message,
        }

        let res: any
        try {
            const response = await axios.post(
                `communication/conversations/${conversationId}/send-message`,
                data,
                this.apiOptions,
            )
            res = response.data.data
        }
        catch (e) {
            console.error('unable to send message', { error: e })
            return
        }

        try {
            await firestore.collection(`conversations/${conversationId}/messages`).doc(res._id).set({
                id: res._id,
                conversationId,
                body: message,
                createdAt: res.createdAt ? new Date(res.createdAt).getTime() : Date.now(),
                type: 'us',
                sent: true,
                didYadaSend: true,
                sender,
            })
        }
        catch (error) {
            console.error('Error saving message to Firestore', { error })
            return
        }
        return res._id
    }
}

export { Guesty }
