/**
 * 
 * @param calryMessages Convert calry messages to AI core message format
 * @returns 
 */
export function convertCalryToAiMessages(calryMessages: any[]): Array<{ role: 'user' | 'assistant'; content: string }> {
    return calryMessages
        .filter(msg => msg.body && msg.body.trim()) // Filter out empty messages
        .map(msg => ({
            role: msg.senderType === 'Guest' ? 'user' as const : 'assistant' as const,
            content: msg.body
        }))
        .sort((a, b) => {
            // Sort by createdAt if available, otherwise maintain original order
            const aTime = calryMessages.find(m => m.body === a.content)?.createdAt;
            const bTime = calryMessages.find(m => m.body === b.content)?.createdAt;
            if (aTime && bTime) {
                return new Date(aTime).getTime() - new Date(bTime).getTime();
            }
            return 0;
        });
}