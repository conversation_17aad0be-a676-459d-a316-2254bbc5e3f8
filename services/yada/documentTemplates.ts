import { listingFaqs } from '~/shared/listingFaqsTags'

/**
 * Initializes the faqs object with default values for each FAQ defined in listingFaqsTags.ts
 *
 * This function creates a structured object where each key follows the format 'faq_{id}'
 * and contains all the necessary properties for a FAQ in the system.
 *
 * @returns Record<string, any> - An object with keys in the format 'faq_{id}' containing FAQ objects
 *
 * @example
 * // Initialize faqs object
 * const faqs = initFaqs();
 *
 * // Use in a listing
 * const listing = {
 *   // ... other properties
 *   faqs: faqs,
 *   // ... other properties
 * };
 *
 * // Access a specific FAQ
 * const checkInFaq = faqs['faq_checkingIn'];
 */
export function initFaqs(): Record<string, any> {
    // Create the result object
    const result: Record<string, any> = {}

    // Process each FAQ from the global list
    listingFaqs.forEach(faq => {
        // Create the FAQ object with the required structure
        result[`faq_${faq.id}`] = {
            internalId: faq.id,
            title: faq.name, // Use name as title
            text: "",
            showInGuidebook: true,
            requireConfirmedReservation: false,
            private: false,
            tags: faq.tags || [],
            photos: [],
            videos: [],
            template: "faq",
            type: "default",
            metadata: {
                updatedAt: Date.now(),
                updatedBy: "system",
            },
        }
    })

    return result
}

export default {
    initFaqs
}
