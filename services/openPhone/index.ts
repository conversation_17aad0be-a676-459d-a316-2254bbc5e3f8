import axios from 'axios'

type MessageEvent = 'message.received' | 'message.delivered'
type CallEvent = 'call.completed' | 'call.ringing' | 'call.recording.completed'
type CallSummaryEvent = 'call.summary.completed'
type CallTranscriptEvent = 'call.transcript.completed'

interface BaseWebhookConfig {
    url: string
}

interface MessageWebhookConfig extends BaseWebhookConfig {
    events: readonly MessageEvent[]
}

interface CallWebhookConfig extends BaseWebhookConfig {
    events: readonly CallEvent[]
}

interface CallSummaryWebhookConfig extends BaseWebhookConfig {
    events: readonly CallSummaryEvent[]
}

interface CallTranscriptWebhookConfig extends BaseWebhookConfig {
    events: readonly CallTranscriptEvent[]
}

class OpenPhone {
    private readonly apiOptions: {
        baseURL: string
        headers: {
            'Accept': string
            'Content-Type': string
            'Authorization': string
        }
    }

    constructor(apiKey: string) {
        console.info('Initializing OpenPhone client')
        this.apiOptions = {
            baseURL: 'https://api.openphone.com/v1',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': apiKey,
            },
        }
    }

    public async heartbeat(): Promise<any> {
        console.info('Checking OpenPhone API heartbeat')
        try {
            await axios.get('phone-numbers', this.apiOptions)
            console.info('OpenPhone API heartbeat successful')
            return true
        }
        catch (error) {
            console.error('OpenPhone API heartbeat failed', { error })
            return false
        }
    }

    /**
     * Get all phone numbers associated with the account
     * @returns Promise<any[]> Array of phone numbers
     */
    public async getPhoneNumbers(): Promise<any[]> {
        console.info('Fetching OpenPhone numbers')
        try {
            const {
                data: { data },
            } = await axios.get('phone-numbers', this.apiOptions)
            console.info('Successfully fetched OpenPhone numbers', { count: data.length })
            return data
        }
        catch (error) {
            console.error('Failed to fetch OpenPhone numbers', { error })
            return []
        }
    }

    /**
     * Create a message webhook
     */
    public async createMessageWebhook(config: MessageWebhookConfig): Promise<any> {
        console.info('Creating message webhook', { url: config.url, events: config.events })
        try {
            const {
                data: { data },
            } = await axios.post(
                'webhooks/messages',
                {
                    url: config.url,
                    events: [...config.events],
                },
                this.apiOptions,
            )
            console.info('Successfully created message webhook', { webhookId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to create message webhook', { error, config })
            if (error instanceof Error) {
                throw new TypeError(`Failed to create message webhook: ${error.message}`)
            }
            throw new Error('Failed to create message webhook: Unknown error')
        }
    }

    /**
     * Create a call webhook
     */
    public async createCallWebhook(config: CallWebhookConfig): Promise<any> {
        console.info('Creating call webhook', { url: config.url, events: config.events })
        try {
            const {
                data: { data },
            } = await axios.post(
                'webhooks/calls',
                {
                    url: config.url,
                    events: [...config.events],
                },
                this.apiOptions,
            )
            console.info('Successfully created call webhook', { webhookId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to create call webhook', { error, config })
            if (error instanceof Error) {
                throw new TypeError(`Failed to create call webhook: ${error.message}`)
            }
            throw new Error('Failed to create call webhook: Unknown error')
        }
    }

    /**
     * Create a call summary webhook
     */
    public async createCallSummaryWebhook(config: CallSummaryWebhookConfig): Promise<any> {
        console.info('Creating call summary webhook', { url: config.url, events: config.events })
        try {
            const {
                data: { data },
            } = await axios.post(
                'webhooks/call-summaries',
                {
                    url: config.url,
                    events: [...config.events],
                },
                this.apiOptions,
            )
            console.info('Successfully created call summary webhook', { webhookId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to create call summary webhook', { error, config })
            if (error instanceof Error) {
                throw new TypeError(`Failed to create call summary webhook: ${error.message}`)
            }
            throw new Error('Failed to create call summary webhook: Unknown error')
        }
    }

    /**
     * Create a call transcript webhook
     */
    public async createCallTranscriptWebhook(config: CallTranscriptWebhookConfig): Promise<any> {
        console.info('Creating call transcript webhook', { url: config.url, events: config.events })
        try {
            const {
                data: { data },
            } = await axios.post(
                'webhooks/call-transcripts',
                {
                    url: config.url,
                    events: [...config.events],
                },
                this.apiOptions,
            )
            console.info('Successfully created call transcript webhook', { webhookId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to create call transcript webhook', { error, config })
            if (error instanceof Error) {
                throw new TypeError(`Failed to create call transcript webhook: ${error.message}`)
            }
            throw new Error('Failed to create call transcript webhook: Unknown error')
        }
    }

    /**
     * Get all webhooks associated with the account
     * @returns Promise<any[]> Array of webhooks
     */
    public async getWebhooks(): Promise<any[]> {
        console.info('Fetching existing webhooks')
        try {
            const {
                data: { data },
            } = await axios.get('webhooks', this.apiOptions)
            console.info('Successfully fetched webhooks', { count: data.length })
            return data
        }
        catch (error) {
            console.error('Failed to fetch webhooks', { error })
            return []
        }
    }

    /**
     * Check if a webhook exists for a given URL
     */
    public async doesWebhookExist(url: string): Promise<boolean> {
        console.info('Checking if webhook exists', { url })
        const webhooks = await this.getWebhooks()
        const exists = webhooks.some(webhook => webhook.url === url)
        console.info('Webhook existence check result', { url, exists })
        return exists
    }

    /**
     * Ensure webhook exists, create if it doesn't
     */
    public async ensureWebhooksExist(): Promise<void> {
        console.info('Starting webhook setup process')
        const baseUrl = 'https://us-central1-home-service-62a33.cloudfunctions.net'

        const messageWebhook: MessageWebhookConfig = {
            url: `${baseUrl}/http-request-openphone-webhook-message`,
            events: ['message.received', 'message.delivered'],
        }

        const callWebhook: CallWebhookConfig = {
            url: `${baseUrl}/http-request-openphone-webhook-call`,
            events: ['call.completed', 'call.ringing', 'call.recording.completed'],
        }

        const callTranscriptWebhook: CallTranscriptWebhookConfig = {
            url: `${baseUrl}/http-request-openphone-webhook-calltranscript`,
            events: ['call.transcript.completed'],
        }

        const callSummaryWebhook: CallSummaryWebhookConfig = {
            url: `${baseUrl}/http-request-openphone-webhook-callsummary`,
            events: ['call.summary.completed'],
        }

        // Create webhooks if they don't exist
        console.info('Checking and creating message webhook')
        if (!(await this.doesWebhookExist(messageWebhook.url))) {
            await this.createMessageWebhook(messageWebhook)
        }

        console.info('Checking and creating call webhook')
        if (!(await this.doesWebhookExist(callWebhook.url))) {
            await this.createCallWebhook(callWebhook)
        }

        console.info('Checking and creating call transcript webhook')
        if (!(await this.doesWebhookExist(callTranscriptWebhook.url))) {
            await this.createCallTranscriptWebhook(callTranscriptWebhook)
        }

        console.info('Checking and creating call summary webhook')
        if (!(await this.doesWebhookExist(callSummaryWebhook.url))) {
            await this.createCallSummaryWebhook(callSummaryWebhook)
        }

        console.info('Completed webhook setup process')
    }

    /**
     * Create a contact in OpenPhone
     * @param contactData Object with contact details
     * @returns Promise<any> Created contact data
     */
    public async createContact(contactData: any): Promise<any> {
        console.info('Creating OpenPhone contact', {
            firstName: contactData.defaultFields?.firstName,
        })
        try {
            const {
                data: { data },
            } = await axios.post('contacts', contactData, this.apiOptions)
            console.info('Successfully created OpenPhone contact', { contactId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to create OpenPhone contact', { error, contactData })
            if (error instanceof Error) {
                throw new TypeError(`Failed to create OpenPhone contact: ${error.message}`)
            }
            throw new Error('Failed to create OpenPhone contact: Unknown error')
        }
    }

    public async updateContact(contactId: string, contactData: any): Promise<any> {
        console.info('Updating OpenPhone contact', { contactId, contactData })
        try {
            const {
                data: { data },
            } = await axios.patch(`contacts/${contactId}`, contactData, this.apiOptions)
            console.info('Successfully updated OpenPhone contact', { contactId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to update OpenPhone contact', { error, contactId, contactData })
            if (error instanceof Error) {
                throw new TypeError(`Failed to update OpenPhone contact: ${error.message}`)
            }
            throw new Error('Failed to update OpenPhone contact: Unknown error')
        }
    }

    public async sendMessage(from: string, to: string, message: string): Promise<any> {
        console.info('Sending message to OpenPhone contact', { from, to, message })
        try {
            const { data } = await axios.post(
                `messages`,
                { from, to: [to], content: message },
                this.apiOptions,
            )
            console.info('Successfully sent message to OpenPhone contact', { messageId: data.id })
            return data
        }
        catch (error: unknown) {
            console.error('Failed to send message to OpenPhone contact', { error, from, to, message })
            if (error instanceof Error) {
                throw new TypeError(`Failed to send message to OpenPhone contact: ${error.message}`)
            }
            throw new Error('Failed to send message to OpenPhone contact: Unknown error')
        }
    }

    public async getCustomFields(): Promise<any[]> {
        try {
            const {
                data: { data },
            } = await axios.get('contact-custom-fields', this.apiOptions)
            return data
        }
        catch (error) {
            console.error('Failed to fetch OpenPhone custom fields', { error })
            return []
        }
    }

    public async getOpenPhoneContactByExternalId(externalId: string): Promise<any | null> {
        try {
            const {
                data: { data },
            } = await axios.get(`contacts?maxResults=1&externalIds=${externalId}`, this.apiOptions)

            return data
        }
        catch (error) {
            console.error('Failed to fetch OpenPhone contacts by external ID', {
                error,
                externalId,
            })
            return null
        }
    }
}

export {
    type CallSummaryWebhookConfig,
    type CallTranscriptWebhookConfig,
    type CallWebhookConfig,
    type MessageWebhookConfig,
    OpenPhone,
}
