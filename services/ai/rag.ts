import { createOpenAI } from '@ai-sdk/openai'
import { streamText } from 'ai'


const apiKey = useRuntimeConfig().openaiApiKey
if (!apiKey)
    throw new Error('Missing OpenAI API key')
const openai = createOpenAI({
    apiKey,
})

export async function smallTalk(messages) {
    const result = streamText({
        model: openai('gpt-4o-mini'),
        messages,
        system: `You are a helpful assistant. Your job is to respond to chitchat type messages. Please do so succinctly and in the language of the incoming message.`,
    })

    return result.toDataStreamResponse()
}