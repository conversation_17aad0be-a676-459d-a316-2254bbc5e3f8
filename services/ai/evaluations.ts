import { z } from 'zod'
import axios from 'axios'
import { generateObject } from 'ai'
import { openai } from '@ai-sdk/openai'

// Grade prompt
const relevanceInstructions = `You are a teacher grading a quiz. 

You will be given a QUESTION and a STUDENT ANSWER. 

Here is the grade criteria to follow:
(1) Ensure the STUDENT ANSWER is concise and relevant to the QUESTION
(2) Ensure the STUDENT ANSWER helps to answer the QUESTION

Relevance:
A relevance value of True means that the student's answer meets all of the criteria.
A relevance value of False means that the student's answer does not meet all of the criteria.

Explain your reasoning in a step-by-step manner to ensure your reasoning and conclusion are correct. 

Avoid simply stating the correct answer at the outset.`

// Grade prompt
const groundedInstructions = `You are a teacher grading a quiz. 

You will be given FACTS and a STUDENT ANSWER. 

Here is the grade criteria to follow:
(1) Ensure the STUDENT ANSWER is grounded in the FACTS. 
(2) Ensure the STUDENT ANSWER does not contain "hallucinated" information outside the scope of the FACTS.

Grounded:
A grounded value of True means that the student's answer meets all of the criteria.
A grounded value of False means that the student's answer does not meet all of the criteria.

Explain your reasoning in a step-by-step manner to ensure your reasoning and conclusion are correct. 

Avoid simply stating the correct answer at the outset.`

export async function getAccuracy(question: string, answer: string, context: string) {
    try {
        // Use the ragas-evaluation-service deployed on Fly.io
        const ragasServiceUrl = 'https://ragas-evaluation-service.fly.dev'

        const response = await axios.post(`${ragasServiceUrl}/evaluate`, {
            user_input: question,
            response: answer,
            retrieved_contexts: [context, 'If you need anything else, feel free to ask!', 'For your convenience', 'For your use', 'If you have any other questions or need more details, feel free to ask!', 'Looking forward to hosting you!'],
            metrics: ['faithfulness']
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
        })
        return response.data.metrics.faithfulness
    } catch (error) {
        console.error('Error calling ragas evaluation service for accuracy:', error)
        return 0
    }
}

export async function getRelevance(question: string, answer: string, context: string) {
    try {
        // Use the ragas-evaluation-service deployed on Fly.io
        const ragasServiceUrl = 'https://ragas-evaluation-service.fly.dev'

        const response = await axios.post(`${ragasServiceUrl}/evaluate`, {
            user_input: question,
            response: answer,
            retrieved_contexts: [context, 'If you need anything else, feel free to ask!'],
            metrics: ['response_relevancy']
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
        })
        return response.data.metrics.response_relevancy
    } catch (error) {
        console.error('Error calling ragas evaluation service:', error)
        // Return a default score of 0 if the service fails
        return 0
    }
}

export async function getCitations(
    userResponse: string,
    userQuestion: string,
    retrievedSources: Array<{ id: string, text: string }>
) {
    if (!retrievedSources || retrievedSources.length === 0) {
        return [] // No citations needed if no sources were used
    }

    try {
        const citationsSchema = z.object({
            relevantSources: z.array(z.object({
                id: z.string().describe('The source ID'),
                text: z.string().describe('The specific text content that was used'),
                reason: z.string().describe('Brief explanation of how this source was used in the response')
            })).describe('Only the specific pieces of information from retrieved data that were actually used in the response')
        })

        const sourcesContext = retrievedSources.map((source, index) =>
            `Source ${index + 1} (ID: ${source.id}):\n${source.text}`
        ).join('\n\n')

        const result = await generateObject({
            model: openai('gpt-4o-mini'),
            system: `You are an expert at analyzing responses and identifying which specific sources were actually used to generate the answer.

Your task is to:
1. Analyze the user's response to their question
2. Identify which specific sources from the retrieved data were actually referenced or used
3. Only include sources that directly contributed information to the response
4. Exclude sources that weren't used or only provided background context

Be very strict - only include sources that can be clearly traced to specific information in the response.`,
            prompt: `
User Question: ${userQuestion}

User Response: ${userResponse}

Retrieved Sources:
${sourcesContext}

Please identify which sources were actually used in generating the response. Only include sources where you can clearly trace specific information from the source to content in the response.`,
            schema: citationsSchema
        })

        // Deduplicate sources by id
        const uniqueSources = result.object.relevantSources.filter((source, index, self) =>
            index === self.findIndex(s => s.id === source.id)
        )

        // Helper function to clean up IDs (same logic as the original tool)
        const cleanId = (id: string) => {
            if (id.startsWith('hostaway')) {
                // Remove everything up to and including the third underscore
                const parts = id.split('_')
                return parts.slice(3).join('_')
            } else {
                // Remove everything up to and including the first underscore
                const firstUnderscoreIndex = id.indexOf('_')
                return firstUnderscoreIndex !== -1 ? id.substring(firstUnderscoreIndex + 1) : id
            }
        }


        const citations = uniqueSources.map(source => ({
            id: cleanId(source.id),
            text: source.text,
            reason: source.reason
        }))

        return citations

    } catch (error) {
        console.error('Error generating citations:', error)
        return []
    }
}
