import { Index } from '@upstash/vector'

// Define the structure of the index configuration
interface IndexConfig {
  url: string | undefined
  token: string | undefined
}

// Define the map with specific keys
type IndexMapKeys = 'listing' | 'sop'

// Define the index map with proper typing
const indexMap: Record<IndexMapKeys, IndexConfig> = {
  listing: {
    url: process.env.UPSTASH_LISTING_INDEX_URL,
    token: process.env.UPSTASH_LISTING_INDEX_TOKEN,
  },
  sops: {
    url: process.env.UPSTASH_SOP_INDEX_URL,
    token: process.env.UPSTASH_SOP_INDEX_TOKEN,
  },
}

async function upsert(indexId: IndexMapKeys, vectorId: string, metadata: any, text: string) {
  const index = new Index({ url: indexMap[indexId].url, token: indexMap[indexId].token })
  try {
    await index.upsert({
      id: vectorId,
      metadata,
      data: text,
    })
  }
  catch (e) {
    // Structured error logging for better visibility in Vercel
    console.error(JSON.stringify({
      error: 'Error upserting to upstash',
      indexId,
      vectorId,
      errorMessage: e instanceof Error ? e.message : String(e),
      errorName: e instanceof Error ? e.name : 'Unknown',
      stack: e instanceof Error ? e.stack : undefined,
      timestamp: new Date().toISOString(),
    }))
  }
}

async function query(indexId: IndexMapKeys, filter: string, data: string) {
  const index = new Index({ url: indexMap[indexId].url, token: indexMap[indexId].token })
  try {
    const results = await index.query({
      data,
      includeVectors: false,
      includeMetadata: true,
      includeData: true,
      topK: 10,
      filter,
    })

    return results.map(({ id, data }) => ({
      id,
      text: data,
    }))
  }
  catch (e) {
    console.error('unable to query Upstash: ', JSON.stringify(e))
    return []
  }
}

export { query, upsert }
