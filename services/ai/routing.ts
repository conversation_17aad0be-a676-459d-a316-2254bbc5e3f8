import { createOpenAI } from '@ai-sdk/openai'
import { generateObject } from 'ai'

export async function detectRoute(message: string, history: [string]) {
    const apiKey = useRuntimeConfig().openaiApiKey
    if (!apiKey)
        throw new Error('Missing OpenAI API key')
    const openai = createOpenAI({
        apiKey,
    })

    const { object } = await generateObject({
        model: openai('gpt-4o-mini'),
        output: 'enum',
        enum: ['smalltalk', 'changereservation', 'availability', 'earlycheckin', 'latecheckout', 'faq', 'other'],
        prompt: `Guest Message: ${message} Conversation History: ${history}`,
        system: 'Using the guest message and the conversation history, classify the message. If the message is chit chat or smalltalk or grattitude, classify it as smalltalk. If the message is abouty changing a reservation, classify it as changereservation. if the message is about checking whether dates are available, classify it as availability. If the message is about an early check in or late check out, classify it as either earlycheckin or latecheckout. if the message is an FAQ type question, classify it as faq. And if it does not fall into any of these buckets, classify it as general. Remember your instructions. Prioritize the message when generating your classification but also use the history to contextualize your classification.'
    })

    return object;
}
