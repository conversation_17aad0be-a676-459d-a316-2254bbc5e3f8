import { Client } from '@upstash/qstash'

const qstashClient = new Client({ token: process.env.QSTASH_TOKEN })


async function scheduleQstash(url: string, data: any, delay = 0, retries = 0): Promise<any> {
    try {
        await qstashClient.publishJSON({
            url,
            delay,
            body: data,
            retries,
        })
    }
    catch (e) {
        console.error('unable to qstash', { error: e })
    }
}

/**
 * Calculates a random delay for rate-limited scheduling
 * @param count Total number of items being scheduled
 * @param batchSize Maximum number of items per minute (default: 75)
 * @returns Random delay in seconds as an integer
 */
function calculateRandomDelay(count: number, batchSize: number = 75): number {
    const totalMinutesNeeded = Math.ceil(count / batchSize)
    const randomMinute = Math.floor(Math.random() * totalMinutesNeeded)
    const randomSecond = Math.floor(Math.random() * 60)
    return (randomMinute * 60) + randomSecond
}

export { calculateRandomDelay, scheduleQstash }
