import { APIError, LoopsClient } from 'loops'

const loops = new LoopsClient(process.env.LOOPS_API_KEY)

const templates = {
  TXN_APP_SMARTSPACE_DOWNLOADAMENITIES_SINGLELISTING: 'cm6qqzkkd00cw12pdpb8hw12k',
  TXN_APP_SMARTSPACE_DOWNLOADCOVER_SINGLELISTING: 'cm6zfj4cf020312t3nutx3ldm',
  TXN_API_WEBCHAT_INITIATED: 'cm8nuvv4q0kluzpomgiyquy1b',
  TXN_API_TASK_CREATED: 'cm8ns82500h3nesybgxme7wo7',
  TXN_API_TASK_ASSIGNED: 'cm8nur0at0j6ytbgehovy5uyu',
  TXN_API_TASK_STATUS_CHANGED: 'cm8ogp4ef1h7qp3d7tfkiqnir',
  TXN_API_AI_RESPONDED: 'cm8nv3p3x0knfs9s81ilhv13e',
  TXN_API_AI_FAILED: 'cm8nvaz3k0kjr2uf75djgsuth',
  TXN_API_NEWAGENT_ADMINCONFIRM: 'cmavxfzz92laxwy0jn7o67c64',
  TXN_API_NEWAGENT_AGENTCONFIRM: 'cmavykoqx2stwwl0ilh83vdz5',
  TXN_API_CREATE_OPENPHONE_CUSTOM_FIELDS: 'cmbicj36b05lf3t0izzaeh61i',
}

async function createContact(email: string, properties: any) {
  try {
    await loops.createContact(email, properties)
  }
  catch (e) {
    console.error('Error creating contact', { error: e })
    throw e instanceof Error ? e : new Error(String(e))
  }
}

async function sendTransactional(templateName: string, to: string, data: any) {
  const templateId = templates[templateName]
  if (!templateId) {
    throw new Error(`Template ${templateName} not found`)
  }

  try {
    await loops.sendTransactionalEmail({
      transactionalId: templateId,
      email: to,
      dataVariables: data,
    })
    // resp.success and resp.id available when successful
  }
  catch (error) {
    if (error instanceof APIError) {
      // JSON returned by the API is in error.json and the HTTP code is in error.statusCode
      // Error messages explaining the issue can be found in error.json.message
      console.error('Error sending transactional email', {
        error: error.json,
        code: error.statusCode,
      })
    }
    else {
      // Non-API errors
    }
  }
}

async function triggerEvent(email: string, eventName: string, eventProperties: any = {}): Promise<void> {
  try {
    await loops.sendEvent({ email, eventName, eventProperties })
  }
  catch (e) {
    console.error('Error triggering event', { error: e })
    throw e instanceof Error ? e : new Error(String(e))
  }
}

export { createContact, sendTransactional, triggerEvent }
