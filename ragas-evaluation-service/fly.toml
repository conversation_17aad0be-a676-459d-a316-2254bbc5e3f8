# fly.toml app configuration file generated for ragas-evaluation-service on 2025-05-25T13:44:55-06:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'ragas-evaluation-service'
primary_region = 'bos'

[build]

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
