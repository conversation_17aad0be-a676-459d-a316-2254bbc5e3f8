# RAGAS Evaluation Service - Fly.io Deployment

## Deployment Steps

1. **Install the Fly CLI**: https://fly.io/docs/hands-on/install-flyctl/

2. **Authenticate with Fly.io**:
   ```bash
   flyctl auth login
   ```

3. **Navigate to the service directory**:
   ```bash
   cd ragas-evaluation-service
   ```

4. **Launch the app** (this will create the app and deploy it):
   ```bash
   flyctl launch
   ```
   - Choose your app name (or use the default `ragas-evaluation-service`)
   - Select a region close to your users
   - Don't add a PostgreSQL database
   - Don't add a Redis database
   - Deploy now: Yes

5. **Set your OpenAI API key as a secret**:
   ```bash
   flyctl secrets set OPENAI_API_KEY=your_openai_api_key_here
   ```

6. **Deploy updates** (after making changes):
   ```bash
   flyctl deploy
   ```

## Useful Commands

- **View logs**: `flyctl logs`
- **Check app status**: `flyctl status`
- **Scale the app**: `flyctl scale count 2`
- **SSH into the app**: `flyctl ssh console`
- **View secrets**: `flyctl secrets list`

## Configuration Notes

- The app is configured to auto-start and auto-stop machines based on traffic
- Minimum 0 machines running to save costs when not in use
- Health checks are configured on `/health` endpoint
- HTTPS is enforced
- 1 CPU, 1GB RAM (adjust in fly.toml if needed)

## Testing the Deployment

Once deployed, you can test your service:

```bash
curl https://your-app-name.fly.dev/
curl https://your-app-name.fly.dev/health
curl https://your-app-name.fly.dev/example
```

## Monitoring

- Dashboard: https://fly.io/dashboard
- Metrics: https://fly.io/apps/your-app-name/monitoring
