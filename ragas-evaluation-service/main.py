from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import os
import async<PERSON>
from contextlib import asynccontextmanager
from uptrain import EvalLLM, Evals, Settings

# RAGAS imports
from ragas.llms import Langchain<PERSON><PERSON><PERSON>rapper
from ragas.embeddings import LangchainEmbeddingsWrapper
from langchain_openai import ChatOpenAI
from langchain_openai import OpenAIEmbeddings
from ragas.dataset_schema import SingleTurnSample
from ragas.metrics import ResponseRelevancy, Faithfulness

# Global variables for evaluators
evaluator_llm = None
evaluator_embeddings = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global evaluator_llm, evaluator_embeddings
    
    # Get OpenAI API key from environment
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    # Initialize evaluators
    evaluator_llm = LangchainLLMWrapper(ChatOpenAI(model="gpt-4o-mini", api_key=openai_api_key))
    evaluator_embeddings = LangchainEmbeddingsWrapper(OpenAIEmbeddings(api_key=openai_api_key))
    
    yield
    
    # Shutdown
    pass

app = FastAPI(
    title="RAGAS Evaluation Service",
    description="A FastAPI service for evaluating RAG responses using RAGAS metrics",
    version="1.0.0",
    lifespan=lifespan
)

class EvaluationRequest(BaseModel):
    user_input: str
    response: str
    retrieved_contexts: List[str]
    metrics: Optional[List[str]] = ["response_relevancy", "faithfulness"]

class EvaluationResponse(BaseModel):
    metrics: dict
    sample: dict

@app.get("/")
async def root():
    return {
        "message": "RAGAS Evaluation Service", 
        "status": "running",
        "available_endpoints": ["/evaluate", "/health"]
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/evaluate", response_model=EvaluationResponse)
async def evaluate(request: EvaluationRequest):
    """
    Evaluate a RAG response using RAGAS metrics
    
    Supported metrics:
    - response_relevancy: How relevant the response is to the user input
    - faithfulness: How faithful the response is to the retrieved contexts
    """
    try:
        # Create sample for evaluation
        sample = SingleTurnSample(
            user_input=request.user_input,
            response=request.response,
            retrieved_contexts=request.retrieved_contexts
        )

        upData = [{
            "question": request.user_input,
            "context": " ".join(request.retrieved_contexts),
            "response": request.response
        }]
        
        results = {}
        
        # Evaluate based on requested metrics
        for metric in request.metrics:
            if metric == "response_relevancy":
                # Run UpTrain evaluation for response relevance in a separate thread with its own event loop
                def run_uptrain_relevance_eval():
                    import asyncio
                    try:
                        # Create a new event loop for this thread
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        
                        settings = Settings(model='gpt-4.1-mini', openai_api_key=os.getenv("OPENAI_API_KEY"))
                        eval_llm = EvalLLM(settings)
                        result = eval_llm.evaluate(
                            data=upData, checks=[Evals.RESPONSE_RELEVANCE]
                        )
                        
                        loop.close()
                        return result
                    except Exception as e:
                        print(f"UpTrain relevance evaluation error: {e}")
                        return None
                
                # Execute in thread pool with isolated event loop
                loop = asyncio.get_event_loop()
                res = await loop.run_in_executor(None, run_uptrain_relevance_eval)

                print(f"UpTrain relevance evaluation result: {res}")
                
                # Extract the response relevance score from the result
                if res and len(res) > 0 and 'score_response_relevance' in res[0]:
                    results["response_relevancy"] = res[0]['score_response_relevance']
                else:
                    # Return the full response if score extraction fails, or None if evaluation failed
                    results["response_relevancy"] = res
                
            elif metric == "faithfulness":
                # Run UpTrain evaluation for factual accuracy in a separate thread with its own event loop
                def run_uptrain_accuracy_eval():
                    import asyncio
                    try:
                        # Create a new event loop for this thread
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        
                        settings = Settings(model='gpt-4.1-mini', openai_api_key=os.getenv("OPENAI_API_KEY"))
                        eval_llm = EvalLLM(settings)
                        result = eval_llm.evaluate(
                            data=upData, checks=[Evals.FACTUAL_ACCURACY]
                        )
                        
                        loop.close()
                        return result
                    except Exception as e:
                        print(f"UpTrain accuracy evaluation error: {e}")
                        return None
                
                # Execute in thread pool with isolated event loop
                loop = asyncio.get_event_loop()
                res = await loop.run_in_executor(None, run_uptrain_accuracy_eval)

                print(f"UpTrain accuracy evaluation result: {res}")
                
                # Extract the factual accuracy score from the result
                if res and len(res) > 0 and 'score_factual_accuracy' in res[0]:
                    results["faithfulness"] = res[0]['score_factual_accuracy']
                else:
                    # Return the full response if score extraction fails, or None if evaluation failed
                    results["faithfulness"] = res

            else:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Unsupported metric: {metric}. Supported metrics: response_relevancy, faithfulness"
                )
        
        return EvaluationResponse(
            metrics=results,
            sample={
                "user_input": request.user_input,
                "response": request.response,
                "retrieved_contexts": request.retrieved_contexts
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Evaluation failed: {str(e)}")

# Example usage endpoint
@app.get("/example")
async def get_example():
    """
    Get an example request for the /evaluate endpoint
    """
    return {
        "example_request": {
            "user_input": "Where should I park?",
            "response": "You should park in parking spot number 2. Please make sure to only park in this spot, as parking in any other spot may result in a fine. If you need any more help, feel free to ask!",
            "retrieved_contexts": [
                "If you park, you can park in the parking spot number 2. Make sure to only park in this spot, as parking in any other spot will result in a fine.",
                "Historic - Yes there is Historic at the rental.",
                "Books for kids - Yes there is Books for kids at the rental."
            ],
            "metrics": ["response_relevancy", "faithfulness"]
        },
        "curl_example": """
curl -X POST "http://localhost:8000/evaluate" \\
     -H "Content-Type: application/json" \\
     -d '{
       "user_input": "Where should I park?",
       "response": "You should park in parking spot number 2. Please make sure to only park in this spot, as parking in any other spot may result in a fine. If you need any more help, feel free to ask!",
       "retrieved_contexts": [
         "If you park, you can park in the parking spot number 2. Make sure to only park in this spot, as parking in any other spot will result in a fine.",
         "Historic - Yes there is Historic at the rental.",
         "Books for kids - Yes there is Books for kids at the rental."
       ],
       "metrics": ["response_relevancy", "faithfulness"]
     }'
        """
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)