{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/vue": "^1.2.12", "@ceereals/vue-pdf": "^0.0.12", "@chargebee/chargebee-js-vue-wrapper": "^0.3.2", "@firebase/app-types": "^0.9.3", "@hugerte/hugerte-vue": "^1.0.2", "@nuxt/icon": "^1.12.0", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "^3.1.2", "@nuxtjs/mdc": "^0.17.0", "@nuxtjs/seo": "^3.0.3", "@nuxtjs/sitemap": "^7.3.1", "@pinia/nuxt": "^0.11.0", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@upstash/qstash": "^2.7.23", "@upstash/redis": "^1.34.9", "@upstash/vector": "^1.2.1", "@upstash/workflow": "^0.2.13", "@vercel/blob": "^1.0.1", "ai": "^4.3.16", "axios": "^1.9.0", "chargebee": "^3.5.0", "date-fns": "^4.1.0", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "fuse.js": "^7.1.0", "heic2any": "^0.0.4", "js-confetti": "^0.12.0", "lodash": "^4.17.21", "loops": "^4.1.0", "lucide-vue-next": "^0.510.0", "nuxt": "^3.16.2", "nuxt-charts": "^0.1.9", "nuxt-vuefire": "^1.0.5", "password-generator": "^2.3.2", "pdf-lib": "^1.17.1", "pinia": "^3.0.2", "posthog-js": "^1.236.1", "print-js": "^1.6.0", "printjs": "^1.1.0", "prismjs": "^1.30.0", "qrcode": "^1.5.4", "typescript": "^5.8.3", "valibot": "^1.1.0", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-prism-editor": "^2.0.0-alpha.2", "vue-router": "^4.5.0", "vuefire": "^3.2.1", "zod": "^3.25.7"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@iconify-json/lucide": "^1.2.35", "@rollup/plugin-image": "^3.0.3", "@types/node-fetch": "^2.6.12", "@types/qrcode": "^1.5.5", "eslint": "^9.24.0"}}