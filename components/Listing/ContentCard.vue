<script lang="ts" setup>
import type { DropdownMenuItem } from '@nuxt/ui'
import debounce from 'lodash/debounce'
import { ref } from 'vue'

const props = defineProps({
  card: {
    type: Object,
    required: true,
  },
  listingGroupContent: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['card-changed', 'archive-card'])

const actionItems = ref<DropdownMenuItem[]>([
  {
    label: 'Show in Guidebook',
    icon: 'i-lucide-user',
    slot: 'guidebook' as const,
  },
  {
    label: 'Confirmed Reservation',
    icon: 'i-lucide-credit-card',
    slot: 'confirmed' as const,
  },
  {
    label: 'Private',
    icon: 'i-lucide-shield-ban',
    slot: 'private' as const,
  },
  {
    label: 'Add Image',
    icon: 'i-lucide-image',
  },
  {
    label: 'Copy to other listings',
    icon: 'i-lucide-copy',
    slot: 'copy' as const,
  },
  {
    label: 'Archive',
    icon: 'i-lucide-trash',
    slot: 'delete' as const,
    click: () => {
      emit('archive-card', props.card.id)
    },
  },
])

const localText = ref(props.card.text)
const localAmenityAvailable = ref(props.card.amenityAvailable)

const debouncedEmit = debounce(() => {
  emit('card-changed', {
    id: props.card.id,
    payload: {
      text: localText.value,
    },
  })
}, 1000)

function onTextInput(val) {
  localText.value = val
  debouncedEmit()
}

function onSwitchChange(newValue, fieldName) {
  emit('card-changed', {
    id: props.card.id,
    payload: {
      [fieldName]: newValue,
    },
  })
}

function onAmenityToggle(value: boolean) {
  localAmenityAvailable.value = value
  emit('card-changed', {
    id: props.card.id,
    payload: {
      amenityAvailable: value,
      amenityKnown: true,
    },
  })
}
</script>

<template>
  <UCard :class="{ 'bg-orange-50 dark:bg-orange-950': card?.template === 'amenity' && !card.amenityKnown }">
    <template #header>
      <div class="flex flex-col gap-2 w-full">
        <div class="flex items-center justify-between">
          <div class="text-md font-medium">
            {{ card.title }}
          </div>
          <UDropdownMenu :items="actionItems" :ui="{
            content: 'w-64',
          }">
            <UButton variant="link" icon="i-lucide-menu" color="neutral" size="sm" />
            <template #guidebook-trailing>
              <USwitch v-model="card.showInGuidebook" @click.stop
                @update:model-value="(value) => onSwitchChange(value, 'showInGuidebook')" />
            </template>
            <template #confirmed-trailing>
              <USwitch v-model="card.requireConfirmedReservation" @click.stop
                @update:model-value="(value) => onSwitchChange(value, 'requireConfirmedReservation')" />
            </template>
            <template #private-trailing>
              <USwitch v-model="card.private" @click.stop
                @update:model-value="(value) => onSwitchChange(value, 'private')" />
            </template>
          </UDropdownMenu>
        </div>
      </div>
    </template>
    <div>
      <div v-if="card?.template === 'amenity'" class="flex items-center gap-2 mb-2">
        <UCheckbox :model-value="card.amenityKnown ? card.amenityAvailable : localAmenityAvailable"
          :disabled="card.amenityKnown" @update:model-value="onAmenityToggle" />
        <span class="text-xs" :class="{ italic: !card.amenityKnown }">
          <template v-if="!card.amenityKnown">
            Amenity status not known, toggle amenity availability to set status
          </template>
          <template v-else>
            {{ card.listingIntegration === 'guesty'
              ? (card.amenityAvailable ? 'Amenity Available - Set in Guesty' : 'Amenity Unavailable - Set in Guesty')
              : (card.amenityAvailable ? 'Amenity Available - Set in PMS' : 'Amenity Unavailable - Set in PMS') }}
          </template>
        </span>
      </div>
      <UTextarea :model-value="localText" class="w-full border border-gray-200 dark:border-gray-700 rounded-lg" autoresize
        @update:model-value="onTextInput" />
    </div>
  </UCard>
</template>

<style scoped></style>
