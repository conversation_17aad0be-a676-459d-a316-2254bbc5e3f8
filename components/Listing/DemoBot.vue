<script setup lang="ts">
import { useChat } from '@ai-sdk/vue'

const props = defineProps<{ listingId: string }>()

const user = useCurrentUser()
const idToken = user ? await user.value?.getIdToken() : ''

const { messages, input, handleSubmit, reload, stop, status, error } = useChat({
  api: `/api/listings/${props.listingId}/ai`,
  headers: { Authorization: `Bearer ${idToken}` },
})

// Function to get citations from assistant message
function getCitations(message: any) {
  if (message.role !== 'assistant' || !message.toolInvocations)
    return null

  const citationsInvocation = message.toolInvocations.find(
    (invocation: any) => invocation.toolName === 'getCitations' && invocation.state === 'result',
  )

  return citationsInvocation?.result || null
}

// Function to get scores from assistant message
function getScores(message: any) {
  if (message.role !== 'assistant' || !message.toolInvocations)
    return null

  const scoresInvocation = message.toolInvocations.find(
    (invocation: any) => invocation.toolName === 'getScores' && invocation.state === 'result',
  )

  if (!scoresInvocation?.result) return null

  const { accuracy, relevance } = scoresInvocation.result
  return {
    accuracy: Math.round(accuracy * 100),
    relevance: Math.round(relevance * 100)
  }
}
</script>

<template>
  <UChatPalette class="h-96 m-2 border border-gray-200 rounded">
    <UChatMessages :messages="messages" :status="status" compact>
      <template #content="{ message }">
        <!-- Default message content -->
        <div>{{ message.content }}</div>

        <!-- Display citations if they exist -->
        <div v-if="getCitations(message)" class="mt-2 pt-2 border-t border-gray-200 text-xs text-gray-500">
          <div class="font-medium mb-1">
            Sources:
          </div>
          <div v-for="citation in getCitations(message)" :key="citation.id" class="flex gap-2 items-start mb-1">
            <div class="font-mono bg-gray-100 px-1 rounded">
              {{ citation.id.includes('_')
                ? (citation.id.split('_').slice(1).join('_').length > 12
                  ? `${citation.id.split('_').slice(1).join('_').substring(0, 12)}...`
                  : citation.id.split('_').slice(1).join('_'))
                : (citation.id.length > 12
                  ? `${citation.id.substring(0, 12)}...`
                  : citation.id) }}
            </div>
            <div>{{ citation.text }}</div>
          </div>
        </div>

        <!-- Display scores if they exist -->
        <div v-if="getScores(message)" class="mt-2 pt-2 border-t border-gray-200 text-xs text-gray-500">
          <div class="flex gap-4">
            <div class="flex items-center gap-1">
              <span class="font-medium">Accuracy:</span>
              <span class="font-mono bg-green-100 text-green-800 px-1 rounded">{{ getScores(message).accuracy }}%</span>
            </div>
            <div class="flex items-center gap-1">
              <span class="font-medium">Relevance:</span>
              <span class="font-mono bg-blue-100 text-blue-800 px-1 rounded">{{ getScores(message).relevance }}%</span>
            </div>
          </div>
        </div>
      </template>
    </UChatMessages>
    <template #prompt>
      <UChatPrompt v-model="input" variant="naked" :error="error" placeholder="Chat with your listing" class="px-1"
        :disable="status !== 'ready'" @submit="handleSubmit">
        <UChatPromptSubmit variant="soft" :status="status" size="sm" @stop="stop" @reload="reload" />
      </UChatPrompt>
    </template>
  </UChatPalette>
</template>

<style></style>
