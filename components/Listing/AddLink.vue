<script setup lang="ts">
import type { Form, FormSubmitEvent } from '@nuxt/ui'
import { reactive, ref } from 'vue'
import * as z from 'zod'

const emit = defineEmits<{ close: [value: { url: string, title: string, description?: string } | boolean] }>()

const schema = z.object({
  url: z.string().url('Invalid URL format'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
})

type Schema = z.output<typeof schema>

const state = reactive<Partial<Schema>>({
  url: undefined,
  title: undefined,
  description: undefined,
})

const form = ref<Form<Schema> | null>(null)

async function onSubmit(event: FormSubmitEvent<Schema>) {
  emit('close', event.data)
}

function triggerSubmit() {
  form.value?.submit()
}
</script>

<template>
  <UModal :close="{ onClick: () => emit('close', false) }" title="Add New Link">
    <template #body>
      <UForm ref="form" :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
        <UFormField label="URL" name="url" required>
          <UInput v-model="state.url" type="url" placeholder="https://example.com" class="w-full" />
        </UFormField>

        <UFormField label="Title" name="title" required>
          <UInput v-model="state.title" placeholder="Enter a descriptive title" class="w-full" />
        </UFormField>

        <UFormField label="Description (Optional)" name="description">
          <UTextarea v-model="state.description" placeholder="Add any extra details..." class="w-full" />
        </UFormField>
      </UForm>
    </template>
    <template #footer>
      <div class="flex justify-end gap-3">
        <UButton color="gray" variant="ghost" label="Cancel" @click="emit('close', false)" />
        <UButton color="primary" label="Add Link" @click="triggerSubmit" />
      </div>
    </template>
  </UModal>
</template>
