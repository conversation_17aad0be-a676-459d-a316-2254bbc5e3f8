<script setup lang="ts">
defineProps<{
  message: string
}>()

const emit = defineEmits<{ close: [boolean] }>()
</script>

<template>
  <UModal
    :close="{ onClick: () => emit('close', false) }"
    title="Confirm"
  >
    <template #body>
      <p>{{ message }}</p>
    </template>
    <template #footer>
      <div class="flex gap-2">
        <UButton label="Confirm" @click="emit('close', true)" />
      </div>
    </template>
  </UModal>
</template>
