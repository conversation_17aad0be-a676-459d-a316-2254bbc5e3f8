<script setup lang="ts">
import { computed, ref } from 'vue'

const emit = defineEmits<{ close: [value: string | boolean] }>()

const email = ref('')
const isValidEmail = computed(() => {
  // Basic email regex validation
  const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
  return emailRegex.test(email.value)
})
</script>

<template>
  <UModal :close="{ onClick: () => emit('close', false) }" title="Share Email">
    <template #body>
      <UInput v-model="email" type="email" placeholder="Enter email" />
    </template>
    <template #footer>
      <div class="flex gap-2">
        <UButton color="neutral" label="Dismiss" @click="emit('close', false)" />
        <UButton label="Share" :disabled="!isValidEmail" @click="emit('close', email)" />
      </div>
    </template>
  </UModal>
</template>
