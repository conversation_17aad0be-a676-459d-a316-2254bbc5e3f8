<script setup lang="ts">
import { computed, ref } from 'vue'

const props = defineProps({
  pdfUrl: {
    type: String,
    required: true,
  },
  pdfType: {
    type: String,
    required: true,
  },
  listingName: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['close'])

const email = ref('')
const sending = ref(false)
const toast = useToast()

const pdfIframeSrc = computed(() => `${props.pdfUrl}#toolbar=0&navpanes=0`)

async function downloadPdf() {
  if (typeof window === 'undefined')
    return
  try {
    const response = await fetch(props.pdfUrl, { mode: 'cors' })
    if (!response.ok)
      throw new Error('Network response was not ok')
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.listingName}_${props.pdfType}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
  catch (error) {
    console.error('Failed to download PDF:', error)
  }
}

async function printPdf() {
  if (typeof window === 'undefined')
    return
  const printJS = (await import('print-js')).default
  printJS(props.pdfUrl)
}

async function sendEmail() {
  if (!email.value) {
    toast && toast.add({
      title: 'Error',
      description: 'Please enter an email address.',
      color: 'red',
    })
    return
  }
  sending.value = true
  try {
    // Example API call, adjust endpoint as needed
    await useApiFetch('smart-spaces/send', {
      method: 'POST',
      body: {
        email: email.value,
        pdfUrl: props.pdfUrl,
        pdfType: props.pdfType,
        listingNName: props.listingName,
      },
    })
    toast && toast.add({
      title: 'Success',
      description: 'Email sent!',
      color: 'green',
    })
    email.value = ''
  }
  catch {
    toast && toast.add({
      title: 'Error',
      description: 'Failed to send email. Please try again.',
      color: 'red',
    })
  }
  finally {
    sending.value = false
  }
}

function closeModal() {
  emit('close', false)
}
</script>

<template>
  <UModal :close="{ onClick: closeModal }" title="PDF Generated Successfully" class="max-w-3xl w-[800px]">
    <template #body>
      <p class="text-gray-500 dark:text-gray-400 mb-4">
        Your PDF has been generated successfully! You will also receive an email with a link to the PDF.
      </p>
      <iframe :src="pdfIframeSrc" width="100%" height="600px" />
    </template>

    <template #footer>
      <div class="flex flex-col gap-2 w-full">
        <div class="flex items-center gap-2">
          <UInput v-model="email" placeholder="Enter email address to send PDF" class="flex-1" :disabled="sending"
            type="email" size="md" />
          <UButton color="primary" icon="i-lucide-send" :loading="sending" @click="sendEmail">
            Send via Email
          </UButton>
        </div>

        <div class="flex justify-end gap-2 mt-2">
          <UButton color="primary" icon="i-lucide-download" @click="downloadPdf">
            Download PDF
          </UButton>
          <UButton color="secondary" icon="i-lucide-printer" @click="printPdf">
            Print PDF
          </UButton>
        </div>
      </div>
    </template>
  </UModal>
</template>
