<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  listingId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close'])

const isGenerating = ref(false)

async function downloadCoverCard() {
  isGenerating.value = true

  try {
    // Simulate API call with a delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    // In the future, this will be replaced with an actual API call
    // const response = await useApiFetch(`listings/${props.listingId}/smart-spaces/cover-card`, {
    //   method: 'POST'
    // })

    // For now, use a placeholder URL
    const pdfUrl = 'https://google.com'

    // Close this modal and return the PDF URL
    emit('close', pdfUrl)
  } catch (error) {
    console.error('Error generating cover card:', error)
    // Handle error (could add toast notification here)
    emit('close', false)
  } finally {
    isGenerating.value = false
  }
}

function closeModal() {
  emit('close', false)
}
</script>

<template>
  <UModal :close="{ onClick: closeModal }" title="Cover Card Confirmation">
    <template #body>
      <p class="text-gray-500 dark:text-gray-400 mb-4">
        Are you sure you want to download the cover card for this listing?
      </p>
    </template>

    <template #footer>
      <div class="flex justify-end gap-2">
        <UButton color="gray" variant="outline" @click="closeModal">
          Cancel
        </UButton>
        <UButton
          color="primary"
          :loading="isGenerating"
          :disabled="isGenerating"
          @click="downloadCoverCard"
        >
          Download
        </UButton>
      </div>
    </template>
  </UModal>
</template>
