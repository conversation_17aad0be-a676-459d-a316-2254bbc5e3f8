<script setup lang="ts">
import type { CheckboxGroupItem, CheckboxGroupValue } from '@nuxt/ui'
import { ref } from 'vue'
import { listingAmenities } from '~/shared'

const props = defineProps({
  listingId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['close'])

const amenities = computed(() =>
  listingAmenities
    .filter(amenity => amenity.priority === 1)
    .map(({ id, name }) => ({ id, name }))
    .sort((a, b) => a.name.localeCompare(b.name)),
)

const items = ref<CheckboxGroupItem[]>(listingAmenities
  .filter(amenity => amenity.priority === 1)
  .map(({ id, name }) => ({ value: id, label: name }))
  .sort((a, b) => a.label.localeCompare(b.label)))
const selectedAmenities = ref<CheckboxGroupValue[]>([])

const isDownloading = ref(false)

function selectAllAmenities() {
  selectedAmenities.value = amenities.value.map(amenity => amenity.id)
}

function clearAllAmenities() {
  selectedAmenities.value = []
}

async function downloadSelectedAmenities() {
  if (selectedAmenities.value.length === 0)
    return

  isDownloading.value = true

  try {
    const pdfUrl: any = await useApiFetch(`smart-spaces/${props.listingId}`, {
      method: 'POST',
      body: {
        type: 'amenities',
        amenities: selectedAmenities.value,
      },
    })

    emit('close', pdfUrl, 'amenities')
  }
  catch (error) {
    console.error('Error downloading amenities:', error)
    emit('close', false)
  }
  finally {
    isDownloading.value = false
  }
}

function closeModal() {
  emit('close', false)
}
</script>

<template>
  <UModal :close="{ onClick: closeModal }" title="Download Amenity QR Codes">
    <template #body>
      <div class="flex gap-2">
        <UButton variant="outline" size="sm" @click="selectAllAmenities">
          Select All
        </UButton>
        <UButton variant="outline" size="sm" @click="clearAllAmenities">
          Clear All
        </UButton>
      </div>

      <div class="max-h-[300px] overflow-y-auto pr-4 mt-4">
        <UCheckboxGroup v-model="selectedAmenities" :items="items" />

        <div v-if="amenities.length === 0" class="text-center py-4 text-gray-500">
          No amenities available for this listing.
        </div>
      </div>
    </template>

    <template #footer>
      <div class="flex justify-end gap-2">
        <UButton color="gray" variant="outline" @click="closeModal">
          Cancel
        </UButton>
        <UButton
          color="primary"
          :loading="isDownloading"
          :disabled="selectedAmenities.length === 0 || isDownloading"
          @click="downloadSelectedAmenities"
        >
          Download Selected
        </UButton>
      </div>
    </template>
  </UModal>
</template>
