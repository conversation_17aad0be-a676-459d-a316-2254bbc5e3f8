<script setup lang="ts">
import * as v from 'valibot'

const emit = defineEmits<{ close: [boolean] }>()

const schema = v.object({
  email: v.pipe(v.string(), v.email('Please enter a valid email')),
})

const toast = useToast()

const state = reactive({
  email: '',
})
const loading = ref(false)

async function onSubmit() {
  loading.value = true
  try {
    await useApiFetch('workspace/agents', {
      method: 'POST',
      body: state,
    })
  }
  catch (e: any) {
    toast.add({
      title: 'Unable to create agent',
      description: e.statusMessage,
      color: 'error',
    })
  }
  finally {
    loading.value = false
    emit('close', true)
  }
}
</script>

<template>
  <UModal
    :close="{ onClick: () => emit('close', false) }"
    title="Create New Agent"
  >
    <template #body>
      <UForm :schema="schema" :state="state" @submit="onSubmit">
        <UFormField label="Email" name="email" required>
          <UInput v-model="state.email" placeholder="Enter an email" class="w-full" />
        </UFormField>
        <div class="flex justify-end mt-4">
          <UButton :loading="loading" :disabled="loading" label="Create Agent" type="submit" class="mt-4" />
        </div>
      </UForm>
    </template>
  </UModal>
</template>
