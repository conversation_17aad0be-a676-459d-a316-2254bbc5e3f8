<script setup lang="ts">
import { CardComponent } from '@chargebee/chargebee-js-vue-wrapper'
import * as v from 'valibot'
import loadChargebee from '~/helpers/chargebee'
import { countryCodes } from '~/shared/webapp'

defineProps<{
  count: number
}>()

const emit = defineEmits<{ close: [boolean] }>()

const toast = useToast()

const schema = v.object({
  firstName: v.pipe(v.string(), v.minLength(1, 'First name is required')),
  lastName: v.pipe(v.string(), v.minLength(1, 'Last name is required')),
  address: v.pipe(v.string(), v.minLength(1, 'Street address is required')),
  city: v.pipe(v.string(), v.minLength(1, 'City is required')),
  state: v.pipe(v.string(), v.minLength(1, 'State is required')),
  zip: v.pipe(v.string(), v.minLength(1, 'Zip is required')),
  country: v.pipe(v.string(), v.minLength(1, 'Country is required')),
})

const state = reactive({
  firstName: '',
  lastName: '',
  address: '',
  city: '',
  state: '',
  zip: '',
  country: '',
})

const didLoadChargebee = ref(false)
const card = ref(null)

async function handleCardUpdate() {
  if (!card.value) {
    return
  }
  const paymentMethod = await card.value.tokenize({})
  console.log('got the payment method', paymentMethod)

  try {
    await useApiFetch('workspace/billing/update-payment-method', {
      method: 'POST',
      body: { ...state.value, cbToken: paymentMethod.token, cbVaultToken: paymentMethod.vaultToken },
    })
  }
  catch (e) {
    console.error('unable to save payment method')
  }
  finally {
    console.log('done')
  }
}

onMounted(async () => {
  await loadChargebee()
  didLoadChargebee.value = true
})
</script>

<template>
  <UModal
    :close="{ onClick: () => emit('close', false) }"
    title="Add a Payment Method"
  >
    <template #body>
      <UForm :state="state" :schema="schema" @submit="handleCardUpdate">
        <div class="space-y-4">
          <!-- First Name & Last Name Row -->
          <div class="flex gap-4">
            <div class="flex-1">
              <UFormField name="firstName" label="First Name" required>
                <UInput v-model="state.firstName" label="First Name" class="w-full" />
              </UFormField>
            </div>
            <div class="flex-1">
              <UFormField name="lastName" label="Last Name" required>
                <UInput v-model="state.lastName" label="Last Name" class="w-full" />
              </UFormField>
            </div>
          </div>
          <!-- Address Row -->
          <div class="w-full">
            <UFormField name="address" label="Street Address" required>
              <UInput v-model="state.address" label="Street Address" class="w-full" />
            </UFormField>
          </div>
          <!-- City & State Row -->
          <div class="flex gap-4">
            <div class="flex-1">
              <UFormField name="city" label="City" required>
                <UInput v-model="state.city" label="City" class="w-full" />
              </UFormField>
            </div>
            <div class="flex-1">
              <UFormField name="state" label="State" required>
                <UInput v-model="state.state" label="State" class="w-full" />
              </UFormField>
            </div>
          </div>
          <!-- Zip & Country Row -->
          <div class="flex gap-4">
            <div class="flex-1">
              <UFormField name="zip" label="Zip" required>
                <UInput v-model="state.zip" label="Zip" class="w-full" />
              </UFormField>
            </div>
            <div class="flex-1">
              <UFormField name="country" label="Country" required>
                <USelect
                  v-model="state.country"
                  :items="countryCodes"
                  label="Country"
                  class="w-full"
                />
              </UFormField>
            </div>
          </div>
          <!-- Card Component -->
          <div v-if="didLoadChargebee" class="rounded border border-gray-200 p-1 mb-4">
            <CardComponent ref="card" />
          </div>
        </div>
        <UButton label="Save Payment Method" type="submit" />
      </UForm>
    </template>
  </UModal>
</template>
