<script lang="ts" setup>
import { computed, defineEmits, defineProps } from 'vue'

const props = defineProps({
  tasks: {
    type: Array,
    required: true,
  },
  showDone: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update-status'])

const statusItems = ref([
  {
    label: 'To Do',
    value: 'toDo',
  },
  {
    label: 'In Progress',
    value: 'inProgress',
  },
  {
    label: 'Done',
    value: 'done',
  },
])

const columns = [
  {
    key: 'toDo',
    label: 'To Do',
    icon: 'i-lucide-circle',
    iconColor: 'text-slate-400',
  },
  {
    key: 'inProgress',
    label: 'In Progress',
    icon: 'i-lucide-clock',
    iconColor: 'text-amber-500',
  },
  {
    key: 'done',
    label: 'Done',
    icon: 'i-lucide-check-circle-2',
    iconColor: 'text-emerald-500',
  },
]

function mapStatus(status: string) {
  if (status === 'toDo')
    return 'toDo'
  if (status === 'inProgress')
    return 'inProgress'
  if (status === 'done')
    return 'done'
  // fallback for legacy or typo
  if (status === 'todo')
    return 'toDo'
  return status
}

const tasksByStatus = computed(() => {
  return {
    toDo: props.tasks.filter(t => mapStatus(t.status) === 'toDo' && !t.archived),
    inProgress: props.tasks.filter(t => mapStatus(t.status) === 'inProgress' && !t.archived),
    done: props.tasks.filter(t => mapStatus(t.status) === 'done' && !t.archived),
  }
})

function formatDate(ts: number) {
  if (!ts)
    return ''
  const d = new Date(ts)
  return d.toLocaleDateString()
}

function handleStatusChange(taskId: string, newStatus: string) {
  emit('update-status', { id: taskId, status: newStatus })
}
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div v-for="col in columns" :key="col.key" class="space-y-4">
      <div class="flex items-center justify-between">
        <h2 class="text-md font-semibold flex items-center">
          <UIcon :name="col.icon" class="mr-2 h-5 w-5" :class="[col.iconColor]" />
          {{ col.label }}
          <UBadge variant="outline" class="ml-2" size="sm">
            {{ tasksByStatus[col.key]?.length || 0 }}
          </UBadge>
        </h2>
      </div>
      <UCard v-if="col.key === 'done' && !props.showDone">
        <template #content>
          <div class="pt-6 text-center text-muted-foreground">
            {{ tasksByStatus.done.length }} completed {{ tasksByStatus.done.length === 1 ? 'task' : 'tasks' }} hidden
          </div>
        </template>
      </UCard>
      <UCard v-else-if="tasksByStatus[col.key]?.length === 0">
        <template #content>
          <div class="pt-6 text-center text-muted-foreground">
            No {{ col.label.toLowerCase() }} tasks
          </div>
        </template>
      </UCard>
      <div v-else>
        <div class="space-y-4">
          <UCard v-for="task in tasksByStatus[col.key]" :key="task.id" class="hover:shadow-sm transition-shadow" :class="col.key === 'done' ? 'opacity-70' : ''">
            <template #header>
              <div class="flex justify-between items-start">
                <div class="text-base font-medium" style="color: #2563eb; cursor: pointer; text-decoration: underline;" @click="$emit('view-task', task)">
                  {{ task.title }}
                </div>
                <UBadge
                  :color="
                    task.priority === 'high' ? 'error'
                    : task.priority === 'medium' ? 'warning'
                      : 'success'"
                >
                  {{ task.priority.charAt(0).toUpperCase() + task.priority.slice(1) }}
                </UBadge>
              </div>
              <div v-if="task.assignees && task.assignees.length" class="mt-1">
                <div class="flex items-center gap-1 mb-1">
                  <UIcon name="i-lucide-user" class="h-3.5 w-3.5" />
                  <span class="text-xs text-muted-foreground">Assignees:</span>
                </div>
                <div class="flex flex-col ml-5">
                  <span v-for="a in task.assignees" :key="a.id" class="text-xs text-muted-foreground mb-0.5">{{ a.email }}</span>
                </div>
              </div>
              <div v-if="task.listing" class="flex items-center gap-1">
                <UIcon name="i-lucide-home" class="h-3.5 w-3.5" />
                {{ task.listing.name }}
              </div>
            </template>
            <!-- <template #content>
            <p class="text-sm text-muted-foreground">
              {{ task.description }}
            </p>
          </template> -->
            <template #footer>
              <div class="pt-0 flex justify-between">
                <p class="text-xs text-muted-foreground">
                  {{ col.key === 'done' ? 'Completed' : 'Due' }}: {{ formatDate(task.createdAt) }}
                </p>
                <USelect
                  :model-value="mapStatus(task.status)"
                  :items="statusItems"
                  class="w-[130px] h-8"
                  @update:model-value="val => handleStatusChange(task.id, val)"
                />
              </div>
            </template>
          </UCard>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-muted-foreground {
  color: #6b7280;
}
</style>
