<script setup lang="ts">
import type { FormSubmitEvent } from '@nuxt/ui'
import { useListingStore, useWorkspaceStore } from '#imports'
import { taskStatusTypes, taskTypes } from '@/shared/webapp'
import * as v from 'valibot'
import { computed } from 'vue'

const emit = defineEmits<{ close: [boolean], submit: [any] }>()

type Schema = v.InferOutput<typeof schema>

const schema = v.object({
  title: v.pipe(v.string(), v.minLength(5, 'Title must be at least 5 characters')),
  status: v.pipe(v.string(), v.minLength(1, 'Status is required')),
  type: v.pipe(v.string(), v.minLength(1, 'Type is required')),
})

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const state = reactive({
  title: '',
  description: '',
  status: 'toDo',
  type: '',
  listing: '',
  assignees: [],
})

const assigneeItems = computed(() =>
  (workspaceStore.workspace?.users || []).map((u: any) => ({
    label: u.email,
    value: u.userId || u.id,
  })),
)

const listingItems = computed(() =>
  (listingStore.listings || []).map((l: any) => ({ label: l.name, value: l.id })),
)

const statusItems = computed(() =>
  taskStatusTypes.map((s: any) => ({ label: s.label, value: s.value })),
)

const typeItems = computed(() =>
  taskTypes.filter((t: any) => !t.hidden && t.label && t.value).map((t: any) => ({ label: t.value, value: t.label, description: t.description })),
)

async function onSubmit(event: FormSubmitEvent<Schema>) {
  // Find the selected listing object
  const selectedListing = listingStore.listings.find((l: any) => l.id === state.listing)
  // Map assignees to objects with userId and email
  const selectedAssignees = (workspaceStore.workspace?.users || [])
    .filter((u: any) => state.assignees.includes(u.userId || u.id))
    .map((u: any) => ({ userId: u.userId || u.id, email: u.email }))

  // Build the task object
  const newTask = {
    title: state.title,
    description: state.description,
    status: state.status,
    type: state.type,
    listing: selectedListing ? { id: selectedListing.id, name: selectedListing.name } : null,
    assignees: selectedAssignees,
    createdAt: Date.now(),
  }
  emit('close', newTask)
}
</script>

<template>
  <UModal
    :close="{ onClick: () => emit('close', false) }"
    title="Create Task"
  >
    <template #body>
      <UForm :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
        <UFormField label="Title" name="title">
          <UInput v-model="state.title" class="w-full" />
        </UFormField>

        <UFormField label="Type" name="type">
          <USelect
            v-model="state.type" :items="typeItems" :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            class="w-full"
            clearable
            label-key="label"
            value-key="value"
            :option-description="item => item.description"
          />
        </UFormField>

        <UFormField label="Status" name="status">
          <USelect
            v-model="state.status" :items="statusItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            class="w-full"
            clearable
            label-key="label"
            value-key="value"
          />
        </UFormField>

        <UFormField label="Description" name="description">
          <UTextarea v-model="state.description" class="w-full" />
        </UFormField>

        <UFormField label="Listing" name="listing">
          <USelect
            v-model="state.listing" :items="listingItems" :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            class="w-full"
            clearable
            label-key="label"
            value-key="value"
          />
        </UFormField>
        <UFormField label="Assignees" name="assignees">
          <USelect
            v-model="state.assignees" :items="assigneeItems"
            :ui="{ trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200' }"
            class="w-full"
            clearable
            multiple
            label-key="label"
            value-key="value"
          />
        </UFormField>
        <UButton
          type="submit"
          label="Create New Task"
          color="primary"
        />
      </UForm>
    </template>
  </UModal>
</template>
