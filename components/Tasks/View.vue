<script lang="ts" setup>
import { useListingStore, useWorkspaceStore } from '#imports'
import * as v from 'valibot'
import { computed, reactive } from 'vue'
import { taskStatusTypes, taskTypes } from '~/shared/webapp'

const props = defineProps({
  task: { type: Object, required: true },
})

const emit = defineEmits(['close'])

const schema = v.object({
  title: v.pipe(v.string(), v.minLength(5, 'Title must be at least 5 characters')),
  status: v.pipe(v.string(), v.minLength(1, 'Status is required')),
  type: v.pipe(v.string(), v.minLength(1, 'Type is required')),
})
const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()
const assigneeItems = computed(() =>
  (workspaceStore.workspace?.users || []).map((u: any) => ({ label: u.email, value: u.userId || u.id })),
)
const listingItems = computed(() =>
  (listingStore.listings || []).map((l: any) => ({ label: l.name, value: l.id })),
)
const statusItems = computed(() =>
  taskStatusTypes.map((s: any) => ({ label: s.label, value: s.value })),
)

const typeItems = computed(() =>
  taskTypes.filter((t: any) => !t.hidden && t.label && t.value).map((t: any) => ({ label: t.value, value: t.label, description: t.description })),
)

const isSpecialType = computed(() => {
  const specialTypes = ['onboarding', 'automated']
  return specialTypes.includes(props.task.type)
})

const state = reactive({
  id: '',
  title: '',
  description: '',
  status: 'toDo',
  type: '',
  listing: '',
  assignees: [],
})

function onSubmit() {
  // Find the selected listing object
  const selectedListing = listingStore.listings.find((l: any) => l.id === state.listing)
  // Map assignees to objects with userId and email
  const selectedAssignees = (workspaceStore.workspace?.users || [])
    .filter((u: any) => state.assignees.includes(u.userId || u.id))
    .map((u: any) => ({ userId: u.userId || u.id, email: u.email }))
  const editedTask = {
    ...props.task,
    title: state.title,
    description: state.description,
    status: state.status,
    type: state.type,
    listing: selectedListing ? { id: selectedListing.id, name: selectedListing.name } : null,
    assignees: selectedAssignees,
  }
  emit('save', editedTask)
}

onMounted(() => {
  console.log('this is the task we got: ', props.task)
  state.id = props.task.id
  state.title = props.task.title
  state.description = props.task.description
  state.status = props.task.status

  // TYPE: convert value to label for select
  const typeObj = taskTypes.find(t => t.value === props.task.type || t.label === props.task.type)
  state.type = typeObj ? typeObj.label : ''

  // LISTING: extract id if object, or use id directly
  if (props.task.listing && typeof props.task.listing === 'object') {
    state.listing = props.task.listing.id
  }
  else {
    state.listing = props.task.listing || ''
  }

  // Ensure assignees is always an array of user IDs
  if (Array.isArray(props.task.assignees)) {
    state.assignees = props.task.assignees.map(a => (typeof a === 'object' ? a.userId || a.id : a))
  }
  else {
    state.assignees = []
  }

  console.log('set the initial state to: ', state)
})
</script>

<template>
  <UModal
    :close="{ onClick: () => emit('close', false) }"
    title="View/Edit Task"
  >
    <template #body>
      <UForm :schema="schema" :state="state" class="space-y-4" @submit="onSubmit">
        <UFormField label="Title" name="title">
          <UInput v-model="state.title" class="w-full" />
        </UFormField>
        <UFormField label="Type" name="type">
          <template v-if="isSpecialType">
            <UAlert
              icon="i-heroicons-information-circle"
              color="primary"
              variant="soft"
              title="Task Type Locked"
              :description="`This task type is set to ${state.type} and cannot be changed.`"
              class="mb-2"
            />
          </template>
          <template v-else>
            <USelect v-model="state.type" :items="typeItems" class="w-full" clearable label-key="label" value-key="value" :option-description="item => item.description" />
          </template>
        </UFormField>
        <UFormField label="Status" name="status">
          <USelect v-model="state.status" :items="statusItems" class="w-full" clearable label-key="label" value-key="value" />
        </UFormField>
        <UFormField label="Description" name="description">
          <UTextarea v-model="state.description" class="w-full" />
        </UFormField>
        <UFormField label="Listing" name="listing">
          <USelect v-model="state.listing" :items="listingItems" class="w-full" clearable label-key="label" value-key="value" />
        </UFormField>
        <UFormField label="Assignees" name="assignees">
          <USelect v-model="state.assignees" :items="assigneeItems" class="w-full" clearable multiple label-key="label" value-key="value" />
        </UFormField>
        <UButton type="submit" label="Save Changes" color="primary" />
      </UForm>
    </template>
  </UModal>
</template>

<style>
</style>
