<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'
import { format } from 'date-fns'
import { computed, h, ref, resolveComponent, watch } from 'vue'
import { useWorkspaceStore } from '~/stores/workspace'

const props = defineProps<{ tasks: Task[], showDone?: boolean }>()

const emit = defineEmits(['update-status', 'update-assignees', 'selectionChange'])

const UCheckbox = resolveComponent('UCheckbox')

// For row selection (Nuxt UI expects an object of { [id]: true })
const rowSelection = ref<Record<string, boolean>>({})

const USelect = resolveComponent('USelect')
const workspaceStore = useWorkspaceStore()

const assigneeItems = computed(() =>
  (workspaceStore.workspace?.users || []).map((u: any) => ({
    label: u.email,
    value: u.userId || u.id,
  })),
)

interface Task {
  id: string
  title: string
  createdAt: number
  status: string
  date?: string
  conversationId?: string
  type: string
  listing?: { id: string, name: string } | null
  assignees?: { id: string, email: string }[]
}

const filteredTasks = computed(() => {
  if (props.showDone)
    return props.tasks
  return props.tasks.filter(task => task.status !== 'done')
})

const statusItems = [
  { label: 'To Do', value: 'toDo', chip: { color: 'error' } },
  { label: 'In Progress', value: 'inProgress', chip: { color: 'warning' } },
  { label: 'Done', value: 'done', chip: { color: 'success' } },
]

function getStatusChip(value: string) {
  return statusItems.find(item => item.value === value)?.chip
}

function handleStatusChange(taskId: string, newStatus: string) {
  emit('update-status', { id: taskId, status: newStatus })
}

const columns: TableColumn<Task>[] = [
  {
    id: 'select',
    header: ({ table }: any) =>
      h(UCheckbox, {
        'modelValue': table.getIsSomePageRowsSelected()
          ? 'indeterminate'
          : table.getIsAllPageRowsSelected(),
        'onUpdate:modelValue': (value: boolean | 'indeterminate') =>
          table.toggleAllPageRowsSelected(!!value),
        'aria-label': 'Select all',
      }),
    cell: ({ row }: { row: TableRow<Task> }) =>
      h(UCheckbox, {
        'modelValue': row.getIsSelected(),
        'onUpdate:modelValue': (value: boolean | 'indeterminate') => row.toggleSelected(!!value),
        'aria-label': 'Select row',
      }),
  },
  {
    accessorKey: 'title',
    header: 'Name',
    cell: ({ row }) => {
      return h('span', {
        style: 'color: #2563eb; cursor: pointer; text-decoration: underline;',
        onClick: () => emit('view-task', row.original),
      }, row.original.title)
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => {
      const value = row.getValue('createdAt')
      return value ? format(new Date(Number(value)), 'yyyy-MM-dd HH:mm') : '—'
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const value = row.getValue('status')
      const taskId = row.original.id
      return h(USelect, {
        'modelValue': value,
        'onUpdate:modelValue': (val: string) => handleStatusChange(taskId, val),
        'items': statusItems,
        'class': 'w-[160px] h-8',
      }, {
        leading: ({ modelValue, ui }) =>
          modelValue
            ? h(resolveComponent('UChip'), {
              ...getStatusChip(modelValue),
              inset: true,
              standalone: true,
              size: ui.itemLeadingChipSize(),
              class: ui.itemLeadingChip(),
            })
            : undefined,
      })
    },
  },
  {
    accessorKey: 'date',
    header: 'Due Date',
    cell: ({ row }) => {
      const value = row.getValue('date')
      // Only process if it's a string in the expected format (YYYY/M/D or YYYY/MM/DD)
      if (typeof value === 'string' && /^\d{4}\/\d{1,2}\/\d{1,2}$/.test(value)) {
        return format(new Date(value), 'yyyy-MM-dd')
      }
      return '—'
    },
  },
  {
    accessorKey: 'conversationId',
    header: 'Conversation',
    cell: ({ row }) => {
      const conversationId = row.getValue('conversationId')
      if (conversationId) {
        return h(
          resolveComponent('NuxtLink'),
          {
            to: `/inbox?id=${conversationId}`,
            style: 'color: #2563eb; text-decoration: underline;',
          },
          { default: () => 'View Conversation' },
        )
      }
      return '—'
    },
  },
  {
    accessorKey: 'type',
    header: 'Type',
  },
  {
    accessorKey: 'listing',
    header: 'Listing',
    cell: ({ row }) => {
      const listing = row.original.listing
      if (listing && listing.id) {
        return h(
          resolveComponent('NuxtLink'),
          {
            to: `/listings/${listing.id}`,
            style: 'color: #2563eb; text-decoration: underline;',
          },
          { default: () => listing.name },
        )
      }
      return '—'
    },
  },
  {
    accessorKey: 'assignees',
    header: 'Assignees',
    cell: ({ row }) => {
      const assignees = row.original.assignees || []
      const taskId = row.original.id
      return h(USelect, {
        'modelValue': assignees.map(a => a.userId || a.id),
        'items': assigneeItems.value,
        'multiple': true,
        'placeholder': 'Select assignees',
        'onUpdate:modelValue': (val: string[]) => {
          // Map selected userIds to user objects (userId + email)
          const selected = (workspaceStore.workspace?.users || [])
            .filter((u: any) => val.includes(u.userId || u.id))
            .map((u: any) => ({ userId: u.userId || u.id, email: u.email }))
          emit('update-assignees', { id: taskId, assignees: selected })
        },
        'class': 'w-[220px] h-8',
        'labelKey': 'label',
        'valueKey': 'value',
      })
    },
  },
]
watch(rowSelection, (val) => {
  const selectedIndices = Object.keys(val).filter(idx => val[idx])
  const selectedIds = selectedIndices.map(idx => filteredTasks.value[Number(idx)]?.id).filter(Boolean)
  emit('selectionChange', selectedIds)
})
</script>

<template>
  <UTable ref="table" v-model:row-selection="rowSelection" :data="filteredTasks" :columns="columns" />
</template>
