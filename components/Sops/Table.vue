<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import { format } from 'date-fns'
import { computed, h } from 'vue'

const props = defineProps<{ sops: Sop[] }>()
const emit = defineEmits(['view-sop'])

interface Sop {
  id: string
  title: string
  description: string
  type?: string
  createdAt?: { _seconds: number; _nanoseconds: number } | number
  updatedAt?: { _seconds: number; _nanoseconds: number } | number
}

const columns: TableColumn<Sop>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => {
      return h('span', {
        style: 'color: #2563eb; cursor: pointer; text-decoration: underline;',
        onClick: () => emit('view-sop', row.original),
      }, row.original.title)
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => row.original.description || '—',
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => row.original.type || '—',
  },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => {
      const value = row.original.createdAt
      let date: Date | undefined
      if (typeof value === 'object' && value?._seconds) {
        date = new Date(value._seconds * 1000)
      } else if (typeof value === 'number') {
        date = new Date(value)
      }
      return date ? format(date, 'yyyy-MM-dd HH:mm') : '—'
    },
  },
]

const data = computed(() => props.sops)
</script>

<template>
  <UTable
    :data="data"
    :columns="columns"
  />
</template>

<style scoped>
</style>
