<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['manage'])

const buttonLabel = computed(() => {
  return props.isActive ? 'Manage' : 'Activate'
})
</script>

<template>
  <UCard>
    <template #header>
      <div class="text-md font-bold">
        {{ name }}
      </div>
    </template>
    <div>
      <div class="text-sm">
        {{ description }}
      </div>
    </div>
    <template #footer>
      <UButton :label="buttonLabel" :color="props.isActive ? 'success' : 'primary'" @click="$emit('manage', name)" />
    </template>
  </UCard>
</template>

<style></style>
