<script setup lang="ts">
import { computed, ref } from 'vue'
import { useWorkspaceStore } from '~/stores/workspace'

const emit = defineEmits<{ close: [boolean] }>()

const workspaceStore = useWorkspaceStore()
const toast = useToast()
const isLoading = ref(false)

const ownerrezIntegrations = computed(() => {
  const calry = workspaceStore?.profile?.integrations?.calry
  if (!calry || typeof calry !== 'object')
    return []

  return Object.values(calry)
    .filter(integration =>
      integration.integrationDefinition?.key === 'ownerrez' && !integration.deleted,
    )
})

async function handleInitiateIntegration() {
  isLoading.value = true
  // Get the link
  try {
    const { linkId } = await useApiFetch(`integrations/calry/link`, {
      method: 'GET',
      query: { pmsId: 'ownerrez' },
    })
  }
  catch {
    toast.add({
      title: 'Error',
      description: 'Failed to initiate OwnerRez integration',
      color: 'error',
    })
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <UModal :close="{ onClick: () => emit('close', false) }" title="OwnerRez">
    <template #body>
      <div v-if="!ownerrezIntegrations.length">
        <UButton label="Initiate OwnerRez Integration" :loading="isLoading" @click="handleInitiateIntegration" />
      </div>
      <div v-else>
        <div v-for="integration in ownerrezIntegrations" :key="integration.integrationAccountId"
          class="mb-4 p-4 border rounded">
          <p><strong>Account Name:</strong> {{ integration.integrationAccountName }}</p>
          <p><strong>Connected On:</strong> {{ new Date(integration.createdAt).toLocaleDateString() }}</p>
          <p><strong>Sync Enabled:</strong> {{ integration.syncEnabled ? 'Yes' : 'No' }}</p>
          <p><strong>Sync Frequency:</strong> {{ integration.syncPeriod }}</p>
        </div>
      </div>
    </template>
  </UModal>
</template>
