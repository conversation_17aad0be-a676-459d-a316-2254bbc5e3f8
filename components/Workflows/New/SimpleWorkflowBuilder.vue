<template>
  <div class="space-y-4">
    <div v-for="(step, index) in steps" :key="step.id">
      <UCard class="relative">
        <template v-if="steps.length > 1" #header>
        </template>
        <UButton v-if="steps.length > 1" color="gray" variant="ghost" icon="i-heroicons-trash" size="sm"
          class="absolute right-2 top-2 z-10" aria-label="Remove message" @click="removeStep(step.id)" />
        <div class="pt-6">
          <div class="mb-4 flex items-center gap-2">
            <div
              class="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-sm font-medium text-gray-700 dark:bg-gray-800 dark:text-gray-300">
              {{ index + 1 }}
            </div>
            <div class="font-medium">{{ step.type === 'delay' ? 'Delay' : `Message ${index + 1}` }}</div>
          </div>
          <div v-if="step.type === 'message'" class="space-y-4">
            <UFormField :label="`Subject Line`" :name="`subject-${step.id}`">
              <UInput :id="`subject-${step.id}`" :model-value="step.subject" placeholder="Email subject line"
                class="flex" @update:model-value="newValue => updateStep(step.id, 'subject', newValue)" />
            </UFormField>
            <UFormField :label="`Message Content`" :name="`body-${step.id}`">
              <UTextarea :id="`body-${step.id}`" :model-value="step.body" placeholder="Write your message here"
                class="flex" :rows="6" @update:model-value="newValue => updateStep(step.id, 'body', newValue)" />
            </UFormField>
          </div>
          <div v-else-if="step.type === 'delay'" class="space-y-4">
            <div class="flex gap-4">
              <UFormField label="Duration" :name="`duration-${step.id}`" class="flex-1">
                <UInput
                  :id="`duration-${step.id}`"
                  type="number"
                  :model-value="step.durationValue"
                  :min="1"
                  :max="step.durationUnit === 'hours' ? 24 : 28"
                  class="flex"
                  @input="(event: Event) => {
                    let min = 1;
                    let max = step.durationUnit === 'hours' ? 24 : 28;
                    let value = Number((event.target as HTMLInputElement).value);
                    if (value < min) value = min;
                    if (value > max) value = max;
                    // Directly set the input value so the user can't type out-of-bounds
                    (event.target as HTMLInputElement).value = value.toString();
                    updateStep(step.id, 'durationValue', value);
                  }"
                />
              </UFormField>
              <UFormField label="Unit" :name="`duration-unit-${step.id}`">
                <USelect :id="`duration-unit-${step.id}`" class="w-48" :model-value="step.durationUnit" :items="[
                  { label: 'Hours', value: 'hours' },
                  { label: 'Days', value: 'days' }
                ]" @update:model-value="newValue => updateStep(step.id, 'durationUnit', newValue)" />
              </UFormField>
            </div>
          </div>
        </div>
      </UCard>
    </div>
    <UButton variant="outline" block @click="addStep">
      <template #leading>
        <UIcon name="i-heroicons-plus" class="h-4 w-4" />
      </template>
      Add Another Message
    </UButton>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

interface Step {
  id: string
  type: string
  subject?: string
  body?: string
  durationValue?: number
  durationUnit?: 'hours' | 'days'
}

const props = defineProps<{ steps: Step[] }>()
const emit = defineEmits(['add-step', 'remove-step', 'update-step'])

const addStep = () => emit('add-step')
const removeStep = (id: string) => emit('remove-step', id)
const updateStep = (id: string, field: 'subject' | 'body' | 'durationValue' | 'durationUnit', value: string | number) => {
  emit('update-step', { id, field, value })
}
</script>

<style scoped>
/* Add any additional styles if necessary */
.u-button.absolute {
  position: absolute;
}
</style>