<template>
  <UCalendar
    v-model="selectedDate"
    :attributes="calendarAttributes"
    class="rounded-md border"
    :day-class="getDayClass"
    :weekday-class="() => 'text-muted-foreground'"
    :month-class="() => 'text-foreground'"
    :prev-button-props="{ icon: 'i-heroicons-chevron-left', color: 'gray', variant: 'ghost' }"
    :next-button-props="{ icon: 'i-heroicons-chevron-right', color: 'gray', variant: 'ghost' }"
  >
    <template #day="{ day }">
      <UChip :show="!!hasMessages(day)">
        {{ day.day }}
      </UChip>
    </template>
  </UCalendar>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { type CalendarDate, today, getLocalTimeZone } from '@internationalized/date'

// Helper to format CalendarDate to YYYY-MM-DD string
const formatDateISO = (date: CalendarDate): string => {
  return `${date.year}-${String(date.month).padStart(2, '0')}-${String(date.day).padStart(2, '0')}`
}

const selectedDate = ref<CalendarDate | undefined>(today(getLocalTimeZone()))

// Sample data for days with scheduled messages
const daysWithMessages = [
  "2025-04-05",
  "2025-04-08",
  "2025-04-09",
  "2025-04-10",
  "2025-04-11",
  "2025-04-12",
  "2025-04-15",
]

const hasMessages = (date: CalendarDate): boolean => {
  const formattedDate = formatDateISO(date)
  return daysWithMessages.includes(formattedDate)
}

// This provides a way to inject content or classes into day cells
// Nuxt UI <UCalendar> has a #day slot for customizing the day cell content
// We can use this slot to achieve the desired customization.

const getDayContentClass = (date: CalendarDate, isSelected: boolean): string[] => {
  const classes: string[] = []
  if (isSelected) {
    classes.push('bg-primary', 'text-primary-foreground', 'rounded-md')
  } else if (hasMessages(date)) {
    classes.push('font-bold')
  }
  return classes
}

// The `attributes` prop can be used for more complex scenarios if needed,
// but the #day slot is generally more flexible for content changes.
// For basic styling, custom classes via the slot are often enough.
const calendarAttributes = computed(() => {
  const attrs: any[] = daysWithMessages.map(isoDate => {
    const [year, month, day] = isoDate.split('-').map(Number)
    return {
      key: `message-${isoDate}`,
      dates: { year, month, day },
      // You could use custom classes here if the slot approach isn't sufficient
      // highlight: {
      //   class: '!bg-blue-100 dark:!bg-blue-900', // Example highlight
      // },
      // Or custom dot indicators (though the slot is better for this specific React example)
      // dot: {
      //   color: 'blue' // Example dot color
      // }
    }
  })

  if (selectedDate.value) {
    attrs.push({
      key: 'selected',
      dates: {
        year: selectedDate.value.year,
        month: selectedDate.value.month,
        day: selectedDate.value.day
      },
      highlight: {
        color: 'primary', // Corresponds to bg-primary
        // class: 'bg-primary text-primary-foreground rounded-md' // More specific
      }
    })
  }
  return attrs
})

// If you needed to apply classes to the button element wrapping the day content directly
// (less common now with the #day slot)
const getDayClass = (date: CalendarDate, { isSelected, isOtherMonth }: { isSelected: boolean, isOtherMonth: boolean }): string => {
  if (isOtherMonth) return 'text-muted-foreground opacity-50' // Default styling for other month days
  let classes = 'hover:bg-muted/50 focus-visible:ring-1 focus-visible:ring-ring focus-visible:rounded-md'
  // if (isSelected) {
  //   classes += ' bg-primary text-primary-foreground hover:bg-primary/90'
  // } else if (hasMessages(date)) {
  //   // This class would be on the button, not the inner div, so font-bold might not apply as expected without the slot.
  //   // The slot approach is better for fine-grained control.
  // }
  return classes
}

</script>