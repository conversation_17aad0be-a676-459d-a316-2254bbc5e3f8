<template>
  <div>
    <div v-if="daySchedule.length === 0" class="py-8 text-center">
      <p class="text-muted-foreground">No messages scheduled for this day</p>
    </div>
    <div v-else class="space-y-3">
      <div
        v-for="item in daySchedule"
        :key="item.id"
        class="flex items-center justify-between rounded-lg border p-3"
      >
        <div class="flex items-center gap-3">
          <div
            class="rounded-full p-2"
            :class="[
              item.status === 'scheduled' ? 'bg-blue-100 dark:bg-blue-900' : 'bg-green-100 dark:bg-green-900',
            ]"
          >
            <UIcon
              v-if="item.type === 'email'"
              name="i-lucide-mail"
              class="h-4 w-4"
              :class="[item.status === 'scheduled' ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400']"
            />
            <UIcon
              v-else
              name="i-lucide-message-square"
              class="h-4 w-4"
              :class="[item.status === 'scheduled' ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400']"
            />
          </div>
          <div>
            <div class="font-medium">{{ item.workflow }}</div>
            <div class="text-sm text-muted-foreground">
              {{ item.guest }} &bull; {{ item.property }}
            </div>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <div class="flex items-center gap-1 text-sm text-muted-foreground">
            <UIcon name="i-lucide-clock" class="h-4 w-4" />
            {{ item.time }}
          </div>
          <UBadge
            :variant="item.status === 'scheduled' ? 'outline' : 'soft'"
            :color="item.status === 'scheduled' ? 'primary' : 'green'"
          >
            {{ item.status === 'scheduled' ? 'Scheduled' : 'Sent' }}
          </UBadge>
          <UButton variant="outline" size="sm">
            View
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Define the type for a schedule item for better type safety
interface ScheduleItem {
  id: string
  workflow: string
  guest: string
  property: string
  time: string
  type: 'email' | 'sms'
  status: 'scheduled' | 'sent'
}

// Sample data for the selected day
// In a real application, this would likely be a prop or fetched data
const daySchedule = ref<ScheduleItem[]>([
  {
    id: "1",
    workflow: "Early Check-in Upsell",
    guest: "Sarah Johnson",
    property: "Oceanview Villa",
    time: "10:00 AM",
    type: "email",
    status: "scheduled",
  },
  {
    id: "2",
    workflow: "Cart Abandonment Recovery",
    guest: "Michael Chen",
    property: "Downtown Loft",
    time: "9:15 AM",
    type: "email",
    status: "sent",
  },
  {
    id: "3",
    workflow: "Late Check-out Upsell",
    guest: "Emma Wilson",
    property: "Mountain Cabin",
    time: "8:30 AM",
    type: "sms",
    status: "sent",
  },
])

// Example: To make daySchedule a prop
// const props = defineProps<{
//   scheduleItems: ScheduleItem[]
// }>()
// And then use props.scheduleItems instead of daySchedule.value
</script>