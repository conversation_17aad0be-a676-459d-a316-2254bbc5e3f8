<template>
  <div>
    <div v-if="todayRuns.length === 0" class="text-center py-8">
      <p class="text-gray-500 dark:text-gray-400">No messages scheduled for today</p>
    </div>
    <div v-else class="space-y-3">
      <div
        v-for="run in todayRuns"
        :key="run.id"
        class="flex items-center justify-between rounded-lg border dark:border-gray-700 p-3"
      >
        <div class="flex items-center gap-3">
          <div
            :class="[
              'rounded-full p-2',
              run.status === 'scheduled'
                ? 'bg-blue-100 dark:bg-blue-800'
                : 'bg-green-100 dark:bg-green-800',
            ]"
          >
            <UIcon
              :name="run.type === 'email' ? 'i-heroicons-envelope' : 'i-heroicons-chat-bubble-left'"
              :class="[
                'h-4 w-4',
                run.status === 'scheduled'
                  ? 'text-blue-600 dark:text-blue-400'
                  : 'text-green-600 dark:text-green-400',
              ]"
            />
          </div>
          <div>
            <div class="font-medium">{{ run.workflow }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ run.guest }} &bull; {{ run.property }}
            </div>
          </div>
        </div>
        <div class="flex items-center gap-3">
          <div class="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
            <UIcon name="i-heroicons-clock" class="h-4 w-4" />
            {{ run.time }}
          </div>
          <UBadge
            :variant="run.status === 'scheduled' ? 'outline' : 'soft'"
            :color="run.status === 'scheduled' ? 'blue' : 'green'"
            size="sm"
          >
            {{ run.status === 'scheduled' ? 'Scheduled' : 'Sent' }}
          </UBadge>
        </div>
      </div>
      <div class="flex justify-center pt-2">
        <NuxtLink to="/workflows/calendar">
          <UButton variant="outline" label="View All Scheduled Messages" />
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Run {
  id: string;
  workflow: string;
  guest: string;
  property: string;
  time: string;
  type: 'email' | 'sms';
  status: 'scheduled' | 'sent';
}

// Sample data for today's runs
const todayRuns = ref<Run[]>([
  {
    id: "1",
    workflow: "Early Check-in Upsell",
    guest: "Sarah Johnson",
    property: "Oceanview Villa",
    time: "10:00 AM",
    type: "email",
    status: "scheduled",
  },
  {
    id: "2",
    workflow: "Cart Abandonment Recovery",
    guest: "Michael Chen",
    property: "Downtown Loft",
    time: "09:15 AM",
    type: "email",
    status: "sent",
  },
  {
    id: "3",
    workflow: "Late Check-out Upsell",
    guest: "Emma Wilson",
    property: "Mountain Cabin",
    time: "08:30 AM",
    type: "sms",
    status: "sent",
  },
])

// In a real application, this data would likely come from a prop or be fetched from an API.
// For example:
// const props = defineProps<{
//   runs: Run[]
// }>()
// const todayRuns = toRef(props, 'runs')

</script>

<style scoped>
/* Add any component-specific styles here if needed */
/* For text-muted-foreground, using text-gray-500 dark:text-gray-400 directly */
</style>