<template>
  <UCard>
    <template #header>
      <div class="pb-1">
        <div class="flex items-start justify-between">
          <div class="space-y-1">
            <h3 class="text-lg font-semibold">{{ title }}</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ description }}</p>
          </div>
          <UDropdownMenu :items="dropdownItems" :popper="{ placement: 'bottom-end' }">
            <UButton color="gray" variant="ghost" icon="i-heroicons-ellipsis-horizontal-20-solid" size="sm" />
          </UDropdownMenu>
        </div>
        <UBadge
          :color="badgeDetails.color"
          :variant="badgeDetails.variant"
          class="mt-2"
          size="sm"
        >
          <UIcon v-if="badgeDetails.icon" :name="badgeDetails.icon" :class="[badgeDetails.text ? 'mr-1' : '', 'h-3 w-3']" />
          {{ badgeDetails.text }}
        </UBadge>
      </div>
    </template>

    <template #footer>
      <div class="pt-0">
        <div class="flex w-full items-center justify-between">
          <div class="text-xs text-gray-500 dark:text-gray-400">Updated {{ lastUpdated }}</div>
          <NuxtLink :to="manageLink">
            <UButton variant="ghost" size="sm">Manage</UButton>
          </NuxtLink>
        </div>
      </div>
    </template>
    </UCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Define Props
const props = withDefaults(defineProps<{
  title: string
  description: string
  status: "active" | "draft" | "archived"
  lastUpdated: string
  simple?: boolean // This prop was in the original interface but not used in its JSX.
  enrolledGuests?: number
  conversionRate?: number
  revenue?: number
}>(), {
  simple: false,
});

// Define Emits for actions
const emit = defineEmits(['edit', 'toggle-status', 'delete']);

// Computed property for the "Manage" link
const manageLink = computed(() => {
  // Basic slugification, consider a more robust slugify function if needed
  const slug = props.title.toLowerCase().replace(/\s+/g, "-").replace(/[^\w-]+/g, "");
  return `/workflows/${slug}`;
});

// Computed property for dynamic dropdown items
const dropdownItems = computed(() => [
  [
    {
      label: 'Edit',
      icon: 'i-heroicons-pencil-square-20-solid',
      click: () => emit('edit', { title: props.title, id: manageLink.value /* or some other unique id */ })
    },
    {
      label: props.status === "active" ? 'Pause Workflow' : 'Activate Workflow',
      icon: props.status === "active" ? 'i-heroicons-pause-circle-20-solid' : 'i-heroicons-play-circle-20-solid',
      click: () => emit('toggle-status', { title: props.title, currentStatus: props.status })
    }
  ],
  [
    {
      label: 'Delete',
      icon: 'i-heroicons-trash-20-solid',
      labelClass: 'text-red-500 dark:text-red-400', // For destructive action indication
      click: () => emit('delete', { title: props.title })
    }
  ]
]);

// Computed property for badge appearance
const badgeDetails = computed(() => {
  switch (props.status) {
    case 'active':
      return { text: 'Active', variant: 'solid' as const, color: 'green' as const, icon: 'i-heroicons-check-circle-solid' };
    case 'draft':
      return { text: 'Draft', variant: 'outline' as const, color: 'yellow' as const, icon: '' }; // No icon specified in original for draft
    case 'archived':
      return { text: 'Archived', variant: 'subtle' as const, color: 'gray' as const, icon: '' }; // No icon specified in original for archived
    default:
      // Fallback, though status should be one of the above
      return { text: props.status, variant: 'subtle' as const, color: 'gray' as const, icon: '' };
  }
});

// If `text-muted-foreground` and `text-destructive` are custom Tailwind classes from shadcn/ui,
// ensure they are defined in your global styles or Tailwind config.
// Nuxt UI typically uses semantic names like `text-gray-500 dark:text-gray-400` for muted text
// and `text-red-500 dark:text-red-400` for destructive text.
// I've used these Nuxt UI/Tailwind conventional classes above.
</script>

<style scoped>
/* Add any component-specific styles here if needed */
/* For example, if you need very precise control over the badge icon that UBadge doesn't offer by default */
.h-3 { height: 0.75rem; }
.w-3 { width: 0.75rem; }
</style>