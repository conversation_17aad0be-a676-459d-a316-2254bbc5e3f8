<script lang="ts" setup>
import { format, isToday } from 'date-fns'
import { useInboxStore } from '#imports'

defineProps<{
  chats: Array<{
    any
  }>
}>()

const inboxStore = useInboxStore()

const chatsRef = ref<Element[]>([])

const selectedId = defineModel<string | null>()
const showLoader = ref(false)

async function onScroll(e: Event) {
  const element = e.target
  if (element.scrollHeight - element.scrollTop === element.clientHeight) {
    if (!showLoader.value) {
      showLoader.value = true
      console.warn('Reached the bottom of the chats list')
      await inboxStore.loadMoreChats()
      showLoader.value = false
    } else {
      console.warn('Already loading chats')
    }
  }
}
</script>

<template>
  <!-- @click="selectedConversation = { ...chat, type: 'chat' };" -->
  <div class="overflow-y-auto divide-y divide-default h-full" @scroll="onScroll">
    <div v-for="(chat, index) in chats" :key="index" :ref="el => { chatsRef[chat.id] = el as Element }">
      <div class="p-4 sm:px-6 text-sm cursor-pointer border-l-2 transition-colors" :class="[
        !chat.read ? 'text-highlighted' : 'text-toned',
        selectedId && selectedId === chat.id ? 'border-primary bg-primary/10' : 'border-(--ui-bg) hover:border-primary hover:bg-primary/5',
      ]" @click="selectedId = chat.id">
        <div class="flex items-center justify-between font-semibold">
          <div class="flex items-center gap-3">
            {{ chat.firstName }} {{ chat.lastName }}

            <UChip v-if="!chat.read" />
          </div>

          <span>{{ isToday(new Date(chat.lastActivityAt)) ? format(new Date(chat.lastActivityAt), 'HH:mm')
            : format(new Date(chat.lastActivityAt), 'dd MMM') }}</span>
        </div>
        <p class="text-dimmed line-clamp-1">
          {{ chat?.lastMessage?.body }}
        </p>
      </div>
    </div>
    <div v-if="showLoader" class="flex justify-center py-4">
      <span class="loader" />
    </div>
  </div>
</template>

<style>
.loader {
  width: 48px;
  height: 48px;
  border: 5px solid #FFF;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>