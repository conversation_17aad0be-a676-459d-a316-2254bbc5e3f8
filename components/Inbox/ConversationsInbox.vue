<script setup lang="ts">
import { format } from 'date-fns'
import { collection, orderBy, query, where } from 'firebase/firestore'
import { useRoute } from 'vue-router'
import { useCollection } from 'vuefire'

defineProps<{
  conversation: any
  contact?: any
  stay?: any
  listing?: any
  tasks?: any
}>()

const emits = defineEmits(['close'])

const { $db } = useNuxtApp()
const route = useRoute()

// Create a computed that returns the collection reference based on route
const messagesQuery = computed(() => {
  if (!route.query.id)
    return null
  return query(collection($db, `conversations/${route.query.id}/messages`), orderBy('createdAt', 'asc'))
})
const messages = useCollection(messagesQuery)
const messagesContainer = ref<HTMLElement>()

// Auto-scroll to bottom when messages change
watch(messages, () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}, { deep: true })

const pauseDropdownItems = [[{
  label: 'Resume Autopilot',
  icon: 'i-lucide-play',
}], [{
  label: 'Pause for 15 minutes',
  icon: 'i-lucide-circle-pause',
}, {
  label: 'Pause for 1 hour',
  icon: 'i-lucide-circle-pause',
}, {
  label: 'Pause for 1 day',
  icon: 'i-lucide-circle-pause',
}, {
  label: 'Pause indefinitely',
  icon: 'i-lucide-circle-pause',
}]]

const filterDropdownItems = [[{
  label: 'All Messages',
  icon: 'i-lucide-calendar',
}, {
  label: 'Autopilot Messages',
  icon: 'i-lucide-bot-message-square',
}, {
  label: 'BuyTime Messages',
  icon: 'i-lucide-clock',
}, {
  label: 'Upsell Messages',
  icon: 'i-lucide-dollar-sign',
}, {
  label: 'Marketing Messages',
  icon: 'i-lucide-handshake',
}]]

const toast = useToast()

const reply = ref('')
const loading = ref(false)
const sidebarOpen = ref(true)

// Dynamic width calculation
const dynamicWidth = ref('100vw')

function updateDynamicWidth() {
  const viewportWidth = window.innerWidth
  let totalSubtractWidth = 0

  // Get dashboard sidebar width
  const dashboardSidebar = document.getElementById('dashboard-sidebar-default')
  if (dashboardSidebar) {
    totalSubtractWidth += dashboardSidebar.offsetWidth
  }

  // Get dashboard panel inbox width
  const dashboardPanel = document.getElementById('dashboard-panel-inbox-1')
  if (dashboardPanel) {
    totalSubtractWidth += dashboardPanel.offsetWidth
  }

  const calculatedWidth = Math.max(0, viewportWidth - totalSubtractWidth)
  dynamicWidth.value = `${calculatedWidth}px`
}

async function onSubmit() {
  loading.value = true
  const tempReply = reply.value.trim()
  reply.value = ''
  try {
    await useApiFetch(`conversations/${route.query.id}/messages`, {
      method: 'POST',
      body: {
        body: tempReply,
      },
    })
  } catch (error: any) {
    toast.add({
      title: 'Error sending message',
      description: error.message || 'An error occurred while sending your message.',
      color: 'error'
    })
    loading.value = false
    return
  } finally {
    loading.value = false
  }
}

// Helper functions for message styling
function getMessageTypeColor(type: string) {
  switch (type) {
    case 'ai':
      return 'bg-purple-500'
    case 'upsell':
      return 'bg-green-500'
    case 'agent':
      return 'bg-blue-500'
    case 'guest':
    default:
      return 'bg-gray-500'
  }
}

function getMessageIcon(type: string) {
  switch (type) {
    case 'ai':
      return 'i-lucide-bot'
    case 'upsell':
      return 'i-lucide-trending-up'
    case 'agent':
      return 'i-lucide-user'
    case 'guest':
    default:
      return 'i-lucide-message-circle'
  }
}

function getMessageTypeBadge(type: string) {
  switch (type) {
    case 'ai':
      return 'AI'
    case 'upsell':
      return 'Upsell'
    case 'agent':
      return 'Agent'
    case 'guest':
      return 'Guest'
    default:
      return null
  }
}

function getMessageTypeBadgeColor(type: string) {
  switch (type) {
    case 'ai':
      return 'purple'
    case 'upsell':
      return 'green'
    case 'agent':
      return 'blue'
    case 'guest':
    default:
      return 'gray'
  }
}

onMounted(() => {
  updateDynamicWidth()
  window.addEventListener('resize', updateDynamicWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateDynamicWidth)
})
</script>

<template>
  <UDashboardPanel id="inbox-conversation" class="flex flex-col h-full">
    <UDashboardNavbar :title="conversation.subject || conversation.contactName || 'Conversation'" :toggle="false">
      <template #right>
        <UDropdownMenu :items="filterDropdownItems">
          <UButton icon="i-lucide-filter" color="neutral" variant="ghost" />
        </UDropdownMenu>
        <UDropdownMenu :items="pauseDropdownItems">
          <UButton icon="i-lucide-pause" color="neutral" variant="ghost" />
        </UDropdownMenu>
      </template>
    </UDashboardNavbar>

    <div class="flex flex-1 min-h-0" :style="{ width: dynamicWidth }">
      <div class="flex flex-col flex-1 overflow-hidden">
        <div ref="messagesContainer" class="flex-1 p-4 sm:p-6 overflow-y-auto min-h-0">
          <div v-if="messages && messages.length > 0" class="space-y-4 max-w-none w-full">
            <div v-for="message in messages" :key="message.createdAt" class="flex w-full" :class="[
              message.type === 'us' ? 'justify-end' : 'justify-start',
            ]">
              <div class="w-[70%]" :class="[
                message.type === 'us' ? 'order-2' : 'order-1',
              ]">
                <div class="flex items-center gap-2 mb-1">
                  <span class="text-sm font-medium">
                    {{ message.type === 'us' ? 'You' : (message.sender || conversation.contactName
                      || conversation.guestName
                      || 'Guest') }}
                  </span>

                  <UBadge v-if="getMessageTypeBadge(message.type || (message.type === 'us' ? 'agent' : 'guest'))"
                    :color="getMessageTypeBadgeColor(message.type || (message.type === 'us' ? 'agent' : 'guest'))"
                    size="xs">
                    {{ getMessageTypeBadge(message.type || (message.type === 'us' ? 'agent' : 'guest')) }}
                  </UBadge>

                  <span class="text-xs text-gray-400">
                    {{ format(new Date(message.createdAt), 'HH:mm') }}
                  </span>
                </div>
                <UCard class="min-w-0">
                  <p class="break-all">
                    {{ message.body
                    }}
                  </p>
                </UCard>
              </div>
            </div>
          </div>
          <div v-else class="flex items-center justify-center h-full text-gray-500">
            <p>No messages in this conversation</p>
          </div>
        </div>

        <div class="pb-2 px-4 sm:px-6 shrink-0">
          <UCard variant="subtle" class="mt-auto">
            <form @submit.prevent="onSubmit">
              <UTextarea v-model="reply" color="neutral" variant="none" required autoresize
                placeholder="Write your reply..." :rows="4" :disabled="loading" class="w-full"
                :ui="{ base: 'p-0 resize-none' }" />

              <div class="flex items-center justify-end">
                <UButton type="submit" color="neutral" :loading="loading" label="Send" icon="i-lucide-send" />
              </div>
            </form>
          </UCard>
        </div>
      </div>

      <!-- Right sidebar -->
      <div v-if="sidebarOpen" class="w-80 border-l border-default bg-subtle/50 flex flex-col">
        <div class="p-4 border-b border-default">
          <h3 class="font-semibold text-highlighted flex items-center gap-2">
            <UIcon name="i-lucide-info" class="size-4" />
            Details
          </h3>
        </div>

        <div class="flex-1 overflow-y-auto p-4 space-y-6">
          <!-- Contact Information -->
          <div v-if="contact" class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-user" class="size-4" />
              Contact
            </h4>
            <UCard variant="subtle" class="p-3">
              <div class="space-y-2">
                <div v-if="contact.name" class="flex justify-between">
                  <span class="text-muted text-sm">Name:</span>
                  <span class="text-sm font-medium">{{ contact.name }}</span>
                </div>
                <div v-if="contact.email" class="flex justify-between">
                  <span class="text-muted text-sm">Email:</span>
                  <span class="text-sm">{{ contact.email }}</span>
                </div>
                <div v-if="contact.phone" class="flex justify-between">
                  <span class="text-muted text-sm">Phone:</span>
                  <span class="text-sm">{{ contact.phone }}</span>
                </div>
                <div v-if="contact.location" class="flex justify-between">
                  <span class="text-muted text-sm">Location:</span>
                  <span class="text-sm">{{ contact.location }}</span>
                </div>
              </div>
            </UCard>
          </div>

          <!-- Stay Information -->
          <div v-if="stay" class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-calendar" class="size-4" />
              Stay
            </h4>
            <UCard variant="subtle" class="p-3">
              <div class="space-y-2">
                <div v-if="stay.checkIn" class="flex justify-between">
                  <span class="text-muted text-sm">Check-in:</span>
                  <span class="text-sm font-medium">{{ format(new Date(stay.checkIn), 'dd MMM yyyy') }}</span>
                </div>
                <div v-if="stay.checkOut" class="flex justify-between">
                  <span class="text-muted text-sm">Check-out:</span>
                  <span class="text-sm font-medium">{{ format(new Date(stay.checkOut), 'dd MMM yyyy') }}</span>
                </div>
                <div v-if="stay.guests" class="flex justify-between">
                  <span class="text-muted text-sm">Guests:</span>
                  <span class="text-sm">{{ stay.guests }}</span>
                </div>
                <div v-if="stay.status" class="flex justify-between">
                  <span class="text-muted text-sm">Status:</span>
                  <UBadge :color="stay.status === 'confirmed' ? 'green' : stay.status === 'pending' ? 'yellow' : 'gray'"
                    size="xs">
                    {{ stay.status }}
                  </UBadge>
                </div>
                <div v-if="stay.totalAmount" class="flex justify-between">
                  <span class="text-muted text-sm">Total:</span>
                  <span class="text-sm font-medium">${{ stay.totalAmount }}</span>
                </div>
              </div>
            </UCard>
          </div>

          <!-- Listing Information -->
          <div v-if="listing" class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-home" class="size-4" />
              Property
            </h4>
            <UCard variant="subtle" class="p-3">
              <div class="space-y-2">
                <div v-if="listing.name" class="flex justify-between">
                  <span class="text-muted text-sm">Name:</span>
                  <span class="text-sm font-medium">{{ listing.name }}</span>
                </div>
                <div v-if="listing.address" class="flex justify-between">
                  <span class="text-muted text-sm">Address:</span>
                  <span class="text-sm">{{ listing.address }}</span>
                </div>
                <div v-if="listing.type" class="flex justify-between">
                  <span class="text-muted text-sm">Type:</span>
                  <span class="text-sm">{{ listing.type }}</span>
                </div>
                <div v-if="listing.bedrooms" class="flex justify-between">
                  <span class="text-muted text-sm">Bedrooms:</span>
                  <span class="text-sm">{{ listing.bedrooms }}</span>
                </div>
                <div v-if="listing.bathrooms" class="flex justify-between">
                  <span class="text-muted text-sm">Bathrooms:</span>
                  <span class="text-sm">{{ listing.bathrooms }}</span>
                </div>
              </div>
            </UCard>
          </div>

          <!-- Tasks -->
          <div v-if="tasks && tasks.length > 0" class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-check-square" class="size-4" />
              Tasks
              <UBadge color="gray" size="xs">
                {{ tasks.length }}
              </UBadge>
            </h4>
            <div class="space-y-2">
              <UCard v-for="task in tasks" :key="task.id" variant="subtle" class="p-3">
                <div class="flex items-start gap-3">
                  <UIcon :name="task.completed ? 'i-lucide-check-circle-2' : 'i-lucide-circle'"
                    class="size-4 mt-0.5 flex-shrink-0" :class="[
                      task.completed ? 'text-green-500' : 'text-gray-400',
                    ]" />
                  <div class="flex-1 min-w-0">
                    <p class="text-sm" :class="[
                      task.completed ? 'line-through text-muted' : 'text-highlighted',
                    ]">
                      {{ task.title }}
                    </p>
                    <p v-if="task.dueDate" class="text-xs text-muted mt-1">
                      Due: {{ format(new Date(task.dueDate), 'dd MMM') }}
                    </p>
                  </div>
                  <UBadge v-if="task.priority"
                    :color="task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'gray'" size="xs">
                    {{ task.priority }}
                  </UBadge>
                </div>
              </UCard>
            </div>
          </div>

          <!-- Quick Actions -->
          <!-- <div class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-zap" class="size-4" />
              Quick Actions
            </h4>
            <div class="space-y-2">
              <UButton color="neutral" variant="soft" size="sm" block icon="i-lucide-user-plus">
                View Contact
              </UButton>
              <UButton v-if="stay" color="neutral" variant="soft" size="sm" block icon="i-lucide-calendar">
                View Booking
              </UButton>
              <UButton v-if="listing" color="neutral" variant="soft" size="sm" block icon="i-lucide-home">
                View Property
              </UButton>
              <UButton color="neutral" variant="soft" size="sm" block icon="i-lucide-plus">
                Add Task
              </UButton>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </UDashboardPanel>
</template>
