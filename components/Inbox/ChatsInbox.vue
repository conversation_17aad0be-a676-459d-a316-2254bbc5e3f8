<script setup lang="ts">
import { format } from 'date-fns'
import { collection, orderBy, query, where } from 'firebase/firestore'
import { useRoute } from 'vue-router'
import { useCollection } from 'vuefire'

defineProps<{
  chat: any,
  contact?: any,
}>()

const emits = defineEmits(['close'])

const { $db } = useNuxtApp()
const route = useRoute()

const dropdownItems = [[{
  label: 'Mark as unread',
  icon: 'i-lucide-check-circle'
}, {
  label: 'Mark as important',
  icon: 'i-lucide-triangle-alert'
}], [{
  label: 'Star thread',
  icon: 'i-lucide-star'
}, {
  label: 'Mute thread',
  icon: 'i-lucide-circle-pause'
}]]

const toast = useToast()

const reply = ref('')
const loading = ref(false)
const sidebarOpen = ref(true)
const dynamicWidth = ref('100vw')

const messagesQuery = computed(() => {
  if (!route.query.id)
    return null
  return query(collection($db, `chats/${route.query.id}/messages`), orderBy('createdAt', 'asc'))
})

const messages = useCollection(messagesQuery)

// Create ref for the messages container
const messagesContainer = ref<HTMLElement>()

// Auto-scroll to bottom when messages change
watch(messages, () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}, { deep: true })

function getMessageCardClass(type: string) {
  switch (type) {
    case 'SYSTEM_ERROR':
      return 'border-red-200 bg-red-50'
    case 'GUEST_MESSAGE':
      return 'bg-blue-50 border-blue-200'
    case 'SYSTEM_PROMPT':
      return 'bg-gray-50 border-gray-200'
    default:
      return ''
  }
}



async function onSubmit() {
  loading.value = true
  const tempReply = reply.value.trim()
  reply.value = ''
  try {
    await useApiFetch(`chats/${route.query.id}/messages`, {
      method: 'POST',
      body: {
        body: tempReply,
        type: 'GUEST_MESSAGE',
      },
    })
  } catch (error: any) {
    toast.add({
      title: 'Error sending message',
      description: error.message || 'An error occurred while sending your message.',
      color: 'error'
    })
    loading.value = false
    return
  } finally {
    loading.value = false
  }
}

function updateDynamicWidth() {
  const viewportWidth = window.innerWidth
  let totalSubtractWidth = 0
  const dashboardSidebar = document.getElementById('dashboard-sidebar-default')
  if (dashboardSidebar) {
    totalSubtractWidth += dashboardSidebar.offsetWidth
  }
  const dashboardPanel = document.getElementById('dashboard-panel-inbox-1')
  if (dashboardPanel) {
    totalSubtractWidth += dashboardPanel.offsetWidth
  }

  const calculatedWidth = Math.max(0, viewportWidth - totalSubtractWidth)
  dynamicWidth.value = `${calculatedWidth}px`
}

onMounted(() => {
  updateDynamicWidth()
  window.addEventListener('resize', updateDynamicWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateDynamicWidth)
})
</script>

<template>
  <UDashboardPanel id="inbox-2" class="flex flex-col h-full">
    <UDashboardNavbar
      :title="chat.subject || (chat.firstName && chat.lastName ? `${chat.firstName} ${chat.lastName}` : 'Chat')"
      :toggle="false">
      <template #leading>
        <UButton icon="i-lucide-x" color="neutral" variant="ghost" class="-ms-1.5" @click="emits('close')" />
      </template>

      <template #right>
      </template>
    </UDashboardNavbar>
    <div class="flex flex-1 min-h-0" :style="{ width: dynamicWidth }">
      <div class="flex flex-col flex-1 overflow-hidden">
        <div ref="messagesContainer" class="flex-1 p-4 sm:p-6 overflow-y-auto min-h-0">
          <div v-if="messages && messages.length > 0" class="space-y-4 max-w-none w-full">
            <div v-for="message in messages" :key="message.createdAt" class="flex w-full" :class="[
              message.type === 'GUEST_MESSAGE' ? 'justify-end' : 'justify-start',
            ]">
              <div class="w-[70%]" :class="[
                message.type === 'GUEST_MESSAGE' ? 'order-2' : 'order-1',
              ]">
                <div class="flex items-center gap-2 mb-1">
                  <span class="text-sm font-medium">
                    {{ message.type === 'GUEST_MESSAGE' ? (chat.firstName && chat.lastName ? `${chat.firstName}
                                        ${chat.lastName}` : chat.guestName || 'Guest') : 'System' }}
                  </span>
                  <span class="text-xs text-gray-400">
                    {{ format(new Date(message.createdAt), 'HH:mm') }}
                  </span>
                </div>
                <UCard class="min-w-0" :class="getMessageCardClass(message.type)">
                  <p class="break-words whitespace-pre-wrap">
                    {{ message.body }}
                  </p>
                </UCard>
              </div>
            </div>
          </div>
          <div v-else class="flex items-center justify-center h-full text-gray-500">
            <p>No messages in this conversation</p>
          </div>
        </div>
        <div class="pb-2 px-4 sm:px-6 shrink-0">
          <UCard variant="subtle" class="mt-auto">
            <form @submit.prevent="onSubmit">
              <UTextarea v-model="reply" color="neutral" variant="none" required autoresize
                placeholder="Write your reply..." :rows="4" :disabled="loading" class="w-full"
                :ui="{ base: 'p-0 resize-none' }" />

              <div class="flex items-center justify-end">
                <UButton type="submit" color="neutral" :loading="loading" label="Send" icon="i-lucide-send" />
              </div>
            </form>
          </UCard>
        </div>
      </div>

      <div v-if="sidebarOpen" class="w-80 border-l border-default bg-subtle/50 flex flex-col">
        <div class="p-4 border-b border-default">
          <h3 class="font-semibold text-highlighted flex items-center gap-2">
            <UIcon name="i-lucide-info" class="size-4" />
            Details
          </h3>
        </div>

        <div class="flex-1 overflow-y-auto p-4 space-y-6">
          <!-- Contact Information -->
          <div v-if="contact" class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-user" class="size-4" />
              Contact
            </h4>
            <UCard variant="subtle" class="p-3">
              <div class="space-y-2">
                <div v-if="contact.firstName || contact.lastName" class="flex justify-between">
                  <span class="text-muted text-sm">Name:</span>
                  <span class="text-sm font-medium">{{ [contact.firstName, contact.lastName].filter(Boolean).join(' ')
                  }}</span>
                </div>
                <div v-if="contact.email" class="flex justify-between">
                  <span class="text-muted text-sm">Email:</span>
                  <span class="text-sm">{{ contact.email }}</span>
                </div>
                <div v-if="contact.phone" class="flex justify-between">
                  <span class="text-muted text-sm">Phone:</span>
                  <span class="text-sm">{{ contact.phone }}</span>
                </div>
              </div>
            </UCard>
          </div>

          <!-- Location Information -->
          <div v-if="chat.location" class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-map-pin" class="size-4" />
              Location
            </h4>
            <UCard variant="subtle" class="p-3">
              <div class="space-y-2">
                <div v-if="chat.location.city && chat.location.region" class="flex justify-between">
                  <span class="text-muted text-sm">City:</span>
                  <span class="text-sm font-medium">{{ chat.location.city }}, {{ chat.location.region_code ||
                    chat.location.region }}</span>
                </div>
                <div v-if="chat.location.country_name" class="flex justify-between items-center">
                  <span class="text-muted text-sm">Country:</span>
                  <div class="flex items-center gap-2">
                    <span class="text-sm">{{ chat.location.country_name }}</span>
                    <span v-if="chat.location.emoji_flag" class="text-sm">{{ chat.location.emoji_flag }}</span>
                  </div>
                </div>
                <div v-if="chat.location.postal" class="flex justify-between">
                  <span class="text-muted text-sm">Postal Code:</span>
                  <span class="text-sm">{{ chat.location.postal }}</span>
                </div>
                <div v-if="chat.location.time_zone?.name" class="flex justify-between">
                  <span class="text-muted text-sm">Time Zone:</span>
                  <span class="text-sm">{{ chat.location.time_zone.abbr }} ({{ chat.location.time_zone.offset }})</span>
                </div>
                <div v-if="chat.location.ip" class="flex justify-between">
                  <span class="text-muted text-sm">IP Address:</span>
                  <span class="text-sm text-gray-600">{{ chat.location.ip }}</span>
                </div>
              </div>
            </UCard>
          </div>

          <!-- Quick Actions -->
          <div class="space-y-3">
            <h4 class="font-medium text-highlighted flex items-center gap-2">
              <UIcon name="i-lucide-zap" class="size-4" />
              Quick Actions
            </h4>
            <div class="space-y-2">
              <UButton v-if="contact" color="neutral" variant="soft" size="sm" block icon="i-lucide-user-plus">
                View Contact
              </UButton>
              <UButton color="neutral" variant="soft" size="sm" block icon="i-lucide-plus">
                Add Task
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UDashboardPanel>
</template>