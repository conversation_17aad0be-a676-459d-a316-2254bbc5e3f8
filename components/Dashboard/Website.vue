<script setup lang="ts">
import { useWorkspaceStore } from '#imports'

const workspaceStore = useWorkspaceStore();

const error = ref(null)

const hasValidData = computed(() => {
  return !!workspaceStore.counts
})

const hasValidChartData = computed(() => {
  return !!workspaceStore.views && Array.isArray(workspaceStore.views) && workspaceStore.views.length > 0
})

const categories: Record<string, BulletLegendItemInterface> = {
  guidebookViews: { name: 'Guidebook Views', color: '#3b82f6' },
  websiteViews: { name: 'Website Views', color: '#22c55e' },
}

const chartData = computed(() => {
  if (!workspaceStore.views)
    return []

  return workspaceStore.views.map((item: any) => {
    const date = new Date(item.event_date)
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    })

    return {
      date: formattedDate,
      guidebookViews: item.guidebook_view_count_per_day,
      websiteViews: item.website_view_count_per_day,
    }
  })
})

const stats = computed(() => {
  if (!hasValidData.value)
    return []

  try {
    const totalViews = {
      current: workspaceStore.counts[0].website_view_count_last_30_days ?? 0,
      previous: workspaceStore.counts[0].website_view_count_prev_30_days ?? 0,
    }

    console.log('lolsack', workspaceStore.counts)

    const percentageChange = calculatePercentageChange(totalViews.current, totalViews.previous)
    const formatChange = (change) => {
      if (change === undefined || change === null)
        return undefined
      return typeof change === 'number' ? `${change.toFixed(1)}% from last month` : `${change} growth from last month`
    }

    return [
      {
        title: 'Total Views',
        value: totalViews.current.toString(),
        change: percentageChange ? formatChange(percentageChange) : undefined,
        icon: 'pi pi-eye',
      },
      {
        title: 'Unique Visitors',
        value: (workspaceStore.counts[0].unique_website_sessions_last_30_days ?? 0).toString(),
        change: calculatePercentageChange(
          workspaceStore.counts[0].unique_website_sessions_last_30_days ?? 0,
          workspaceStore.counts[0].unique_website_sessions_prev_30_days ?? 0,
        )
          ? formatChange(calculatePercentageChange(
            workspaceStore.counts[0].unique_website_sessions_last_30_days ?? 0,
            workspaceStore.counts[0].unique_website_sessions_prev_30_days ?? 0,
          ))
          : undefined,
        icon: 'pi pi-user',
      },
      {
        title: 'Total Chats',
        value: (workspaceStore.chats?.find(c => c.period === 'last_30_days')?.new_conversations ?? 0).toString(),
        change: formatChange(calculatePercentageChange(
          workspaceStore.chats?.find(c => c.period === 'last_30_days')?.new_conversations ?? 0,
          workspaceStore.chats?.find(c => c.period === 'prev_30_days')?.new_conversations ?? 0,
        )),
        icon: 'pi pi-comments',
      },
    ]
  }
  catch (err) {
    console.error('Error generating stats:', err)
    return []
  }
})

// Watch for data validity and update error state
watch([hasValidData, hasValidChartData], ([hasData, hasChart]) => {
  if (!hasData) {
    error.value = 'No statistics data available'
  }
  else if (!hasChart) {
    error.value = 'No view data available'
  }
  else {
    error.value = null
  }
}, { immediate: true })

function calculatePercentageChange(current, previous) {
  try {
    if (current === undefined || current === null)
      return 0
    if (!previous || previous === 0) {
      return current > 0 ? '∞' : 0
    }
    return ((current - previous) / previous) * 100
  }
  catch (err) {
    console.error('Error calculating percentage change:', err)
    return 0
  }
}
</script>

<template>
  <div>
    <UCard class="!bg-(--ui-bg)">
      <template #header>
        <div class="mb-2">
          <h2 class="text-lg font-medium">
            Direct Booking Website
          </h2>
          <p class="text-sm text-(--ui-text-muted)">
            Website views, live chats, and popup submissions over the last 30 days.
          </p>
        </div>
        <div class="grid grid-cols-3 gap-2">
          <div v-for="stat, index in stats" :key="index" class="border border-gray-200 rounded-lg p-1">
            <div class="flex items-center gap-2">
              <div class="flex-shrink-0">
                <i :class="stat.icon" class="text-xl text-(--ui-primary)" />
              </div>
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-(--ui-text) truncate">
                  {{ stat.title }}
                </h3>
                <p class="text-lg font-semibold text-(--ui-text)">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>


      <!-- <template #footer>
        <div class="flex justify-end gap-2">
          <UButton variant="link" trailing-icon="i-lucide-external-link" color="neutral">
            View transactions
          </UButton>
        </div>
      </template> -->
    </UCard>
  </div>
</template>
