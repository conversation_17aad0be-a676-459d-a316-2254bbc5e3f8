<script setup lang="ts">
import { useWorkspaceStore } from '#imports'

const workspaceStore = useWorkspaceStore()

const totalContacts = computed(() => {
  const contacts = workspaceStore.contacts?.recent || {}
  return (contacts.chatContacts?.length || 0)
    + (contacts.guidebookContacts?.length || 0)
    + (contacts.popupContacts?.length || 0)
})

const previousTotalContacts = computed(() => {
  const contacts = workspaceStore.contacts?.previous || {}
  return (contacts.chatContacts?.length || 0)
    + (contacts.guidebookContacts?.length || 0)
    + (contacts.popupContacts?.length || 0)
})

const chatContactsCount = computed(() => {
  return workspaceStore.contacts?.recent?.chatContacts?.length || 0
})

const previousChatCount = computed(() => {
  return workspaceStore.contacts?.previous?.chatContacts?.length || 0
})

const guidebookContactsCount = computed(() => {
  return workspaceStore.contacts?.recent?.guidebookContacts?.length || 0
})

const previousGuidebookCount = computed(() => {
  return workspaceStore.contacts?.previous?.guidebookContacts?.length || 0
})

const popupContactsCount = computed(() => {
  return workspaceStore.contacts?.recent?.popupContacts?.length || 0
})

const previousPopupCount = computed(() => {
  return workspaceStore.contacts?.previous?.popupContacts?.length || 0
})

function calculatePercentageChange(current, previous) {
  // Handle invalid inputs
  if (typeof current !== 'number' || typeof previous !== 'number') {
    return '0'
  }

  // Handle zero cases
  if (previous === 0) {
    return current > 0 ? '+100' : '0'
  }

  try {
    const change = ((current - previous) / previous) * 100
    // Handle potential NaN or Infinity
    if (!Number.isFinite(change)) {
      return '0'
    }
    return change > 0 ? `+${change.toFixed(1)}` : change.toFixed(1)
  }
  catch (error) {
    console.error('Error calculating percentage change:', error)
    return '0'
  }
}

const stats = computed(() => {
  return [
    {
      title: 'Total',
      value: totalContacts.value.toString(),
      change: `${calculatePercentageChange(totalContacts.value, previousTotalContacts.value)}% from last month`,
      icon: 'pi pi-users',
    },
    {
      title: 'Guidebook',
      value: guidebookContactsCount.value.toString(),
      change: `${calculatePercentageChange(guidebookContactsCount.value, previousGuidebookCount.value)}% from last month`,
      icon: 'pi pi-book',
    },
    {
      title: 'Chat',
      value: chatContactsCount.value.toString(),
      change: `${calculatePercentageChange(chatContactsCount.value, previousChatCount.value)}% from last month`,
      icon: 'pi pi-comments',
    },
    {
      title: 'Popup',
      value: popupContactsCount.value.toString(),
      change: `${calculatePercentageChange(popupContactsCount.value, previousPopupCount.value)}% from last month`,
      icon: 'pi pi-exclamation-triangle',
    },
  ]
})

const recentContacts = computed(() => {
  try {
    const contacts = workspaceStore.contacts?.recent || {}
    const allContacts = [
      ...(contacts.chatContacts || []).map(contact => ({ ...contact, source: 'Chat' })),
      ...(contacts.guidebookContacts || []).map(contact => ({ ...contact, source: 'Guidebook' })),
      ...(contacts.popupContacts || []).map(contact => ({ ...contact, source: 'Popup' })),
    ]

    return allContacts
      .sort((a, b) => ((b.lastActivityAt || 0) - (a.lastActivityAt || 0)))
      .slice(0, 5)
      .map(contact => ({
        name: contact.fullName || contact.email || 'Unknown',
        source: contact.source || 'Unknown',
        date: contact.lastActivityAt
          ? new Date(contact.lastActivityAt).toLocaleDateString()
          : 'N/A',
      }))
  }
  catch (error) {
    console.error('Error processing recent contacts:', error)
    return []
  }
})

const contactsBySource = computed(() => {
  try {
    const contacts = workspaceStore.contacts?.recent || {}
    const sources = {}

    // Use _ prefix for unused parameters to satisfy linter
    contacts.guidebookContacts?.forEach((_contact) => {
      sources.Guidebook = (sources.Guidebook || 0) + 1
    })

    contacts.chatContacts?.forEach((_contact) => {
      sources.Chat = (sources.Chat || 0) + 1
    })

    contacts.popupContacts?.forEach((_contact) => {
      sources.Popup = (sources.Popup || 0) + 1
    })

    return sources
  }
  catch (error) {
    console.error('Error processing contacts by source:', error)
    return {}
  }
})
</script>

<template>
  <div>
    <UCard class="!bg-(--ui-bg)">
      <template #header>
        <div class="mb-2">
          <h2 class="text-lg font-medium">
            Yada Contacts
          </h2>
          <p class="text-sm text-(--ui-text-muted)">
            Contacts and leads generated and enriched by Yada over the last 30 days.
          </p>
        </div>
        <div class="grid grid-cols-3 gap-2">
          <div v-for="stat, index in stats" :key="index" class="border border-gray-200 rounded-lg p-1 bg-white">
            <div class="flex items-center gap-2">
              <div class="flex-shrink-0">
                <i :class="stat.icon" class="text-xl text-(--ui-primary)" />
              </div>
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-(--ui-text) truncate">
                  {{ stat.title }}
                </h3>
                <p class="text-lg font-semibold text-(--ui-text)">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>
      <UTable v-if="recentContacts" :data="recentContacts" />
    </UCard>
  </div>
</template>
