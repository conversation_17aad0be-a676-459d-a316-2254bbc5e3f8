// Utility functions for workflow templates
import { winbackTemplate, earlyCheckInUpsellTemplate } from './index'

export type WorkflowTemplateType = 'winback' | 'upsell'

export function getWorkflowFromTemplate(type: WorkflowTemplateType) {
    switch (type) {
        case 'winback':
            // Deep clone to avoid mutating the original template
            return JSON.parse(JSON.stringify(winbackTemplate))
        case 'upsell':
            return JSON.parse(JSON.stringify(earlyCheckInUpsellTemplate))
        default:
            throw new Error('Unknown template type')
    }
}

// Step management logic
export function canAddStep(workflow: any) {
    // Only allow more than one step for winback
    return workflow.type === 'winback'
}

export function addMessageStep(workflow: any) {
    if (workflow.type === 'upsell') {
        // Only one step allowed for upsell
        return workflow
    }
    // For winback, add a delay and a message step at the end
    const stepKeys = Object.keys(workflow.steps).map(Number)
    const lastStep = Math.max(...stepKeys)
    const nextDelay = lastStep + 1
    const nextMessage = lastStep + 2
    workflow.steps[nextDelay] = { type: 'delay', duration: '1d' }
    workflow.steps[nextMessage] = {
        type: 'message',
        subject: '',
        body: ''
    }
    return workflow
}

export function deleteLastMessageStep(workflow: any) {
    if (workflow.type === 'upsell') {
        // Only one step, do nothing
        return workflow
    }
    // For winback, remove the last delay and message step pair
    const stepKeys = Object.keys(workflow.steps).map(Number)
    if (stepKeys.length < 2) return workflow
    const lastStep = Math.max(...stepKeys)
    const secondLastStep = lastStep - 1
    delete workflow.steps[lastStep]
    delete workflow.steps[secondLastStep]
    return workflow
} 