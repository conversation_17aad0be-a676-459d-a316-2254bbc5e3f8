export const winbackTemplate = {
    name: 'Inquiry Winback',
    description: 'A workflow to win back guests who have made an inquiry but did not book.',
    type: 'winback',
    trigger: 'inquiry',
    listings: [],
    steps: {
        1: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, are you sure you do not want to book?'
        },
        2: {
            type: 'delay',
            duration: '1d'
        },
        3: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, we are still here if you want to book.'
        },
        4: {
            type: 'delay',
            duration: '1d'
        },
        5: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, we are still here if you want to book. If you have any questions, please let us know.'
        },
        6: {
            type: 'delay',
            duration: '1d'
        },
        7: {
            type: 'message',
            subject: 'We noticed you inquired about a stay with us',
            body: 'Hi, we are still here if you want to book.'
        }
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true
}


export const earlyCheckInUpsellTemplate = {
    name: 'Early Check-In Upsell',
    description: 'A workflow to upsell early check-in to guests who have booked.',
    type: 'upsell',
    trigger: 'checkin',
    triggerTiming: {
        time: '5d',
        when: 'before'
    },
    listings: [],
    steps: {
        1: {
            type: 'message',
            subject: 'Early Check-In Available',
            body: 'Hi, we have early check-in available for your stay. Would you like to add it?'
        },
    },
    channels: {
        ota: true,
        email: true,
        sms: true
    },
    active: true

}