export const listingAmenities = [
    {
        id: 'cableTv',
        name: 'Cable TV',
        hostawayIds: [1],
        guestyAmenityNames: ['Cable TV'],
        hostfullyAmenityNames: ['HAS_CABLE_TV'],
        ownerrezAmenityNames: ['Satellite/Cable'],
        tags: ['entertainment'],
        priority: 2,
        oldAmenityKeys: ['cable'],
        trueText: 'The cable TV provided is YoutubeTV and Vizio WatchFREE. You are also welcome to log into your streaming apps',
    },
    {
        id: 'smartTv',
        name: 'Smart TV',
        hostawayIds: [287],
        hostfullyAmenityNames: ['HAS_SMART_TV'],
        ownerrezAmenityNames: ['Smart TV'],
        tags: ['entertainment'],
        priority: 5,
        trueText: 'The cable TV provided is YoutubeTV and Vizio WatchFREE. You are also welcome to log into your streaming apps',
    },
    {
        id: 'streamingServices',
        name: 'Streaming Services',
        tags: ['entertainment'],
        priority: 5,
        oldAmenityKeys: ['streamingServices'],
        trueText: 'The cable TV provided is YoutubeTV and Vizio WatchFREE. You are also welcome to log into your streaming apps',
    },
    {
        id: 'tv',
        name: 'TV',
        hostawayIds: [34],
        airbnbAmenityNames: ['TV'],
        guestyAmenityNames: ['TV'],
        hostfullyAmenityNames: ['HAS_TV'],
        ownerrezAmenityNames: ['Television'],
        tags: ['entertainment'],
        priority: 1,
        autoPopulate: true,
        oldAmenityKeys: ['tv'],
        trueText: 'The cable TV provided is YoutubeTV and Vizio WatchFREE. You are also welcome to log into your streaming apps',
    },
    {
        id: 'videoGames',
        name: 'Video games',
        hostawayIds: [147],
        airbnbAmenityNames: ['Game console'],
        guestyAmenityNames: ['Game console'],
        hostfullyAmenityNames: ['HAS_GAME_CONSOLE'],
        ownerrezAmenityNames: ['Video Games'],
        tags: ['entertainment'],
        priority: 3,
        oldAmenityKeys: ['gameConsole'],
        trueText: 'We provide access to the Playstation and Xbox. We store them under the basement TV.',
    },
    {
        id: 'poolTable',
        name: 'Pool table',
        hostawayIds: [146],
        guestyAmenityNames: ['Pool table'],
        hostfullyAmenityNames: ['HAS_POOL_TABLE'],
        ownerrezAmenityNames: ['Pool Table'],
        tags: ['entertainment'],
        priority: 3,
        trueText: 'The pool table is located in the basement',
    },
    {
        id: 'internet',
        name: 'Internet / WiFi',
        hostawayIds: [2, 3, 105, 280, 281],
        guestyAmenityNames: ['Internet', 'Pocket wifi', 'Wireless Internet'],
        airbnbAmenityNames: ['Wifi'],
        hostfullyAmenityNames: ['HAS_INTERNET_WIFI', 'HAS_PAID_WIFI', 'HAS_ETHERNET_CONNECTION'],
        ownerrezAmenityNames: ['Internet', 'Wifi', 'Ethernet', 'Free Internet', 'Paid Internet', 'Pocket Wifi'],
        tags: ['internet', 'productivity'],
        priority: 1,
        oldAmenityKeys: ['internet', 'wifi'],
        autoPopulate: true,
        falseText: 'This home does not have WiFi but there is a Starbucks with internet service a few miles down the road',
        trueText: 'WIFI NETWORK: Banana PASSWORD: 5starstay 250mbps - and the router is right under the TV',
    },
    {
        id: 'airConditioning',
        name: 'Air conditioning',
        airbnbAmenityNames: ['air conditioning'],
        hostawayIds: [4],
        guestyAmenityNames: ['Air conditioning'],
        hostfullyAmenityNames: ['HAS_AIR_CONDITIONING'],
        ownerrezAmenityNames: ['Air Conditioning'],
        tags: ['hvac'],
        priority: 2,
        oldAmenityKeys: ['ac'],
        autoPopulate: true,
        falseText: 'This property does not have air conditioning however there are ceiling fans in all the bedrooms',
        trueText: 'The air conditioning is controlled via the google wall thermostats',
    },
    {
        id: 'wheelchair',
        name: 'Wheelchair and Handicap Accessible',
        hostawayIds: [5],
        guestyAmenityNames: ['Wheelchair accessible'],
        hostfullyAmenityNames: ['IS_WHEELCHAIR_ACCESSIBLE'],
        ownerrezAmenityNames: ['Wheelchair accessible'],
        tags: ['access'],
        priority: 2,
        autoPopulate: true,
        oldAmenityKeys: ['wheelchair'],
        falseText: 'This home is not wheelchair & handicap accessible, it\'s 3 steps up into the home and bedrooms are all upstairs.',
        trueText: 'This home is wheelchair friendly, there is a full bathroom and a bedroom on the main level and it\'s only 2 steps to get into the home',
    },
    {
        id: 'kitchen',
        name: 'Kitchen',
        hostawayIds: [7],
        guestyAmenityNames: ['Kitchen'],
        airbnbAmenityNames: ['Kitchen'],
        hostfullyAmenityNames: ['HAS_KITCHEN', 'HAS_KITCHENETTE'],
        ownerrezAmenityNames: ['Kitchen', 'Kitchenette'],
        tags: ['kitchenDining'],
        priority: 2,
        oldAmenityKeys: ['kitchen'],
        trueText: 'The kitchen is located on the main level and it is stocked with everything you need and seats 12 people total',
    },
    {
        id: 'gym',
        name: 'Gym',
        airbnbAmenityNames: ['Gym'],
        hostawayIds: [9],
        guestyAmenityNames: ['Gym'],
        hostfullyAmenityNames: ['HAS_GYM'],
        ownerrezAmenityNames: ['Fitness Room/Equipment', 'Gym'],
        tags: ['sports'],
        priority: 2,
        oldAmenityKeys: ['gym'],
    },
    {
        id: 'breakfast',
        name: 'Breakfast Services included during your stay',
        hostawayIds: [10, 218],
        guestyAmenityNames: ['Breakfast'],
        hostfullyAmenityNames: ['HAS_BREAKFAST'],
        tags: ['services'],
        priority: 5,
        oldAmenityKeys: ['breakfast'],
    },
    {
        id: 'hotTub',
        name: 'Hot tub',
        hostawayIds: [11, 12],
        guestyAmenityNames: ['Hot tub'],
        hostfullyAmenityNames: ['HAS_HOT_TUB', 'HAS_JACUZZI'],
        ownerrezAmenityNames: ['Hot Tub', 'Jacuzzi'],
        tags: ['hottubPool', 'entertainment'],
        priority: 1,
        oldAmenityKeys: ['hotTub'],
        autoPopulate: true,
        trueText: 'The hot tub fits 5 people and it is open year round. Please be sure not to take the chlorine floater out of the tub, that keeps the water clean',
    },
    {
        id: 'fireplace',
        name: 'Fireplace',
        airbnbAmenityNames: ['Fireplace'],
        hostawayIds: [12],
        guestyAmenityNames: ['Indoor fireplace'],
        hostfullyAmenityNames: ['HAS_INDOOR_FIREPLACE'],
        ownerrezAmenityNames: ['Fireplace'],
        tags: ['entertainment'],
        priority: 2,
        oldAmenityKeys: ['fireplace'],
    },
    {
        id: 'clothesWasher',
        name: 'Clothes Washing Machine',
        hostawayIds: [13],
        airbnbAmenityNames: ['Washer'],
        guestyAmenityNames: ['Washer', 'Washer in common space'],
        hostfullyAmenityNames: ['HAS_WASHER', 'HAS_SHARED_WASHER'],
        tags: ['laundry'],
        priority: 1,
        oldAmenityKeys: ['washer'],
        autoPopulate: true,
        falseText: 'We do not have laundry at the property but there is a laundromat 2 miles down the road on Route 125',
        trueText: 'The washer and dryer is located in the upstairs common area. Please remember to clear the dryer vent each use.',
    },
    {
        id: 'clothesDryer',
        name: 'Clothes Dryer',
        hostawayIds: [14],
        airbnbAmenityNames: ['Dryer'],
        guestyAmenityNames: ['Dryer', 'Dryer in common space'],
        hostfullyAmenityNames: ['HAS_DRYER', 'HAS_SHARED_DRYER'],
        tags: ['laundry'],
        priority: 1,
        oldAmenityKeys: ['dryer'],
        autoPopulate: true,
        falseText: 'We do not have laundry at the property but there is a laundromat 2 miles down the road on Route 125',
        trueText: 'The washer and dryer is located in the upstairs common area. Please remember to clear the dryer vent each use.',
    },
    {
        id: 'elevator',
        name: 'Elevator',
        airbnbAmenityNames: ['elevator'],
        hostawayIds: [15],
        guestyAmenityNames: ['Elevator'],
        hostfullyAmenityNames: ['HAS_ELEVATOR'],
        tags: ['access'],
        priority: 2,
        oldAmenityKeys: ['elevator'],
        falseText: 'Unfortunately we do not have an elevator at this property, however there is only 2 steps to get in the home and there is a bathroom and bedroom on the main level.',
        trueText: 'The elevator can be accessed from the lobby and will take you right to floor 4. It fits 6 people.',
    },
    {
        id: 'twentyFourHourCheckin',
        name: '24-hour checkin',
        hostawayIds: [16],
        hostfullyAmenityNames: ['HAS_24_HOUR_CHECKIN'],
        tags: ['access'],
        priority: 5,
        oldAmenityKeys: ['twentyFourHourCheckin'],
        falseText: 'Check in time is after 4pm and before 8pm, our office locks up at 8:15PM',
        trueText: 'You are able to check in 24 hours a day as long as it is after your check in because you can use the keypad lock',
    },
    {
        id: 'hairDryer',
        name: 'Hair Dryer',
        hostawayIds: [17],
        guestyAmenityNames: ['Hair dryer'],
        airbnbAmenityNames: ['Hair dryer'],
        hostfullyAmenityNames: ['HAS_HAIR_DRYER'],
        ownerrezAmenityNames: ['Hair Dryer'],
        tags: ['bathroom'],
        priority: 2,
        oldAmenityKeys: ['hairDryer'],
        autoPopulate: true,
        falseText: 'This home does not have a hair dryer, but you are welcome to bring your own',
        trueText: 'Typically we leave the hair dryer under the bathroom sink',
    },
    {
        id: 'heating',
        name: 'Heating',
        hostawayIds: [18],
        airbnbAmenityNames: ['Heating'],
        guestyAmenityNames: ['Heating'],
        hostfullyAmenityNames: ['HAS_HEATING'],
        ownerrezAmenityNames: ['Central Heating'],
        tags: ['hvac'],
        priority: 1,
        oldAmenityKeys: ['heating'],
        autoPopulate: true,
        falseText: 'This property does not have a heating system but it typically is very warm year round',
        trueText: 'The heating is controlled via the google wall thermostats',
    },
    {
        id: 'doorman',
        name: 'Doorman',
        hostawayIds: [19],
        guestyAmenityNames: ['Doorman'],
        hostfullyAmenityNames: ['HAS_DOORMAN'],
        tags: ['access'],
        priority: 5,
        oldAmenityKeys: ['doorman'],
        falseText: 'This property does not have a doorman, you will access the property with a code that we will send 2 days prior to your arrival',
        trueText: 'The doorman is on staff 24/7',
    },
    {
        id: 'smokeDetector',
        name: 'Smoke detector',
        airbnbAmenityNames: ['Smoke alarm'],
        hostawayIds: [25],
        guestyAmenityNames: ['Smoke detector'],
        hostfullyAmenityNames: ['HAS_SMOKE_DETECTOR'],
        tags: ['safety'],
        priority: 2,
        oldAmenityKeys: ['smokeDetector'],
    },
    {
        id: 'carbonMonoxideDetector',
        name: 'Carbon Monoxide Detector',
        airbnbAmenityNames: ['Carbon monoxide detector'],
        hostawayIds: [26],
        guestyAmenityNames: ['Carbon monoxide detector'],
        hostfullyAmenityNames: ['HAS_CARBON_MONOXIDE_DETECTOR'],
        tags: ['safety'],
        priority: 2,
        oldAmenityKeys: ['carbonMonoxideDetector'],
    },
    {
        id: 'firstAidKit',
        name: 'First aid kit',
        airbnbAmenityNames: ['First aid kit'],
        hostawayIds: [27],
        guestyAmenityNames: ['First aid kit'],
        hostfullyAmenityNames: ['HAS_FIRST_AID_KIT'],
        tags: ['safety'],
        priority: 3,
        oldAmenityKeys: ['firstAidKit'],
        falseText: 'There is no first aid kit at this property but CVS is right around the corner',
        trueText: 'The first aid kit is typically left in the medicine cabinet of the first floor bathroom',
    },
    {
        id: 'fireExtinguisher',
        name: 'Fire Extinguisher',
        airbnbAmenityNames: ['Fire extinguisher'],
        hostawayIds: [28],
        guestyAmenityNames: ['Fire extinguisher'],
        hostfullyAmenityNames: ['HAS_FIRE_EXTINGUISHER'],
        tags: ['safety'],
        priority: 3,
        oldAmenityKeys: ['fireExtinguisher'],
    },
    {
        id: 'essentials',
        name: 'Essentials',
        hostawayIds: [29],
        airbnbAmenityNames: ['Essentials'],
        guestyAmenityNames: ['Essentials'],
        hostfullyAmenityNames: ['HAS_ESSENTIALS'],
        tags: ['general'],
        priority: 2,
        oldAmenityKeys: ['essentials'],
    },
    {
        id: 'shampoo',
        name: 'Shampoo',
        hostawayIds: [30],
        guestyAmenityNames: ['Shampoo'],
        airbnbAmenityNames: ['Shampoo'],
        hostfullyAmenityNames: ['HAS_SHAMPOO'],
        ownerrezAmenityNames: ['Shampoo'],
        tags: ['bathroom'],
        priority: 2,
        oldAmenityKeys: ['shampoo'],
        autoPopulate: true,
        falseText: 'We do not provide shampoo or toiletries so please remember to bring your own',
        trueText: 'We provide shampoo, conditioner and body wash. It will usually be in the shower.',
    },
    {
        id: 'hangers',
        name: 'Hangers',
        hostawayIds: [31],
        airbnbAmenityNames: ['Hangers'],
        guestyAmenityNames: ['Hangers'],
        hostfullyAmenityNames: ['HAS_HANGERS'],
        ownerrezAmenityNames: ['Hangers'],
        tags: ['laundry'],
        priority: 2,
        oldAmenityKeys: ['hangers', 'hangars'],
        trueText: 'We have hangers throughout the house in the closets',
    },
    {
        id: 'clothesIron',
        name: 'Iron',
        hostawayIds: [32],
        airbnbAmenityNames: ['Iron'],
        guestyAmenityNames: ['Iron'],
        hostfullyAmenityNames: ['HAS_IRON', 'HAS_IRONING_FACILITIES'],
        ownerrezAmenityNames: ['Iron & Board'],
        tags: ['laundry'],
        priority: 2,
        oldAmenityKeys: ['iron'],
        autoPopulate: true,
    },
    {
        id: 'laptopFriendly',
        name: 'Laptop Friendly workspace',
        airbnbAmenityNames: ['Dedicated workspace'],
        hostawayIds: [33],
        guestyAmenityNames: ['Laptop friendly workspace'],
        hostfullyAmenityNames: ['HAS_OFFICE', 'HAS_DESK', 'IS_LAPTOP_FRIENDLY'],
        ownerrezAmenityNames: ['Laptop Friendly Workspace'],
        tags: ['productivity'],
        priority: 2,
        oldAmenityKeys: ['laptopFriendlyWorkplace'],
        falseText: 'There is not really a good laptop friendly workspace at this property but starbucks is right down the street',
        trueText: 'The best place to work from with your laptop is the stand up desk upstairs or the table in the basement',
    },
    {
        id: 'soundSystem',
        name: 'Sound system',
        hostawayIds: [35],
        guestyAmenityNames: ['Stereo system', 'Sound system'],
        hostfullyAmenityNames: ['HAS_SOUND_SYSTEM'],
        ownerrezAmenityNames: ['Stereo', 'Sound System'],
        tags: ['entertainment'],
        priority: 2,
        oldAmenityKeys: ['stereoSystem'],
    },
    {
        id: 'smoking',
        name: 'Smoking',
        hostawayIds: [36],
        guestyAmenityNames: ['Smoking allowed'],
        ownerrezAmenityNames: ['Smoking allowed'],
        tags: ['property'],
        priority: 2,
    },
    {
        id: 'pets',
        name: 'Pets',
        hostawayIds: [37],
        guestyAmenityNames: ['Pets allowed'],
        ownerrezAmenityNames: ['Pets welcome'],
        tags: ['pets'],
        priority: 2,
        autoPopulate: true,
        trueText: 'We do allow up to 2 dogs for $125 per dog',
    },
    {
        id: 'parking',
        name: 'Parking',
        airbnbAmenityNames: ['Parking'],
        hostawayIds: [39, 38, 52, 80, 200, 314],
        guestyAmenityNames: [
            'Free parking on premises',
            'Free parking on street',
            'Paid parking off premises',
            'Disabled parking spot',
            'Paid parking on premises',
        ],
        hostfullyAmenityNames: [
            'HAS_FREE_PARKING',
            'HAS_FREE_STREET_PARKING',
            'HAS_PAID_OFF_PREMISES_PARKING',
            'HAS_PAID_ON_PREMISES_PARKING',
        ],
        ownerrezAmenityNames: ['Parking', 'Free Parking', 'Limited Parking'],
        tags: ['parking', 'cars'],
        priority: 1,
        oldAmenityKeys: ['paidParkingOffPremise', 'disabledParkingSpot', 'freeStreetParking'],
        autoPopulate: true,
        falseText: 'This home does not come with parking, you can park on the street from Monday-Friday and on weekends you can use the SpotHero App',
        trueText: 'We have parking for 3-4 cars on the right side of the home, if you have more than that we recommend parking on the street or the City Place Garage.',
    },
    {
        id: 'suitableForEvents',
        name: 'Suitable for events',
        hostawayIds: [41],
        guestyAmenityNames: ['Suitable for events'],
        tags: ['theme'],
        priority: 2,
    },
    {
        id: 'balcony',
        name: 'Patio or Balcony',
        airbnbAmenityNames: ['Balcony'],
        hostawayIds: [45, 67],
        guestyAmenityNames: ['Patio or balcony'],
        hostfullyAmenityNames: ['HAS_BALCONY_TERRASSE', 'HAS_DECK_PATIO'],
        ownerrezAmenityNames: ['Deck/Patio (uncovered)', 'Lanai/Gazebo (covered)', 'Balcony/Terrace', 'Sunroof/Roof Terrace'],
        tags: ['property'],
        priority: 2,
        oldAmenityKeys: ['patioBalcony'],
    },
    {
        id: 'keyCardAccess',
        name: 'Electronic key card access',
        hostawayIds: [46],
        tags: ['access'],
        priority: 5,
    },
    {
        id: 'privateLivingRoom',
        name: 'Private living room',
        hostawayIds: [47],
        ownerrezAmenityNames: ['Private Living Room'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'suitableKids',
        name: 'Suitable for Kids & Infants',
        hostawayIds: [48, 49],
        guestyAmenityNames: [
            'Suitable for children (2-12 years)',
            'Suitable for infants (under 2 years)',
        ],
        ownerrezAmenityNames: ['Children welcome', 'Infants welcome'],
        tags: ['property'],
        priority: 2,
        oldAmenityKeys: ['suitableChildren', 'suitableInfants'],
    },
    {
        id: 'ironingBoard',
        name: 'Iron board',
        hostawayIds: [53],
        ownerrezAmenityNames: ['Iron & Board'],
        tags: ['laundry'],
        priority: 2,
        autoPopulate: true,
        oldAmenityKeys: ['ironBoard'],
        falseText: 'We do not have laundry an ironing board at the property but you are welcome to bring one',
        trueText: 'The iron board is typically hanging on the back of the laundry room room on the upper level. The iron is located in the laundry closet',
    },
    {
        id: 'linens',
        name: 'Linens',
        hostawayIds: [54],
        airbnbAmenityNames: ['Bed linens'],
        guestyAmenityNames: ['Bed linens'],
        hostfullyAmenityNames: ['HAS_LINENS'],
        ownerrezAmenityNames: ['Linens Provided'],
        tags: ['bedroom'],
        priority: 1,
        oldAmenityKeys: ['bedLinens'],
        autoPopulate: true,
        falseText: 'We do not provide linens so be sure to bring your own',
        trueText: 'All the beds will have linens on them upon your arrival',
    },
    {
        id: 'bbq',
        name: 'Outdoor grill',
        airbnbAmenityNames: ['bbq'],
        hostawayIds: [55],
        guestyAmenityNames: ['BBQ grill', 'Barbeque utensils'],
        hostfullyAmenityNames: ['HAS_BARBECUE'],
        ownerrezAmenityNames: ['Outdoor Grill'],
        tags: ['entertainment'],
        priority: 1,
        oldAmenityKeys: ['bbq', 'bbqGrill'],
        autoPopulate: true,
    },
    {
        id: 'toaster',
        name: 'Toaster',
        airbnbAmenityNames: ['Toaster'],
        hostawayIds: [56],
        guestyAmenityNames: ['Toaster'],
        hostfullyAmenityNames: ['HAS_TOASTER'],
        ownerrezAmenityNames: ['Toaster'],
        tags: ['kitchenDining'],
        priority: 2,
        oldAmenityKeys: ['toaster'],
        trueText: 'The toaster fits 4 pieces of toast and it id right next to the microwave',
    },
    {
        id: 'dishwasher',
        name: 'Dishwasher',
        airbnbAmenityNames: ['Dishwasher'],
        hostawayIds: [57],
        guestyAmenityNames: ['Dishwasher'],
        hostfullyAmenityNames: ['HAS_DISHWASHER'],
        ownerrezAmenityNames: ['Dishwasher'],
        tags: ['kitchenDining'],
        priority: 1,
        oldAmenityKeys: ['dishwasher'],
        autoPopulate: true,
        trueText: 'The LG dishwasher is located next to the kitchen sink and the cascade dishwasher fluid is under the kitchen sink',
    },
    {
        id: 'microwave',
        name: 'Microwave',
        airbnbAmenityNames: ['Microwave'],
        hostawayIds: [58],
        guestyAmenityNames: ['Microwave'],
        hostfullyAmenityNames: ['HAS_MICROWAVE_OVEN'],
        ownerrezAmenityNames: ['Microwave'],
        tags: ['kitchenDining'],
        priority: 1,
        oldAmenityKeys: ['microwave'],
        autoPopulate: true,
    },
    {
        id: 'oven',
        name: 'Oven',
        hostawayIds: [59],
        guestyAmenityNames: ['Oven'],
        hostfullyAmenityNames: ['HAS_OVEN'],
        ownerrezAmenityNames: ['Oven'],
        tags: ['kitchenDining'],
        priority: 1,
        oldAmenityKeys: ['oven'],
        autoPopulate: true,
        trueText: 'The oven is located in the kitchen and all the sheet pans are underneath it',
    },
    {
        id: 'kettle',
        name: 'Electric kettle',
        airbnbAmenityNames: ['Kettle'],
        hostawayIds: [60],
        guestyAmenityNames: ['Kettle'],
        hostfullyAmenityNames: ['HAS_WATER_KETTLE'],
        ownerrezAmenityNames: ['Kettle'],
        tags: ['kitchenDining'],
        priority: 2,
        oldAmenityKeys: ['kettle'],
        autoPopulate: true,
        trueText: 'The electric kettle for hot water is typically left in the pantry',
    },
    {
        id: 'coffeeMaker',
        name: 'Coffee/tea maker',
        airbnbAmenityNames: ['Coffee maker'],
        hostawayIds: [61],
        guestyAmenityNames: ['Coffee maker'],
        hostfullyAmenityNames: ['HAS_COFFEE_MAKER'],
        ownerrezAmenityNames: ['Coffee Maker'],
        tags: ['kitchenDining'],
        priority: 1,
        oldAmenityKeys: ['coffeeMaker'],
        autoPopulate: true,
        trueText: 'We provide a keurig coffee maker and its on the kitchen countertop. The keurig pods are in the cabinet above it.',
    },
    {
        id: 'shower',
        name: 'Shower',
        guestyAmenityNames: ['Shower', 'Bathtub'],
        airbnbAmenityNames: ['Bathtub'],
        hostawayIds: [62],
        tags: ['bathroom'],
        priority: 1,
        alwaysSet: true,
        falseText: 'This property does not have a shower but there is a communal shower down the road.',
        trueText: 'The shower is located in the upstairs bathroom. The bath tub is in the main level bathroom.',
    },
    {
        id: 'bathtub',
        name: 'Tub',
        hostawayIds: [63],
        guestyAmenityNames: ['Shower', 'Bathtub'],
        airbnbAmenityNames: ['Bathtub'],
        hostfullyAmenityNames: ['HAS_BATHTUB'],
        tags: ['bathroom'],
        priority: 2,
        oldAmenityKeys: ['bathtub'],
        falseText: 'This property does not feature any bath tubs, we do have 2 stand up showers.',
        trueText: 'The bath tub is located in the main level bathroom',
    },
    {
        id: 'bidet',
        name: 'Bidet',
        hostawayIds: [64],
        airbnbAmenityNames: ['Bidet'],
        hostfullyAmenityNames: ['HAS_BIDET'],
        ownerrezAmenityNames: ['Bidet'],
        tags: ['bathroom'],
        priority: 5,
        falseText: 'This property does not have a bidet',
        trueText: 'The bidet is located in the primary bedroom upstairs',
    },
    {
        id: 'crib',
        name: 'Baby Crib or Pack N Play',
        hostawayIds: [66, 138],
        airbnbAmenityNames: ['Crib'],
        guestyAmenityNames: ['Crib', 'Pack \'n Play/travel crib'],
        hostfullyAmenityNames: ['HAS_BABY_TRAVEL_BED', 'HAS_PACK_N_PLAY_TRAVEL_CRIB'],
        ownerrezAmenityNames: ['Pack N Play/Travel Crib'],
        tags: ['bedroom', 'general'],
        priority: 1,
        oldAmenityKeys: ['crib'],
        autoPopulate: true,
        falseText: 'We do not provide cribs or pack n plays at this property but you are welcome to bring your own or rent one on BabyQuip',
        trueText: 'The baby crib is located in the primary bedroom. We store the pack n play in the primary closet if you need that as well',
    },
    {
        id: 'stove',
        name: 'Stove',
        airbnbAmenityNames: ['Stove'],
        hostawayIds: [68],
        guestyAmenityNames: ['Stove'],
        hostfullyAmenityNames: ['HAS_STOVE'],
        ownerrezAmenityNames: ['Stove'],
        tags: ['kitchenDining'],
        priority: 1,
        oldAmenityKeys: ['stove'],
        autoPopulate: true,
    },
    {
        id: 'refrigerator',
        name: 'Refrigerator',
        airbnbAmenityNames: ['Refrigerator'],
        hostawayIds: [69, 358],
        guestyAmenityNames: ['Refrigerator'],
        hostfullyAmenityNames: ['HAS_FRIDGE', 'HAS_MINI_FRIDGE'],
        ownerrezAmenityNames: ['Refrigerator', 'Mini Fridge'],
        tags: ['kitchenDining'],
        priority: 1,
        oldAmenityKeys: ['refrigerator'],
        trueText: 'The refridgerator is located in the kitchen. There is also a small beer fridge in the basement',
    },
    {
        id: 'towels',
        name: 'Towels & Extra Towels',
        hostawayIds: [70],
        guestyAmenityNames: ['Towels provided'],
        hostfullyAmenityNames: ['HAS_TOWELS'],
        ownerrezAmenityNames: ['Towels Provided'],
        tags: ['bathroom'],
        priority: 1,
        oldAmenityKeys: ['towelsProvided'],
        autoPopulate: true,
        falseText: 'We do not provide towels so be sure to bring your own',
        trueText: 'We leave 2 towels and one face cloth out per bedroom. We leave 2 hand towels in the bathrooms',
    },
    {
        id: 'diningRoom',
        name: 'Dining room',
        hostawayIds: [71],
        tags: ['kitchenDining'],
        priority: 3,
        trueText: 'The formal dining room is located by the front door and it has seating for 8',
    },
    {
        id: 'garden',
        name: 'Garden or backyard',
        airbnbAmenityNames: ['Backyard'],
        hostawayIds: [72],
        guestyAmenityNames: ['Garden or backyard', 'Garden view'],
        hostfullyAmenityNames: ['HAS_GARDEN'],
        ownerrezAmenityNames: ['Lawn/Garden', 'Private Yard'],
        tags: ['property', 'outdoor'],
        priority: 2,
        oldAmenityKeys: ['gardenBackyard'],
    },
    {
        id: 'highChair',
        name: 'High chair',
        hostawayIds: [73],
        guestyAmenityNames: ['High chair'],
        hostfullyAmenityNames: ['HAS_BABY_HIGH_CHAIR'],
        tags: ['property'],
        priority: 2,
        oldAmenityKeys: ['highChair'],
    },
    {
        id: 'dishesSilverware',
        name: 'Dishes, Silverware, Cookware, and Kitchen utensils',
        airbnbAmenityNames: ['Cookware', 'Dishes and silverware'],
        hostawayIds: [74],
        guestyAmenityNames: ['Dishes and silverware', 'Cookware'],
        hostfullyAmenityNames: ['HAS_CROCKERY_CUTLERY', 'HAS_POTS_PANS'],
        ownerrezAmenityNames: ['Dishes & Utensils', 'Pots & Pans'],
        tags: ['kitchenDining'],
        priority: 2,
        oldAmenityKeys: ['dishesAndSilverware', 'cookware'],
        trueText: 'We provide cooking basics which include spatulas, pans, silverware, cups, plates and bowls',
    },
    {
        id: 'singleLevelHome',
        name: 'Single level home',
        hostawayIds: [75],
        guestyAmenityNames: ['Single level home'],
        hostfullyAmenityNames: ['HAS_SINGLE_LEVEL'],
        tags: ['property', 'access'],
        priority: 3,
        oldAmenityKeys: ['singleLevelHome'],
    },
    {
        id: 'stepFreeAccess',
        name: 'Home step free access',
        hostawayIds: [364, 76],
        guestyAmenityNames: ['Step-free access'],
        tags: ['access', 'property'],
        priority: 2,
        oldAmenityKeys: ['stepFreeAccess'],
        autoPopulate: true,
        falseText: 'This property has a 2-3 steps to get into the property',
        trueText: 'This home does not have any steps, so quite easy for kids and elderly guests to access the front door',
    },
    {
        id: 'pathToEntranceLit',
        name: 'Path to entrance lit at night',
        hostawayIds: [77],
        guestyAmenityNames: ['Path to entrance lit at night'],
        tags: ['access', 'property'],
        priority: 2,
        oldAmenityKeys: ['pathToEntranceLitAtNight'],
        falseText: 'Unfortunately the path to the entrance is not lit at night so be careful as you are arriving at the house. Once you are inside you can turn on the exterior lights nest to the garage door.',
        trueText: 'The path to the entrance is lit at night by a motion sensor light',
    },
    {
        id: 'widePassage',
        name: 'Wide Doorways and Hallways',
        hostawayIds: [78, 92, 97, 365],
        guestyAmenityNames: ['Wide doorway', 'Wide hallway clearance'],
        tags: ['access', 'property'],
        priority: 2,
        oldAmenityKeys: ['wideDoorway', 'wideHallway'],
        falseText: 'Unfortunately this property does not have wide doorways and hallways. We are not handicap accessible.',
        trueText: 'The doorways and hallways are all at least 30 inches wide',
    },
    {
        id: 'flatPathwayToDoor',
        name: 'Flat smooth pathway to front door',
        hostawayIds: [79],
        guestyAmenityNames: ['Flat smooth pathway to front door'],
        tags: ['access', 'property'],
        priority: 2,
        oldAmenityKeys: ['flatPathwayToDoor'],
        falseText: 'The pathway to the front door is actually several steps down the hill',
        trueText: 'The entryway is a flat smooth pathway to the front door and there are 2 steps up into the house',
    },
    {
        id: 'bedroomStepFreeAccess',
        name: 'Bedroom step free access',
        hostawayIds: [81],
        tags: ['bedroom', 'access'],
        priority: 5,
        trueText: 'There are 2 bedrooms on the main level that do not require steps. There are however 2 steps to get into the home',
    },
    {
        id: 'wideClearanceToBed',
        name: 'Wide clearance to bed & bedroom',
        hostawayIds: [82, 83],
        guestyAmenityNames: ['Wide clearance to bed'],
        tags: ['bedroom'],
        priority: 5,
        oldAmenityKeys: ['wideBedClearance'],
    },
    {
        id: 'accessibleHeightBed',
        name: 'Accessible height bed',
        hostawayIds: [84],
        guestyAmenityNames: ['Accessible-height bed'],
        tags: ['bedroom'],
        priority: 5,
        oldAmenityKeys: ['accessibleHeightBed'],
        trueText: 'all of the beds on the far side of the house are accesible height',
    },
    {
        id: 'electricProfilingBed',
        name: 'Electric profiling bed',
        hostawayIds: [85],
        tags: ['bedroom'],
        priority: 5,
    },
    {
        id: 'bathroomStepFreeAccess',
        name: 'Bathroom step free access',
        hostawayIds: [86],
        tags: ['bathroom'],
        priority: 5,
        falseText: 'The only bathroom we have is upstairs, there is not one on the main level.',
        trueText: 'There is a single full bathroom on the main level that does not require any steps',
    },
    {
        id: 'grabRails',
        name: 'Grab rails in shower',
        hostawayIds: [87, 88],
        guestyAmenityNames: ['Grab-rails for shower and toilet'],
        tags: ['bathroom'],
        priority: 5,
        oldAmenityKeys: ['grabRailsShowerToilet'],
        falseText: 'This property does not feature grab rails in the shower, sorry for the inconvenience',
        trueText: 'The grab rail in the shower is about 32 inches off the ground and there is a small lip to access the shower',
    },
    {
        id: 'accessibleHeightToilet',
        name: 'Accessible height toilet',
        hostawayIds: [89],
        guestyAmenityNames: ['Accessible-height toilet'],
        tags: ['bathroom'],
        priority: 5,
        oldAmenityKeys: ['accessibleHeightToilet'],
    },
    {
        id: 'rollinShower',
        name: 'Rollin shower with Bench or Chair',
        hostawayIds: [90, 91],
        guestyAmenityNames: [
            'Roll-in shower with shower bench or chair',
            'Shower bench',
            'Shower chair',
        ],
        tags: ['bathroom'],
        priority: 5,
        oldAmenityKeys: ['rollInShower'],
    },
    {
        id: 'tubShowerBench',
        name: 'Tub with shower bench',
        hostawayIds: [93],
        guestyAmenityNames: ['Tub with shower bench'],
        tags: ['bathroom'],
        priority: 5,
        trueText: 'The tub with the shower bench is located in the main level bathroom',
    },
    {
        id: 'wideClearanceToShowerAndToilet',
        name: 'Wide clearance to shower and toilet',
        hostawayIds: [94],
        guestyAmenityNames: ['Wide clearance to shower and toilet'],
        tags: ['bathroom'],
        priority: 5,
        oldAmenityKeys: ['wideShowerTubClearance'],
        trueText: 'The main level bathroom has a 30 inch wide door',
    },
    {
        id: 'handheldShowerHead',
        name: 'Handheld shower head',
        hostawayIds: [95],
        tags: ['bathroom'],
        priority: 5,
    },
    {
        id: 'commonSpaceStepFreeAccess',
        name: 'Common space step free access',
        hostawayIds: [96],
        tags: ['property'],
        priority: 5,
    },
    { id: 'mobileHoist', name: 'Mobile hoist', hostawayIds: [98], tags: ['property'], priority: 5 },
    {
        id: 'poolHoist',
        name: 'Pool hoist',
        hostawayIds: [99],
        tags: ['property', 'hottubPool'],
        priority: 5,
    },
    {
        id: 'ceilingHoist',
        name: 'Ceiling hoist',
        hostawayIds: [100],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'hotWater',
        name: 'Hot water',
        hostawayIds: [101],
        airbnbAmenityNames: ['Hot water'],
        guestyAmenityNames: ['Hot water'],
        ownerrezAmenityNames: ['Hot Water'],
        hostfullyAmenityNames: ['HAS_HOT_WATER'],
        tags: ['property'],
        priority: 1,
        oldAmenityKeys: ['hotWater'],
    },
    {
        id: 'privateEntrance',
        name: 'Private entrance',
        airbnbAmenityNames: ['Private entrance'],
        hostawayIds: [102],
        guestyAmenityNames: ['Private entrance'],
        hostfullyAmenityNames: ['HAS_PRIVATE_ENTRANCE'],
        ownerrezAmenityNames: ['Private Entrance'],
        tags: ['access'],
        priority: 2,
        oldAmenityKeys: ['privateEntrance'],
        autoPopulate: true,
        falseText: 'This property does not feature a private entrance, you need to access the public lobby and take the stairs to floor 2 and find apartment 204',
        trueText: 'The private entrance to the property is right on silver street at the top of the hill',
    },
    {
        id: 'extraPillowsAndBlankets',
        name: 'Extra pillows and blankets',
        hostawayIds: [104],
        airbnbAmenityNames: ['Extra pillows and blankets'],
        guestyAmenityNames: ['Extra pillows and blankets'],
        hostfullyAmenityNames: ['HAS_EXTRA_PILLOWS_AND_BLANKETS'],
        ownerrezAmenityNames: ['Extra Pillows & Blankets'],
        tags: ['bedroom'],
        priority: 1,
        oldAmenityKeys: ['extraPillowsAndBlankets'],
        autoPopulate: true,
        falseText: 'We do not provide extra pillows and blankets, each bed has 2 pillows, a fitted sheet and comforter',
        trueText: 'All the beds have linens on them however the extra blankets are located in the closet at the top of the stairs',
    },
    {
        id: 'cookingBasics',
        name: 'Cooking basics',
        guestyAmenityNames: ['Cooking Basics', 'Kitchen'],
        hostfullyAmenityNames: ['HAS_COOKING_BASICS'],
        hostawayIds: [106],
        ownerrezAmenityNames: ['Spices/Pantry Items'],
        tags: ['kitchenDining'],
        priority: 2,
        trueText: 'We provide cooking basics which include spatulas, pans, silverware, cups, plates and bowls',
    },
    {
        id: 'electricVehicleCharger',
        name: 'Electric vehicle charger',
        airbnbAmenityNames: ['ev charger'],
        hostawayIds: [107],
        guestyAmenityNames: ['EV charger'],
        hostfullyAmenityNames: ['HAS_EV_CAR_CHARGER'],
        tags: ['parking', 'cars'],
        priority: 1,
        oldAmenityKeys: ['evCharger'],
        autoPopulate: true,
        falseText: 'This home does not have an EV charger, however there is a Tesla Supercharger station 2 miles away.',
        trueText: 'This home does feature an EV charger, it is located on the right side of the home. ',
    },
    {
        id: 'beachEssentials',
        name: 'Beach essentials',
        airbnbAmenityNames: ['Beach essentials'],
        hostawayIds: [108],
        guestyAmenityNames: ['Beach essentials'],
        hostfullyAmenityNames: ['HAS_BEACH_ESSENTIALS'],
        ownerrezAmenityNames: ['Beach Essentials'],
        tags: ['entertainment', 'outdoor'],
        priority: 3,
        oldAmenityKeys: ['beachEssentials'],
    },
    {
        id: 'roomDarkeningShades',
        name: 'Room darkening shades',
        hostawayIds: [109],
        airbnbAmenityNames: ['Room darkening shades'],
        guestyAmenityNames: ['Room-darkening shades'],
        hostfullyAmenityNames: ['HAS_ROOM_DARKENING_SHADES'],
        ownerrezAmenityNames: ['Room Darkening Shades'],
        tags: ['property'],
        priority: 3,
        oldAmenityKeys: ['roomDarkeningShades'],
    },
    {
        id: 'gameRoom',
        name: 'Game room',
        hostawayIds: [110],
        guestyAmenityNames: ['Game room'],
        ownerrezAmenityNames: ['Game Room'],
        tags: ['entertainment', 'property'],
        priority: 5,
    },
    {
        id: 'garage',
        name: 'Garage',
        hostawayIds: [111],
        guestyAmenityNames: ['Garage'],
        ownerrezAmenityNames: ['Garage'],
        tags: ['parking', 'cars'],
        priority: 2,
        autoPopulate: true,
        oldAmenityKeys: ['garage'],
        falseText: 'We don\'t have a garage at this property however you can park 3-4 cars on the right side of the home',
        trueText: 'The garage has room for car or small SUV, it\'s the red garage just across the street from the house. Park in spot #4',
    },
    { id: 'telephone', name: 'Telephone', hostawayIds: [112], ownerrezAmenityNames: ['Telephone'], tags: ['productivity'], priority: 5 },
    {
        id: 'woodStove',
        name: 'Wood stove',
        hostawayIds: [113],
        ownerrezAmenityNames: ['Wood Stove'],
        tags: ['property'],
        priority: 3,
        oldAmenityKeys: ['woodStove'],
    },
    {
        id: 'fitnessCenter',
        name: 'Fitness center',
        hostawayIds: [114],
        ownerrezAmenityNames: ['Fitness Center'],
        tags: ['sports'],
        priority: 5,
    },
    {
        id: 'laundromat',
        name: 'Laundromat',
        airbnbAmenityNames: ['Laundry'],
        hostawayIds: [115],
        hostfullyAmenityNames: ['HAS_LAUNDROMAT_NEARBY'],
        guestyAmenityNames: ['Laundromat nearby'],
        tags: ['laundry'],
        priority: 5,
    },
    {
        id: 'sauna',
        name: 'Sauna',
        airbnbAmenityNames: ['sauna'],
        hostawayIds: [116],
        tags: ['sports'],
        guestyAmenityNames: ['Sauna'],
        ownerrezAmenityNames: ['Sauna'],
        priority: 2,
        oldAmenityKeys: ['sauna'],
    },
    { id: 'bay', name: 'Bay', hostawayIds: [117], ownerrezAmenityNames: ['Bay/Sound'], tags: ['location'], priority: 5 },
    {
        id: 'coinLaundry',
        name: 'Coin laundry',
        hostawayIds: [118],
        tags: ['laundry'],
        guestyAmenityNames: ['Coin Laundry'],
        priority: 3,
    },
    { id: 'dutyFree', name: 'Duty free', hostawayIds: [119], tags: ['misc'], priority: 5 },
    {
        id: 'healthSpa',
        name: 'Health beauty spa',
        hostawayIds: [120],
        tags: ['sports'],
        guestyAmenityNames: ['Spa'],
        priority: 5,
    },
    { id: 'marina', name: 'Marina', hostawayIds: [121], ownerrezAmenityNames: ['Marina'], tags: ['location'], priority: 5 },
    {
        id: 'themeParks',
        name: 'Theme parks',
        hostawayIds: [123],
        guestyAmenityNames: ['Theme Parks'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'waterParks',
        name: 'Water parks',
        hostawayIds: [124],
        guestyAmenityNames: ['Water parks'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'wineryTours',
        name: 'Winery tours',
        hostawayIds: [125],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    { id: 'zoo', name: 'Zoo', hostawayIds: [126], tags: ['localRecs', 'attractions'], priority: 5 },
    { id: 'toilet', name: 'Toilet', hostawayIds: [129], tags: ['bathroom'], priority: 5 },
    {
        id: 'carNecessary',
        name: 'Car necessary',
        hostawayIds: [130],
        tags: ['location', 'cars'],
        priority: 5,
    },
    {
        id: 'carNotNecessary',
        name: 'Car not necessary',
        hostawayIds: [131],
        tags: ['location', 'cars'],
        priority: 5,
    },
    {
        id: 'carRecommended',
        name: 'Car recommended',
        hostawayIds: [132],
        tags: ['location', 'cars'],
        priority: 5,
    },
    {
        id: 'babyBath',
        name: 'Baby bath',
        airbnbAmenityNames: ['Baby bath'],
        hostawayIds: [133],
        guestyAmenityNames: ['Baby bath'],
        hostfullyAmenityNames: ['HAS_BABY_BATH'],
        ownerrezAmenityNames: ['Baby Bath'],
        tags: ['bathroom'],
        priority: 2,
        oldAmenityKeys: ['babyBath'],
        autoPopulate: true,
        falseText: 'We don\'t have a baby bath at this property but you are welcome to bring your own.',
        trueText: 'The baby bath is typically left in the laundry room',
    },
    {
        id: 'babyMonitor',
        name: 'Baby monitor',
        hostawayIds: [134],
        guestyAmenityNames: ['Baby monitor'],
        ownerrezAmenityNames: ['Baby Monitor'],
        tags: ['bedroom'],
        priority: 5,
        oldAmenityKeys: ['babyMonitor'],
        trueText: 'We store the baby monitor in the linen closet upstairs',
    },
    {
        id: 'babysitterRecommendations',
        name: 'Babysitter recommendations',
        hostawayIds: [135],
        guestyAmenityNames: ['Babysitter recommendations'],
        hostfullyAmenityNames: ['HAS_BABYSITTER_RECOMMENDATIONS'],
        ownerrezAmenityNames: ['Babysitter Recommendations'],
        tags: ['services'],
        priority: 5,
        oldAmenityKeys: ['babysitterRecommendations'],
    },
    {
        id: 'changingTable',
        name: 'Changing table',
        airbnbAmenityNames: ['Changing table'],
        hostawayIds: [136],
        guestyAmenityNames: ['Changing table'],
        hostfullyAmenityNames: ['HAS_CHANGING_TABLE'],
        ownerrezAmenityNames: ['Changing Table'],
        tags: ['bedroom'],
        priority: 5,
        oldAmenityKeys: ['changingTable'],
        trueText: 'The changing table is located in the nursery upstairs nest to the primary bedroom',
    },
    {
        id: 'childrensDinnerware',
        name: 'Childrens dinnerware',
        airbnbAmenityNames: ['Children\'s dinnerware'],
        hostawayIds: [137],
        guestyAmenityNames: ['Children\'s dinnerware'],
        hostfullyAmenityNames: ['HAS_CHILDRENS_DINNERWARE'],
        ownerrezAmenityNames: ['Children\'s Dinnerware'],
        tags: ['kitchenDining'],
        priority: 3,
        oldAmenityKeys: ['childrensDinnerware'],
        trueText: 'The kids dishes and utensils are in the drawer next to the dishwasher',
    },
    {
        id: 'toys',
        name: 'Toys',
        hostawayIds: [139],
        guestyAmenityNames: ['Children\'s books and toys'],
        hostfullyAmenityNames: ['HAS_CHILDRENS_BOOKS_AND_TOYS'],
        ownerrezAmenityNames: ['Toys', 'Children\'s Books & Toys'],
        tags: ['entertainment'],
        priority: 3,
        oldAmenityKeys: ['childrensToys', 'toys'],
    },
    {
        id: 'books',
        name: 'Books',
        hostawayIds: [140],
        airbnbAmenityNames: ['Books'],
        ownerrezAmenityNames: ['Books', 'Children\'s Books & Toys'],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'dvdPlayer',
        name: 'Dvd',
        hostawayIds: [141],
        guestyAmenityNames: ['Dvd player'],
        hostfullyAmenityNames: ['HAS_CDDVD_PLAYER'],
        ownerrezAmenityNames: ['DVD Player'],
        tags: ['entertainment'],
        priority: 3,
        oldAmenityKeys: ['dvdPlayer'],
    },
    {
        id: 'foosball',
        name: 'Foosball',
        hostawayIds: [142],
        ownerrezAmenityNames: ['Foosball'],
        tags: ['entertainment'],
        guestyAmenityNames: ['Foosball table'],
        priority: 3,
    },
    {
        id: 'boardGames',
        name: 'Games',
        hostawayIds: [143, 349],
        hostfullyAmenityNames: ['HAS_BOARD_GAMES'],
        guestyAmenityNames: ['Board games'],
        ownerrezAmenityNames: ['Games/Board Games'],
        tags: ['entertainment'],
        priority: 3,
    },
    {
        id: 'musicLibrary',
        name: 'Music library',
        hostawayIds: [144],
        ownerrezAmenityNames: ['Music Library'],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'pingPong',
        name: 'Ping pong table',
        airbnbAmenityNames: ['Ping pong table'],
        hostawayIds: [145],
        guestyAmenityNames: ['Ping pong table'],
        hostfullyAmenityNames: ['HAS_PING_PONG_TABLE'],
        ownerrezAmenityNames: ['Ping Pong Table'],
        tags: ['entertainment'],
        priority: 3,
    },
    {
        id: 'poolTable',
        name: 'Pool table',
        hostawayIds: [146],
        guestyAmenityNames: ['Pool table'],
        hostfullyAmenityNames: ['HAS_POOL_TABLE'],
        tags: ['entertainment'],
        priority: 3,
        trueText: 'The pool table is located in the basement',
    },
    {
        id: 'videoGames',
        name: 'Video games',
        hostawayIds: [147],
        airbnbAmenityNames: ['Game console'],
        guestyAmenityNames: ['Game console'],
        hostfullyAmenityNames: ['HAS_GAME_CONSOLE'],
        tags: ['entertainment'],
        priority: 3,
        oldAmenityKeys: ['gameConsole'],
        trueText: 'We provide access to the Playstation and Xbox. We store them under the basement TV.',
    },
    {
        id: 'videoLibrary',
        name: 'Video library',
        hostawayIds: [148],
        ownerrezAmenityNames: ['Video Library'],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'diningArea',
        name: 'Dining area',
        hostawayIds: [149],
        tags: ['kitchenDining'],
        priority: 5,
        trueText: 'The dining area is located right next to the kitchen on the main level. It seats 8 people at the table and 4 at the counter',
    },
    {
        id: 'iceMaker',
        name: 'Ice maker',
        hostawayIds: [150],
        tags: ['kitchenDining'],
        guestyAmenityNames: ['Ice maker'],
        ownerrezAmenityNames: ['Ice Maker'],
        priority: 2,
        trueText: 'The ice maker is part of the LG fridge',
    },
    { id: 'spices', name: 'Spices', hostawayIds: [153], ownerrezAmenityNames: ['Spices/Pantry Items'], tags: ['kitchenDining'], priority: 5 },
    {
        id: 'antiquing',
        name: 'Antiquing',
        hostawayIds: [155],
        ownerrezAmenityNames: ['Antiquing'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'birdWatching',
        name: 'Bird watching',
        hostawayIds: [156],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'boating',
        name: 'Boating',
        hostawayIds: [157],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'ecoTourism',
        name: 'Eco tourism',
        hostawayIds: [158],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'gambling',
        name: 'Gambling',
        hostawayIds: [159],
        guestyAmenityNames: ['Casinos'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'horseRiding',
        name: 'Horseback riding',
        hostawayIds: [160],
        guestyAmenityNames: ['Horseback riding'],
        ownerrezAmenityNames: ['Horseback Riding'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'outletShopping',
        name: 'Outlet shopping',
        hostawayIds: [161],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'paddleBoating',
        name: 'Paddle boating',
        hostawayIds: [162],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'shopping',
        name: 'Shopping',
        hostawayIds: [163],
        guestyAmenityNames: ['Shopping'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'sledding',
        name: 'Sledding',
        hostawayIds: [164],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'waterSports',
        name: 'Water sports',
        hostawayIds: [165],
        guestyAmenityNames: ['Water sports'],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'whaleWatching',
        name: 'Whale watching',
        hostawayIds: [166],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'wildlife',
        name: 'Wildlife viewing',
        hostawayIds: [167],
        tags: ['localRecs', 'attractions'],
        priority: 5,
    },
    {
        id: 'beach',
        name: 'Beach',
        hostawayIds: [168],
        guestyAmenityNames: ['Beach'],
        hostfullyAmenityNames: ['HAS_BEACH'],
        tags: ['location'],
        priority: 2,
        oldAmenityKeys: ['beach'],
        trueText: 'The beach is a 5 minute walk from the house, be sure to bring your sandles!',
    },
    {
        id: 'beachFront',
        name: 'Beach front',
        hostawayIds: [169, 170],
        guestyAmenityNames: ['Beach Front', 'Beach View', 'Beach access'],
        hostfullyAmenityNames: ['HAS_BEACH_FRONT', 'HAS_BEACH_VIEW'],
        ownerrezAmenityNames: ['Shared Beach Access'],
        tags: ['location'],
        priority: 2,
        oldAmenityKeys: ['beachFront', 'beachView', 'beachfront'],
        trueText: 'This property is right on Silver Beach, make sure to bring your beach toys!',
    },
    {
        id: 'downtown',
        name: 'Downtown',
        hostawayIds: [171],
        guestyAmenityNames: ['Downtown'],
        hostfullyAmenityNames: ['IS_DOWNTOWN'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['downtown'],
    },
    {
        id: 'golfCourse',
        name: 'Property is on a golf course or has a golf course view',
        hostawayIds: [172, 173],
        guestyAmenityNames: ['Golf course front', 'Golf view', 'Golf - Optional'],
        hostfullyAmenityNames: ['HAS_GOLF_COURSE_VIEW', 'IS_GOLF_COURSE_FRONT'],
        ownerrezAmenityNames: ['Walkable to Golf'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['golfCourseNextTo', 'golfCourseView'],
    },
    {
        id: 'lake',
        name: 'Lake',
        hostawayIds: [174],
        guestyAmenityNames: ['Lake'],
        hostfullyAmenityNames: ['HAS_LAKE_ACCESS', 'HAS_LAKE'],
        ownerrezAmenityNames: ['Near Lake, River, or Water Body'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['lake'],
        trueText: 'Lake Winnisquam is a 5 minute ride from the house, you can park on Silver Street. It\'s amazing and has tons of boats',
    },
    {
        id: 'lakeFront',
        name: 'Lake front',
        airbnbAmenityNames: ['Lake Front', 'Lake access'],
        hostawayIds: [175, 176],
        guestyAmenityNames: ['Lake Front', 'Lake access'],
        hostfullyAmenityNames: ['HAS_LAKE_ACCESS', 'IS_LAKEFRONT'],
        ownerrezAmenityNames: ['Lake Access', 'Near Lake, River, or Water Body'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['lakeFront', 'lakeAccess'],
        trueText: 'This property is located right on the lake. We allow fishing, boating and water sports',
    },
    {
        id: 'mountain',
        name: 'Mountain',
        hostawayIds: [177, 178],
        guestyAmenityNames: ['Mountain', 'Mountain view'],
        hostfullyAmenityNames: ['HAS_MOUNTAIN', 'HAS_MOUNTAIN_VIEW'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['mountain', 'mountainView'],
        trueText: 'The property is located right on the mountain, perfect for hiking and sking.',
    },
    {
        id: 'nearOcean',
        name: 'Near Ocean or Sea + Views',
        hostawayIds: [179, 180, 181],
        guestyAmenityNames: ['Near Ocean', 'Ocean Front', 'Sea view'],
        hostfullyAmenityNames: ['HAS_SEA_VIEW', 'IS_NEAR_OCEAN'],
        ownerrezAmenityNames: ['Near Lake, River, or Water Body'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['nearOcean', 'oceanFront', 'seaView'],
        trueText: 'The ocean is about a 20 minute ride. We recommend going to Wells Beach for great sand and good food.',
    },
    {
        id: 'resort',
        name: 'Resort',
        hostawayIds: [182],
        guestyAmenityNames: ['Resort', 'Resort access'],
        hostfullyAmenityNames: ['IS_RESORT', 'HAS_RESORT_ACCESS'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['resort'],
        trueText: 'The resort is included as part of your stay, you will have access to all the resort amenities',
    },
    {
        id: 'river',
        name: 'River',
        hostawayIds: [183],
        guestyAmenityNames: ['River'],
        hostfullyAmenityNames: ['HAS_RIVER'],
        ownerrezAmenityNames: ['Near Lake, River, or Water Body'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['river'],
        trueText: 'The river runs right down the left side of the property.',
    },
    {
        id: 'rural',
        name: 'Rural',
        hostawayIds: [184],
        guestyAmenityNames: ['Rural'],
        hostfullyAmenityNames: ['IS_RURAL'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['rural'],
        trueText: 'This property is very rural and there are not any stores or street lights within 5 miles',
    },
    {
        id: 'skiInOut',
        name: 'Ski In / Ski Out',
        hostawayIds: [185, 186, 187],
        guestyAmenityNames: ['Ski In', 'Ski in/Ski out', 'Ski Out'],
        hostfullyAmenityNames: ['HAS_SKI_IN_SKI_OUT'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['skiInSkiOut'],
        trueText: 'This property is truly ski in and ski out, be sure to leave you gear in the heated garage behind the house',
    },
    {
        id: 'town',
        name: 'Town',
        hostawayIds: [188],
        guestyAmenityNames: ['Town'],
        hostfullyAmenityNames: ['HAS_TOWN'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['town'],
        trueText: 'This property is right in town which allows you to walk to multiple resraurants and shops',
    },
    {
        id: 'village',
        name: 'Village',
        hostawayIds: [189],
        guestyAmenityNames: ['Village'],
        hostfullyAmenityNames: ['HAS_VILLAGE'],
        tags: ['location'],
        priority: 3,
        oldAmenityKeys: ['village'],
        trueText: 'The village is right up the road',
    },
    {
        id: 'waterfront',
        name: 'Water View / Waterfront',
        airbnbAmenityNames: ['Water View', 'Waterfront'],
        hostawayIds: [190, 191],
        guestyAmenityNames: ['Water View', 'Waterfront'],
        hostfullyAmenityNames: [
            'HAS_WATER_FRONT',
            'HAS_OCEAN_FRONT',
            'HAS_WATER_VIEW',
            'HAS_LAKE_VIEW',
        ],
        ownerrezAmenityNames: ['Water View', 'Waterfront', 'Near Lake, River, or Water Body'],
        tags: ['location'],
        priority: 2,
        oldAmenityKeys: ['waterView', 'waterfront'],
        trueText: 'You are able to see the water from the deck, it\'s a 3 minute walk to get to it.',
    },
    {
        id: 'bicycles',
        name: 'Bicycle',
        airbnbAmenityNames: ['Bikes'],
        hostawayIds: [192],
        guestyAmenityNames: ['Bicycles available', 'Bikes'],
        hostfullyAmenityNames: ['HAS_BIKES_FOR_RENT'],
        ownerrezAmenityNames: ['Bicycles', 'Children\'s Bike'],
        tags: ['sports', 'outdoor'],
        priority: 2,
        oldAmenityKeys: ['bicyclesAvailable'],
    },
    {
        id: 'boat',
        name: 'Boat',
        hostawayIds: [193],
        ownerrezAmenityNames: ['Boat Available', 'Boat Slip'],
        tags: ['sports', 'outdoor'],
        guestyAmenityNames: ['Boat slip'],
        priority: 3,
    },
    { id: 'golf', name: 'Golf', hostawayIds: [194, 234], ownerrezAmenityNames: ['Walkable to Golf'], tags: ['sports', 'outdoor'], priority: 5 },
    {
        id: 'kayaking',
        name: 'Kayak canoe',
        airbnbAmenityNames: ['Kayak'],
        hostawayIds: [195, 240],
        guestyAmenityNames: ['Kayak'],
        hostfullyAmenityNames: ['HAS_KAYAK'],
        ownerrezAmenityNames: ['Kayak/Canoe'],
        tags: ['sports', 'outdoor'],
        priority: 3,
    },
    {
        id: 'snowSportsGear',
        name: 'Snow sports gear',
        hostawayIds: [196],
        ownerrezAmenityNames: ['Snow Sports Gear'],
        tags: ['sports', 'outdoor'],
        priority: 5,
    },
    {
        id: 'tennis',
        name: 'Tennis',
        hostawayIds: [197],
        hostfullyAmenityNames: ['HAS_TENNIS'],
        ownerrezAmenityNames: ['Tennis'],
        tags: ['sports', 'outdoor'],
        priority: 5,
    },
    { id: 'veranda', name: 'Veranda', hostawayIds: [198], ownerrezAmenityNames: ['Veranda'], tags: ['property'], priority: 5 },
    {
        id: 'waterSportsGear',
        name: 'Water sports gear',
        hostawayIds: [199],
        ownerrezAmenityNames: ['Water Sports Gear'],
        tags: ['sports', 'outdoor'],
        priority: 5,
    },
    {
        id: 'luggageDropoff',
        name: 'Luggage dropoff and Bags Dropoff',
        airbnbAmenityNames: ['Luggage dropoff'],
        hostawayIds: [201],
        guestyAmenityNames: ['Luggage dropoff allowed'],
        tags: ['access'],
        priority: 1,
        oldAmenityKeys: ['luggageDropoffAllowed'],
        autoPopulate: true,
        trueText: 'We do allow luggage drop off early, but be sure to place your belongings in the front foyer closet so it\'s not in the cleaners way',
        falseText: 'Unfortunately we do not allow luggage drop off early but you are welcome to use the Bounce Storage app or LuggageHero to store your luggage nearby',
    },
    {
        id: 'longTermStays',
        name: 'Long term stays allowed',
        airbnbAmenityNames: ['Long term stays'],
        hostawayIds: [202],
        guestyAmenityNames: ['Long term stays allowed'],
        tags: ['booking'],
        priority: 2,
        oldAmenityKeys: ['longTermStaysAllowed'],
    },
    {
        id: 'fireplaceGuards',
        name: 'Fireplace guards',
        airbnbAmenityNames: ['Fireplace guards'],
        hostawayIds: [206],
        guestyAmenityNames: ['Fireplace guards'],
        hostfullyAmenityNames: ['HAS_FIREPLACE_GUARDS'],
        ownerrezAmenityNames: ['Fireplace Guards'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'outletCovers',
        name: 'Outlet covers',
        hostawayIds: [207],
        guestyAmenityNames: ['Outlet covers'],
        hostfullyAmenityNames: ['HAS_OUTLET_COVERS'],
        ownerrezAmenityNames: ['Outlet Covers'],
        tags: ['property'],
        priority: 5,
        oldAmenityKeys: ['outletCovers', 'outletCover'],
    },
    {
        id: 'stairGates',
        name: 'Stair gates',
        hostawayIds: [208],
        guestyAmenityNames: ['Stair gates'],
        hostfullyAmenityNames: ['HAS_STAIR_GATES'],
        ownerrezAmenityNames: ['Baby Gate'],
        tags: ['property'],
        priority: 5,
        oldAmenityKeys: ['stairGates'],
    },
    {
        id: 'tableCornerGuards',
        name: 'Table corner guards',
        hostawayIds: [209],
        guestyAmenityNames: ['Table corner guards'],
        hostfullyAmenityNames: ['HAS_TABLE_CORNER_GUARDS'],
        ownerrezAmenityNames: ['Table Corner Guards'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'windowGuards',
        name: 'Window guards',
        airbnbAmenityNames: ['Window guards'],
        hostawayIds: [210],
        guestyAmenityNames: ['Window guards'],
        hostfullyAmenityNames: ['HAS_WINDOW_GUARDS'],
        ownerrezAmenityNames: ['Window Guards'],
        tags: ['property'],
        priority: 5,
        oldAmenityKeys: ['windowGuards'],
    },
    {
        id: 'deadbolt',
        name: 'Deadbolt lock',
        hostawayIds: [211],
        hostfullyAmenityNames: ['HAS_DEADBOLT_LOCK'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'emergencyExit',
        name: 'Emergency exit',
        hostawayIds: [212],
        guestyAmenityNames: ['Emergency exit'],
        hostfullyAmenityNames: ['HAS_EMERGENCY_EXIT'],
        tags: ['safety'],
        priority: 5,
        oldAmenityKeys: ['emergencyExit'],
    },
    {
        id: 'fireEmergencyContact',
        name: 'Fire emergency contact',
        hostawayIds: [213],
        tags: ['safety'],
        priority: 5,
    },
    {
        id: 'hospitalNearby',
        name: 'Hospital nearby',
        hostawayIds: [214],
        tags: ['safety'],
        priority: 5,
    },
    {
        id: 'medicalEmergencyContact',
        name: 'Medical emergency contact',
        hostawayIds: [215],
        tags: ['safety'],
        priority: 5,
    },
    {
        id: 'outdoorLighting',
        name: 'Outdoor lighting',
        hostawayIds: [216],
        hostfullyAmenityNames: ['HAS_OUTDOOR_LIGHTING'],
        ownerrezAmenityNames: ['Exterior Lighting'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'policeEmergencyContact',
        name: 'Police emergency contact',
        hostawayIds: [217],
        tags: ['safety'],
        priority: 5,
    },
    {
        id: 'carAvailable',
        name: 'Car available',
        hostawayIds: [221],
        ownerrezAmenityNames: ['Car Available'],
        tags: ['services'],
        priority: 5,
    },
    { id: 'chauffeur', name: 'Chauffeur', hostawayIds: [222], tags: ['services'], priority: 5 },
    { id: 'massage', name: 'Massage', hostawayIds: [223], tags: ['services'], priority: 5 },
    {
        id: 'basketballCourt',
        name: 'Basketball court',
        hostawayIds: [225],
        ownerrezAmenityNames: ['Basketball Court'],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'crossCountrySki',
        name: 'Cross country skiing',
        hostawayIds: [226],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'cycling',
        name: 'Cycling',
        hostawayIds: [227],
        guestyAmenityNames: ['Cycling'],
        tags: ['sports', 'location', 'outdoor'],
        priority: 3,
    },
    {
        id: 'deepseaFishing',
        name: 'Deepsea fishing',
        hostawayIds: [228],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'fishing',
        name: 'Fishing',
        hostawayIds: [229],
        guestyAmenityNames: ['Fishing'],
        ownerrezAmenityNames: ['Fishing'],
        tags: ['sports', 'location', 'outdoor'],
        priority: 3,
    },
    {
        id: 'fishingBay',
        name: 'Fishing bay',
        hostawayIds: [230],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'fishingFly',
        name: 'Fishing fly',
        hostawayIds: [231],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'fishingFreshwater',
        name: 'Fishing freshwater',
        hostawayIds: [232],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'fishingSurf',
        name: 'Fishing surf',
        hostawayIds: [233],
        ownerrezAmenityNames: ['Surf Fishing'],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'golfOptional',
        name: 'Golf optional',
        hostawayIds: [235],
        ownerrezAmenityNames: ['Walkable to Golf'],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'hiking',
        name: 'Hiking',
        hostawayIds: [236],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'hunting',
        name: 'Hunting',
        hostawayIds: [237],
        ownerrezAmenityNames: ['Hunting'],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'iceSkating',
        name: 'Ice skating',
        hostawayIds: [238],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'jetSki',
        name: 'Jet skiing',
        hostawayIds: [239],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'mountainBiking',
        name: 'Mountain biking',
        hostawayIds: [241],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'mountainClimbing',
        name: 'Mountain climbing',
        hostawayIds: [242],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'mountaineering',
        name: 'Mountaineering',
        hostawayIds: [243],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'parasailing',
        name: 'Parasailing',
        hostawayIds: [244],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'pierFishing',
        name: 'Pier fishing',
        hostawayIds: [245],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'rafting',
        name: 'Rafting',
        hostawayIds: [246],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'rockClimbing',
        name: 'Rock climbing',
        hostawayIds: [247],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'sailing',
        name: 'Sailing',
        hostawayIds: [248],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'scuba',
        name: 'Scuba or snorkeling',
        hostawayIds: [249],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'skiLiftPrivilegesOptional',
        name: 'Ski lift privileges optional',
        hostawayIds: [250],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'skiLiftPrivileges',
        name: 'Ski lift privileges',
        hostawayIds: [251],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'skiing',
        name: 'Skiing',
        hostawayIds: [252],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'waterSkiing',
        name: 'Water Skiing',
        hostawayIds: [253],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'snorkeling',
        name: 'Snorkeling',
        hostawayIds: [254],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'snorkelingDiving',
        name: 'Snorkeling diving',
        hostawayIds: [255],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'snowboarding',
        name: 'Snowboarding',
        hostawayIds: [256],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'snowmobiling',
        name: 'Snowmobiling',
        hostawayIds: [257],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'spelunking',
        name: 'Spelunking',
        hostawayIds: [258],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'surfing',
        name: 'Surfing',
        hostawayIds: [259],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'swimming',
        name: 'Swimming',
        hostawayIds: [260],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'tubingWater',
        name: 'Tubing water',
        hostawayIds: [261],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'whitewaterRafting',
        name: 'Whitewater rafting',
        hostawayIds: [262],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    {
        id: 'windSurfing',
        name: 'Wind surfing',
        hostawayIds: [263],
        tags: ['sports', 'location', 'outdoor'],
        priority: 5,
    },
    { id: 'historic', name: 'Historic', hostawayIds: [265], ownerrezAmenityNames: ['Historic'], tags: ['theme'], priority: 5 },
    { id: 'romantic', name: 'Romantic', hostawayIds: [266], ownerrezAmenityNames: ['Romantic'], tags: ['theme'], priority: 5 },
    {
        id: 'cleaning',
        name: 'Cleaning',
        hostawayIds: [203, 219, 220, 267, 268],
        guestyAmenityNames: [
            'Cleaning Disinfection',
            'Enhanced cleaning practices',
            'Cleaning before checkout',
        ],
        hostfullyAmenityNames: ['HAS_ENHANCED_CLEANING', 'HAS_CLEANING_WITH_DISINFECTANTS'],
        ownerrezAmenityNames: ['Cleaning Disinfection'],
        tags: ['property'],
        priority: 2,
        oldAmenityKeys: ['cleaningDisinfection', 'enhancedCleaningPractices', 'cleaningBeforeCheckout'],
        trueText: 'For example: We have cleaning supplies under the sink in the kitchen. If you\'d like us to come clean the house for you it will just be a the cleaning fee of $200',
    },
    {
        id: 'contactlessCheckInOut',
        name: 'Contactless Check-In/Out',
        hostawayIds: [272],
        hostfullyAmenityNames: ['HAS_CONTACTLESS_CHECKIN'],
        tags: ['access'],
        priority: 3,
        falseText: 'This property does not offer contactless check in, you need to pick up keys at our office.',
        trueText: 'We offer contactless check in and check out. Just use the keypad on the front door.',
    },
    { id: 'fencedPool', name: 'Fenced pool', hostawayIds: [273], ownerrezAmenityNames: ['Fenced Pool'], tags: ['hottubPool'], priority: 5 },
    {
        id: 'fencedYard',
        name: 'Fenced yard',
        hostawayIds: [274],
        hostfullyAmenityNames: ['HAS_FENCED_YARD'],
        ownerrezAmenityNames: ['Fenced Yard'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'cabinetLocks',
        name: 'Cabinet locks',
        hostawayIds: [275],
        hostfullyAmenityNames: ['HAS_CABINET_LOCKS'],
        ownerrezAmenityNames: ['Cabinet Locks'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'dishesUtensilsForChildren',
        name: 'Dishes & utensils for children',
        hostawayIds: [276],
        ownerrezAmenityNames: ['Children\'s Dinnerware'],
        tags: ['kitchenDining'],
        priority: 5,
        trueText: 'The kids dishes and utensils are in the drawer next to the dishwasher',
    },
    {
        id: 'booksForKids',
        name: 'Books for kids',
        hostawayIds: [277],
        ownerrezAmenityNames: ['Children\'s Books & Toys'],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'mealDelivery',
        name: 'Meal delivery',
        hostawayIds: [278],
        hostfullyAmenityNames: ['HAS_MEAL_DELIVERY'],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'childcare',
        name: 'Childcare',
        hostawayIds: [279],
        hostfullyAmenityNames: ['HAS_CHILDCARE'],
        ownerrezAmenityNames: ['Childcare Available'],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'office',
        name: 'Office',
        hostawayIds: [288],
        ownerrezAmenityNames: ['Office'],
        tags: ['property', 'productivity'],
        priority: 5,
    },
    {
        id: 'computerMonitor',
        name: 'Computer monitor',
        hostawayIds: [289],
        hostfullyAmenityNames: ['HAS_COMPUTER_MONITOR'],
        ownerrezAmenityNames: ['Computer Monitor'],
        tags: ['property', 'productivity'],
        priority: 5,
    },
    {
        id: 'printer',
        name: 'Printer',
        hostawayIds: [290],
        hostfullyAmenityNames: ['HAS_PRINTER'],
        ownerrezAmenityNames: ['Printer'],
        tags: ['property', 'productivity'],
        priority: 5,
    },
    {
        id: 'deskChair',
        name: 'Desk chair',
        hostawayIds: [291],
        hostfullyAmenityNames: ['HAS_DESK_CHAIR'],
        ownerrezAmenityNames: ['Desk Chair'],
        tags: ['property', 'productivity'],
        priority: 5,
    },
    {
        id: 'kitchenIsland',
        name: 'Kitchen island',
        hostawayIds: [292],
        hostfullyAmenityNames: ['HAS_KITCHEN_ISLAND'],
        ownerrezAmenityNames: ['Kitchen Island'],
        tags: ['kitchenDining'],
        priority: 5,
    },
    {
        id: 'outdoorPlayArea',
        name: 'Outdoor play area',
        hostawayIds: [293],
        hostfullyAmenityNames: ['HAS_OUTDOOR_PLAY_AREA'],
        ownerrezAmenityNames: ['Walkable to Park/Playground', 'Play Area', 'Playground', 'Climbing/Play Structure'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'diningTable',
        name: 'Dining table',
        airbnbAmenityNames: ['Dining table'],
        guestyAmenityNames: ['Dining table'],
        hostfullyAmenityNames: ['HAS_DINING_TABLE'],
        ownerrezAmenityNames: ['Dining Table'],
        hostawayIds: [294],
        tags: ['kitchenDining'],
        priority: 3,
        trueText: 'The dining table seats 8 people however there is an additional 4 seats at the counter in the kitchen',
    },
    {
        id: 'privateFitnessCenter',
        name: 'Private fitness center',
        hostawayIds: [295],
        tags: ['sports', 'property'],
        priority: 5,
    },
    {
        id: 'fitnessEquipment',
        name: 'Fitness equipment',
        hostawayIds: [296],
        ownerrezAmenityNames: ['Exercise Equipment'],
        tags: ['sports', 'property'],
        priority: 5,
    },
    {
        id: 'communalSauna',
        name: 'Communal sauna',
        hostawayIds: [297],
        ownerrezAmenityNames: ['Sauna'],
        tags: ['sports', 'property'],
        priority: 5,
    },
    {
        id: 'privateSauna',
        name: 'Private sauna',
        hostawayIds: [298],
        ownerrezAmenityNames: ['Sauna'],
        tags: ['sports', 'property'],
        priority: 5,
    },
    {
        id: 'theater',
        name: 'Theater',
        hostawayIds: [299],
        ownerrezAmenityNames: ['Home Theater'],
        tags: ['entertainment', 'property'],
        priority: 5,
    },
    {
        id: 'inPersonCheckIn',
        name: 'In person check-in',
        hostawayIds: [300],
        hostfullyAmenityNames: ['HAS_IN_PERSON_CHECKIN'],
        tags: ['access'],
        priority: 5,
        falseText: 'We do not provide in person check in, you can check in on your own with the code we send you 2 days prior to your arrival',
        trueText: 'A member of your team can meet you at the property if you desire',
    },
    {
        id: 'babysitter',
        name: 'Babysitter on request',
        hostawayIds: [301],
        ownerrezAmenityNames: ['Childcare Available'],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'kidsPlayArea',
        name: 'Children play area',
        hostawayIds: [302],
        ownerrezAmenityNames: ['Playroom', 'Walkable to Park/Playground', 'Play Area', 'Playground', 'Climbing/Play Structure'],
        tags: ['entertainment'],
        priority: 5,
    },
    { id: 'kidsBed', name: 'Toddler bed', hostawayIds: [303], tags: ['bedroom'], priority: 5 },
    {
        id: 'kidsAmenities',
        name: 'Kids amenities',
        hostawayIds: [304],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'cityView',
        name: 'City view',
        hostawayIds: [305],
        tags: ['location'],
        guestyAmenityNames: ['City View'],
        priority: 5,
    },
    {
        id: 'privateDock',
        name: 'Private dock',
        hostawayIds: [306],
        hostfullyAmenityNames: ['HAS_BOAT_SLIP'],
        ownerrezAmenityNames: ['Boat Slip'],
        tags: ['location'],
        priority: 5,
        trueText: 'The private dock has enough room for one boat and one jetski, let us know in advance if you plan to bring a boat.',
    },
    {
        id: 'outdoorDining',
        name: 'Outdoor dining',
        airbnbAmenityNames: ['Outdoor dining'],
        hostawayIds: [308],
        hostfullyAmenityNames: ['HAS_OUTDOOR_SEATING'],
        ownerrezAmenityNames: ['Outdoor Dining', 'Outdoor Seating'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'outdoorFirepit',
        name: 'Outdoor firepit',
        hostawayIds: [309],
        ownerrezAmenityNames: ['Fire Pit'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'outdoorFurniture',
        name: 'Outdoor furniture',
        airbnbAmenityNames: ['Outdoor furniture'],
        guestyAmenityNames: ['Outdoor seating (furniture)'],
        ownerrezAmenityNames: ['Outdoor Seating'],
        hostawayIds: [310],
        tags: ['property'],
        priority: 2,
    },
    {
        id: 'outdoorSunLoungers',
        name: 'Outdoor sun loungers',
        airbnbAmenityNames: ['sun loungers'],
        ownerrezAmenityNames: ['Sun Loungers'],
        hostawayIds: [311],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'skiRental',
        name: 'Ski rental',
        hostawayIds: [312],
        tags: ['services', 'sports'],
        priority: 5,
    },
    { id: 'skiStorage', name: 'Ski storage', hostawayIds: [313], tags: ['sports'], priority: 5 },
    {
        id: 'petsAllowedOnRequest',
        name: 'Pets allowed on request',
        hostawayIds: [315],
        tags: ['pets'],
        priority: 5,
    },
    {
        id: 'swimmingPool',
        name: 'Pool',
        airbnbAmenityNames: ['pool'],
        hostawayIds: [6, 50, 51, 204, 205, 316],
        guestyAmenityNames: [
            'Swimming pool',
            'Private pool',
            'Communal pool',
            'Indoor pool',
            'Outdoor pool',
        ],
        hostfullyAmenityNames: [
            'HAS_POOL',
            'HAS_POOL_ALL_YEAR',
            'HAS_POOL_SEASONAL',
            'HAS_COMMUNAL_POOL',
            'HAS_HEATED_POOL',
            'HAS_INDOOR_POOL',
            'HAS_INDOOR_POOL_ALL_YEAR',
            'HAS_INDOOR_POOL_SEASONAL',
            'HAS_FENCED_POOL',
        ],
        ownerrezAmenityNames: ['Communal Pool', 'Private Pool', 'Indoor Pool', 'Heated Pool', 'Children\'s Pool', 'Fenced Pool'],
        tags: ['hottubPool'],
        priority: 1,
        oldAmenityKeys: ['pool', 'indoorPool', 'outdoorPool', 'privatePool'],
        autoPopulate: true,
        trueText: 'The pool is located in the backyard. It\'s open from mid may until late September',
    },
    {
        id: 'safe',
        name: 'Safe',
        hostawayIds: [317],
        airbnbAmenityNames: ['Safe'],
        guestyAmenityNames: ['Safe'],
        hostfullyAmenityNames: ['HAS_SAFE_BOX', 'HAS_SAFE'],
        ownerrezAmenityNames: ['Safe'],
        tags: ['property'],
        priority: 2,
        oldAmenityKeys: ['safe'],
    },
    {
        id: 'securitySystem',
        name: 'Security system',
        hostawayIds: [318],
        ownerrezAmenityNames: ['Cameras/Surveillance'],
        tags: ['safety'],
        priority: 5,
    },
    {
        id: 'mealIncluded',
        name: 'Meal included',
        hostawayIds: [319],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'privateChef',
        name: 'Private chef',
        hostawayIds: [224, 320],
        ownerrezAmenityNames: ['Private Chef'],
        tags: ['services'],
        priority: 5,
    },
    { id: 'butler', name: 'Butler included', hostawayIds: [321], tags: ['services'], priority: 5 },
    {
        id: 'butlerOnRequest',
        name: 'Butler on request',
        hostawayIds: [322],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'housekeeping',
        name: 'Housekeeper included',
        hostawayIds: [323],
        tags: ['services'],
        priority: 5,
        oldAmenityKeys: ['freeHousekeeping'],
    },
    {
        id: 'housekeepingOnRequest',
        name: 'Housekeeper on request',
        hostawayIds: [324],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'groceriesIncluded',
        name: 'Grocery included',
        hostawayIds: [325],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'groceriesOnRequest',
        name: 'Grocery on request',
        hostawayIds: [326],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'siteStaffIncluded',
        name: 'Site staff included',
        hostawayIds: [327],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'siteStaffOnRequest',
        name: 'Site staff on request',
        hostawayIds: [328],
        tags: ['services'],
        priority: 5,
    },
    {
        id: 'valetParking',
        name: 'Valet parking included',
        hostawayIds: [329],
        tags: ['services', 'parking', 'cars'],
        priority: 5,
    },
    {
        id: 'valetParkingOnRequest',
        name: 'Valet parking on request',
        hostawayIds: [330],
        tags: ['services', 'parking', 'cars'],
        priority: 5,
    },
    {
        id: 'bartender',
        name: 'Bartender included',
        hostawayIds: [331],
        tags: ['services', 'entertainment'],
        priority: 5,
    },
    {
        id: 'bartenderOnRequest',
        name: 'Bartender on request',
        hostawayIds: [332],
        tags: ['services', 'entertainment'],
        priority: 5,
    },
    { id: 'laundry', name: 'Laundry', hostawayIds: [333], tags: ['laundry'], priority: 5 },
    {
        id: 'basketball',
        name: 'Basketball private court',
        hostawayIds: [334],
        hostfullyAmenityNames: ['HAS_BASKETBALL_COURT'],
        ownerrezAmenityNames: ['Basketball Court'],
        tags: ['sports'],
        priority: 5,
    },
    {
        id: 'firepit',
        name: 'Fire pit',
        airbnbAmenityNames: ['Fire pit'],
        guestyAmenityNames: ['Fire Pit'],
        hostfullyAmenityNames: ['HAS_FIRE_PIT'],
        ownerrezAmenityNames: ['Fire Pit'],
        hostawayIds: [335],
        tags: ['property'],
        priority: 2,
    },
    {
        id: 'cleaningProducts',
        name: 'Cleaning products',
        airbnbAmenityNames: ['Cleaning products'],
        hostawayIds: [337],
        hostfullyAmenityNames: ['HAS_CLEANING_PRODUCTS'],
        guestyAmenityNames: ['Cleaning products'],
        ownerrezAmenityNames: ['Cleaning Products'],
        tags: ['bathroom'],
        priority: 2,
        autoPopulate: true,
        falseText: 'We do not provide cleaning supplies so feel free to bring your own',
        trueText: 'All of cleaning products for the home are located under the kitchen sink.',
    },
    {
        id: 'bodySoap',
        name: 'Body soap',
        airbnbAmenityNames: ['Body soap'],
        hostawayIds: [338],
        hostfullyAmenityNames: ['HAS_BODY_SOAP'],
        guestyAmenityNames: ['Body soap'],
        ownerrezAmenityNames: ['Basic Soaps', 'Body Soap'],
        tags: ['bathroom'],
        priority: 2,
        falseText: 'We don\'t provide body soap so make sure to bring your own',
        trueText: 'The body soap along with shampoo & conditioner is typically left in the shower with extras stored under the sink',
    },
    {
        id: 'conditioner',
        name: 'Conditioner',
        airbnbAmenityNames: ['Conditioner'],
        guestyAmenityNames: ['Conditioner'],
        hostawayIds: [339],
        hostfullyAmenityNames: ['HAS_CONDITIONER'],
        ownerrezAmenityNames: ['Hair Conditioner'],
        tags: ['bathroom'],
        priority: 2,
        falseText: 'We don\'t provide conditioner so make sure to bring your own',
        trueText: 'The conditioner is typically left in the shower for you to use',
    },
    { id: 'rainShower', name: 'Rain Shower', hostawayIds: [340], ownerrezAmenityNames: ['Rain Shower'], tags: ['bathroom'], priority: 5 },
    {
        id: 'showerGel',
        name: 'Shower gel',
        airbnbAmenityNames: ['Shower gel'],
        guestyAmenityNames: ['Shower gel'],
        hostfullyAmenityNames: ['HAS_SHOWER_GEL'],
        ownerrezAmenityNames: ['Shower Gel'],
        hostawayIds: [341],
        tags: ['bathroom'],
        priority: 2,
        falseText: 'We don\'t provide shower gel so make sure to bring your own',
        trueText: 'The shower gel is typically left in the shower for you to use',
    },
    {
        id: 'clothingStorage',
        name: 'Clothing storage',
        airbnbAmenityNames: ['Clothing storage'],
        guestyAmenityNames: ['Clothing storage'],
        ownerrezAmenityNames: ['Clothing Storage (closet or wardrobe)'],
        hostawayIds: [342],
        tags: ['laundry'],
        priority: 5,
        trueText: 'Each bedroom has a dresser and we also have closets to store your clothes',
    },
    {
        id: 'dryingRack',
        name: 'Drying rack for clothing',
        airbnbAmenityNames: ['Drying rack'],
        hostfullyAmenityNames: ['HAS_CLOTHES_DRYING_RACK'],
        ownerrezAmenityNames: ['Clothes Drying Rack'],
        hostawayIds: [343],
        tags: ['laundry'],
        priority: 5,
    },
    {
        id: 'mosquitoNet',
        name: 'Mosquito net',
        airbnbAmenityNames: ['Mosquito net'],
        guestyAmenityNames: ['Mosquito net'],
        hostawayIds: [344],
        hostfullyAmenityNames: ['HAS_MOSQUITO_NET'],
        ownerrezAmenityNames: ['Mosquito Net'],
        tags: ['property'],
        priority: 5,
    },
    {
        id: 'exerciseEquipment',
        name: 'Exercise equipment',
        airbnbAmenityNames: ['Exercise equipment'],
        hostfullyAmenityNames: ['HAS_EXCERCISE_EQUIPMENT'],
        ownerrezAmenityNames: ['Exercise Equipment'],
        hostawayIds: [345],
        tags: ['sports'],
        priority: 5,
    },
    {
        id: 'piano',
        name: 'Piano',
        airbnbAmenityNames: ['Piano'],
        hostfullyAmenityNames: ['HAS_PIANO'],
        guestyAmenityNames: ['Piano'],
        ownerrezAmenityNames: ['Piano'],
        hostawayIds: [346],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'recordPlayer',
        name: 'Record player',
        airbnbAmenityNames: ['Record player'],
        ownerrezAmenityNames: ['Record Player'],
        hostawayIds: [347],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'babySafetyGates',
        name: 'Baby safety gates',
        hostawayIds: [348],
        ownerrezAmenityNames: ['Baby Gate'],
        tags: ['safety'],
        priority: 5,
    },
    {
        id: 'ceilingFan',
        name: 'Ceiling fan',
        hostawayIds: [350],
        ownerrezAmenityNames: ['Ceiling Fans'],
        tags: ['property'],
        guestyAmenityNames: ['Ceiling fan'],
        priority: 5,
    },
    {
        id: 'portableFans',
        name: 'Portable fans',
        airbnbAmenityNames: ['Portable fans'],
        guestyAmenityNames: ['Portable fans'],
        hostawayIds: [351],
        hostfullyAmenityNames: ['HAS_PORTABLE_FANS'],
        ownerrezAmenityNames: ['Portable Fans'],
        tags: ['property'],
        priority: 2,
    },
    {
        id: 'bakingSheet',
        name: 'Baking sheet',
        airbnbAmenityNames: ['Baking sheet'],
        guestyAmenityNames: ['Baking sheet'],
        ownerrezAmenityNames: ['Baking Sheet'],
        // hostfullyAmenityNames: ["HAS_BAKING_SHEET"],
        hostawayIds: [352],
        tags: ['kitchenDining'],
        priority: 3,
    },
    {
        id: 'bbqUtils',
        name: 'Barbecue utensils',
        airbnbAmenityNames: ['Barbecue utensils'],
        hostfullyAmenityNames: ['HAS_BARBECUE_UTENSILS'],
        ownerrezAmenityNames: ['Barbeque/Grill Utensils'],
        hostawayIds: [353],
        tags: ['kitchenDining'],
        priority: 2,
        trueText: 'The barbeque utensils are stored next to the oven. We have 2 spatulas and one set of metal tongs',
    },
    {
        id: 'breadMaker',
        name: 'Bread maker',
        airbnbAmenityNames: ['Bread maker'],
        hostfullyAmenityNames: ['HAS_BREAD_MAKER'],
        ownerrezAmenityNames: ['Bread Maker'],
        hostawayIds: [354],
        tags: ['kitchenDining'],
        priority: 5,
    },
    {
        id: 'blender',
        name: 'Blender',
        airbnbAmenityNames: ['Blender'],
        hostfullyAmenityNames: ['HAS_BLENDER'],
        guestyAmenityNames: ['Blender'],
        ownerrezAmenityNames: ['Blender'],
        hostawayIds: [355],
        tags: ['kitchenDining'],
        priority: 3,
    },
    {
        id: 'coffee',
        name: 'Coffee',
        airbnbAmenityNames: ['Coffee'],
        hostawayIds: [356],
        hostfullyAmenityNames: ['HAS_COFFEE'],
        ownerrezAmenityNames: ['Coffee'],
        tags: ['kitchenDining'],
        priority: 2,
        autoPopulate: true,
        trueText: 'We provide keurig pods and they are located in the cabinet above the keurig machine',
    },
    {
        id: 'freezer',
        name: 'Freezer',
        hostawayIds: [357],
        hostfullyAmenityNames: ['HAS_FREEZER'],
        guestyAmenityNames: ['Freezer'],
        ownerrezAmenityNames: ['Freezer'],
        tags: ['kitchenDining'],
        priority: 3,
        trueText: 'The freezer is in the bottom of the fridge in the kitchen',
    },
    {
        id: 'riceMaker',
        name: 'Rice maker',
        airbnbAmenityNames: ['Rice maker'],
        hostawayIds: [359],
        hostfullyAmenityNames: ['HAS_RICE_MAKER'],
        guestyAmenityNames: ['Rice maker'],
        ownerrezAmenityNames: ['Rice Maker'],
        tags: ['kitchenDining'],
        priority: 3,
        trueText: 'The rice maker is located in the kitchen cabinet next to the stove',
    },
    {
        id: 'trashCompactor',
        name: 'Trash Compactor',
        airbnbAmenityNames: ['Trash Compactor'],
        guestyAmenityNames: ['Trash compactor'],
        hostfullyAmenityNames: ['HAS_TRASH_COMPACTOR'],
        ownerrezAmenityNames: ['Trash Compacter'],
        hostawayIds: [360],
        tags: ['kitchenDining'],
        priority: 3,
        trueText: 'For example: The trash compactor is located under the bar. Please be sure to use the trash compactor bags in the cabinet to the left of it.',
    },
    {
        id: 'wineGlasses',
        name: 'Wine glasses',
        airbnbAmenityNames: ['Wine glasses'],
        guestyAmenityNames: ['Wine glasses'],
        hostfullyAmenityNames: ['HAS_WINE_GLASSES'],
        ownerrezAmenityNames: ['Wine Glasses'],
        hostawayIds: [361],
        tags: ['kitchenDining'],
        priority: 3,
        trueText: 'We have 12 winde glasses in the cabinet above the sink',
    },
    {
        id: 'hammock',
        name: 'Hammock',
        airbnbAmenityNames: ['Hammock'],
        hostawayIds: [362],
        hostfullyAmenityNames: ['HAS_HAMMOCK'],
        guestyAmenityNames: ['Hammock'],
        ownerrezAmenityNames: ['Hammock'],
        tags: ['entertainment'],
        priority: 5,
    },
    {
        id: 'outdoorKitchen',
        name: 'Outdoor kitchen',
        airbnbAmenityNames: ['Outdoor kitchen'],
        guestyAmenityNames: ['Outdoor kitchen'],
        hostawayIds: [363],
        hostfullyAmenityNames: ['HAS_OUTDOOR_KITCHEN'],
        ownerrezAmenityNames: ['Outdoor Kitchen'],
        tags: ['kitchenDining'],
        priority: 3,
    },
    {
        id: 'highTouchSurfacesDisinfected',
        name: 'High touch surfaces disinfected',
        guestyAmenityNames: ['High touch surfaces disinfected'],
        hostfullyAmenityNames: ['HAS_HIGH_TOUCH_SURFACES_CLEANING_WITH_DISINFECTANTS'],
        tags: ['safety'],
        priority: 5,
        oldAmenityKeys: ['highTouchSurfacesDisinfected'],
    },
    {
        id: 'family',
        name: 'Family friendly',
        hostawayIds: [264],
        guestyAmenityNames: ['Family/kid friendly'],
        ownerrezAmenityNames: ['Family'],
        tags: ['theme'],
        priority: 2,
        oldAmenityKeys: ['familyKidFriendly'],
    },
    {
        id: 'desk',
        name: 'Desk',
        guestyAmenityNames: ['Desk'],
        hostfullyAmenityNames: ['HAS_DESK'],
        ownerrezAmenityNames: ['Desk'],
        tags: ['productivity'],
        priority: 2,
        oldAmenityKeys: ['desk'],
        falseText: 'There is no desk at this property however most people sit at the bar with their laptop.',
        trueText: 'The standup desk is located in the bedroom with the king bed upstairs.',
    },
    {
        id: 'mirror',
        name: 'Mirror',
        tags: ['bathroom'],
        priority: 5,
        oldAmenityKeys: ['mirror'],
    },
    {
        id: 'skiArea',
        name: 'Ski Area',
        tags: ['location'],
        priority: 5,
        oldAmenityKeys: ['skiArea'],
        trueText: 'The Gunstock Ski Area is 5 minutes from the house by car',
    },
    {
        id: 'pulloutBeds',
        name: 'Pullout Beds',
        tags: ['bedroom', 'property'],
        priority: 1,
        autoPopulate: true,
        falseText: 'For example: We do not have any pull out beds or futons at the property',
        trueText: 'For example: We do have one pullout bed under the couch on the lower level. The sheets for the pulllout bed are in the container next to the coffee table',
    },
    {
        id: 'bedAndBreakfast',
        name: 'Bed and Breakfast',
        tags: ['services'],
        priority: 5,
        oldAmenityKeys: ['bedAndBreakfast'],
    },
    {
        id: 'paperTowels',
        name: 'Paper Towels',
        guestyAmenityNames: ['Paper Towels'],
        ownerrezAmenityNames: ['Paper Towels'],
        tags: ['kitchenDining'],
        priority: 1,
        autoPopulate: true,
        alwaysSet: true,
        trueText: 'We typically leave out 2 rolls of paper towels, if you need more than that feel free to bring some extra.',
    },
    {
        id: 'toiletPaper',
        name: 'Toilet Paper',
        guestyAmenityNames: ['Toilet Paper'],
        ownerrezAmenityNames: ['Toilet Paper'],
        tags: ['bathroom'],
        priority: 1,
        autoPopulate: true,
        alwaysSet: true,
    },
]

// TODO Use the code generated by this gemini prompt to complete this: https://gemini.google.com/app/bd63c7ae70d58ddb
