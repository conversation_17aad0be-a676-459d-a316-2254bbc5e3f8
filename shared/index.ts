import { useWorkspaceStore } from '@/stores/workspace'

export { CalryPmsIntegrationDefinitionKeys } from './enums'
export { listingAmenities } from './listingAmenities'
export { listingFaqs, listingTags } from './listingFaqsTags'
export { listingTypes, listingTypesS, messageTypes, messageTypesS, taskPriorityTypes, taskStatusTypes, taskTypes, workflowTemplates, workflowTypes } from './webapp'

export function getChatCode() {
  const workspaceStore = useWorkspaceStore()
  return `<script type="text/javascript" async>
    var readyStateCheckInterval = setInterval(function() {
      if (document.readyState === "complete") {
        clearInterval(readyStateCheckInterval);
        loadYada();
      }
    }, 50);

    function loadYada () {
      const s = document.createElement("script");
      s.src = "https://cdn.yada.ai/widget.umd.js"
      document.body.appendChild(s);
      const w = document.createElement("yada-widget");
      w.setAttribute("id", "${workspaceStore.workspace.id}");
      document.body.appendChild(w);
    }
  </script>`
}

export function generateUniqueID(length = 32) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let id = ''
  for (let i = 0; i < length; i++) {
    id += characters[Math.floor(Math.random() * characters.length)]
  }
  return id
}
