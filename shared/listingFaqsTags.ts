export const listingTags = [
  {
    id: 'general',
    name: 'General',
    description: 'General Information about the Property',
    priority: 10,
  },
  {
    id: 'access',
    name: 'Access',
    description: 'Getting into the Property',
    priority: 1,
  },
  {
    id: 'bathroom',
    name: 'Bathroom',
    description: '',
    priority: 2,
  },
  {
    id: 'bedroom',
    name: 'Bedroom',
    description: '',
    priority: 2,
  },
  {
    id: 'entertainment',
    name: 'Entertainment',
    description: '',
    priority: 4,
  },
  {
    id: 'kitchenDining',
    name: 'Kitchen & Dining',
    description: '',
    priority: 3,
  },
  {
    id: 'parking',
    name: 'Parking',
    description: '',
    priority: 1,
  },
  {
    id: 'hottubPool',
    name: 'Pool & Hot Tub',
    description: '',
    priority: 3,
  },
  {
    id: 'safety',
    name: 'Safety',
    description: '',
    priority: 5,
  },
  {
    id: 'laundry',
    name: '<PERSON>nd<PERSON>',
    description: '',
    priority: 2,
  },
  {
    id: 'internet',
    name: 'Internet & WiFI',
    description: '',
    priority: 1,
  },
  {
    id: 'productivity',
    name: 'Productivity',
    description: '',
    priority: 4,
  },
  {
    id: 'pets',
    name: 'Pets',
    description: '',
    priority: 3,
  },
  {
    id: 'cleanliness',
    name: 'Cleaning',
    description: '',
    priority: 5,
  },
  {
    id: 'services',
    name: 'Services',
    description: '',
    priority: 6,
  },
  {
    id: 'location',
    name: 'Location',
    description: '',
    priority: 3,
  },
  {
    id: 'attractions',
    name: 'Attractions',
    description: '',
    priority: 5,
  },
  {
    id: 'outdoor',
    name: 'Outdoor',
    description: '',
    priority: 5,
  },
  {
    id: 'sports',
    name: 'Sports',
    description: '',
    priority: 5,
  },
  {
    id: 'hvac',
    name: 'Heating and Cooling',
    description: '',
    priority: 2,
  },
  {
    id: 'theme',
    name: 'Listing Theme',
    description: '',
    priority: 5,
  },
  {
    id: 'property',
    name: 'Property Details',
    description: '',
    priority: 3,
  },
  {
    id: 'misc',
    name: 'Misc',
    description: '',
    priority: 5,
  },
  {
    id: 'localRecs',
    name: 'Local Recommendations',
    description: '',
    priority: 2,
  },
  {
    id: 'booking',
    name: 'Booking Information',
    description: '',
    priority: 5,
  },
  {
    id: 'transport',
    name: 'Transportation',
    description: '',
    priority: 2,
  },
]

export const listingFaqs = [
  {
    id: 'gettingThere',
    name: 'Getting to the Property',
    tags: ['access'],
    priority: 1,
    autoPopulate: true,
    sampleText:
      'We recommend you use a GPS to get to the property. Check in instructions will be sent to you 48 hours prior to check in',
  },
  {
    id: 'checkingIn',
    name: 'Checking In & Getting Inside',
    tags: ['access'],
    priority: 1,
    autoPopulate: true,
    sampleText:
      'Getting into the property is really easy. All you need to do is first go through the gate, then find the green lockbox. The lockbox key code is in your message history in an automated message you will have received from us 24 hours prior to check in.',
  },
  {
    id: 'checkingOut',
    name: 'Checking Out and Leaving',
    tags: ['access'],
    priority: 1,
    autoPopulate: true,
    sampleText:
      'While checking out, make sure to take the trash out, and if you have time, strip the bed and put the sheets in the washing machine. You don\'t need to return the keys to the lockbox, just leave them on the kitchen table before you leave. The door will auto lock behind you.',
  },
  {
    id: 'earlyCheckIn',
    name: 'Early Check-in',
    tags: ['access'],
    priority: 1,
    autoPopulate: true,
    sampleText:
      'Typically this property is booked every single day. We do our best to accommodate these requests. We will follow up with you as your stay gets a bit closer and we have the opportunity to check with the cleaning team. Check in is 4PM for all guests and we will let you know if we are able to accommodate your request.',
  },
  {
    id: 'lateCheckOut',
    name: 'Late Check-out',
    tags: ['access'],
    priority: 1,
    autoPopulate: true,
    sampleText:
      'Typically this property is booked every single day. We do our best to accommodate late checkout requests. Check out is 10am for all guests and we will let you know if we are able to accommodate your request once we chat with the cleaners.',
  },
  {
    id: 'airport',
    name: 'Airports & Airport Directions',
    tags: ['transport', 'localRecs'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'train',
    name: 'Trains & Subways',
    tags: ['transport', 'localRecs'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'bus',
    name: 'Buses',
    tags: ['transport', 'localRecs'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'cab',
    name: 'Cabs & Ride Sharing',
    tags: ['transport', 'localRecs'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'restaurants',
    name: 'Restaurants',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'bars',
    name: 'Bars',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'nightlife',
    name: 'Nightlife',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'cafes',
    name: 'Cafes & Breakfast',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'groceryStores',
    name: 'Grocery Stores',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'attractions',
    name: 'Local Attractions',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'museums',
    name: 'Museums',
    tags: ['localRecs', 'entertainment'],
    priority: 2,
    autoPopulate: true,
  },
  {
    id: 'outdoor',
    name: 'Outdoor Activities',
    tags: ['localRecs', 'entertainment'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'trash',
    name: 'Trash & Garbage Disposal',
    tags: ['property'],
    priority: 1,
    autoPopulate: true,
  },
  {
    id: 'houseRules',
    name: 'House Rules',
    tags: ['property'],
    priority: 1,
    autoPopulate: true,
    sampleText:
      'Guests and visitors should comply with the parking regulations and requirements and must show consideration to other vehicles in the neighborhood. \nNo shoes inside the home. \nNo smoking inside the Airbnb. Cigarette butts must be disposed of in the ashtray provided in the backyard of the property. \nNo parties or events. \nNo unregistered guests or visitors allowed. The maximum property capacity is 4 people. \nQuiet time after 11 p.m. \nNo eating or drinking in bedrooms. Please enjoy your meal in the dining room. \nDispose of the garbage in the trash bin. \nPlease use the swimming pool only between 10 a.m. and 3 p.m. \nKeep a close eye on your kids while being on the balcony. \nNo pets allowed. \nMake sure to turn off the lights, air conditioning, and any electronics when you leave the house. Don\'t forget to close the windows and lock the door. \nReport any damages and/or breakages in a reasonably timely manner. Damages exceeding the security deposit amount must be paid by guests and will be reported to Airbnb. \nWash the dishes, and throw out the garbage before checkout.',
  },
]
