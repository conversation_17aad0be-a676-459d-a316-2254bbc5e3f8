export const listingTypes = ['apartment', 'house', 'room']
export const listingTypesS = {
  apartment: 'Apartment',
  house: 'House',
  room: 'Room',
}

export const messageTypes = ['email', 'sms', 'whatsapp']
export const messageTypesS = {
  email: 'Email',
  sms: 'SMS',
  whatsapp: 'WhatsApp',
}

export const taskPriorityTypes = [
  {
    label: 'Low',
    value: 'low',
    variant: 'success',
  },
  {
    label: 'Medium',
    value: 'medium',
    variant: 'warning',
  },
  {
    label: 'High',
    value: 'high',
    variant: 'error',
  },
]

export const taskStatusTypes = [
  {
    label: 'To Do',
    value: 'toDo',
    color: 'error',
  },
  {
    label: 'In Progress',
    value: 'inProgress',
    color: 'warning',
  },
  {
    label: 'Done',
    value: 'done',
    color: 'success',
  },
  {
    label: 'Deferred',
    value: 'deferred',
    color: 'neutral',
  },
  {
    label: 'Dead End',
    value: 'deadEnd',
    color: 'neutral',
  },
]

export const taskTypes = [
  {
    value: 'housekeeping',
    label: 'Housekeeping',
    description:
            'Tasks related to cleaning guest rooms, replenishing amenities, and attending to special guest requests to ensure a comfortable and pleasant stay.',
  },
  {
    value: 'maintenance',
    label: 'Maintenance',
    description:
            'Tasks involving the upkeep and repair of facilities, equipment, and infrastructure to ensure a safe and functional environment for guests and staff.',
  },
  {
    value: 'frontDeskOperations',
    label: 'Front Desk Operations',
    description:
            'Tasks encompassing guest check-in and check-out, reservations, concierge services, and handling inquiries to provide a seamless and positive guest experience from arrival to departure.',
  },
  {
    value: 'guestServices',
    label: 'Guest Services',
    description:
            'Tasks focused on enhancing guests\' stays through organizing activities, providing transportation, arranging events, and offering personalized services to create memorable experiences.',
  },
  {
    value: 'foodAndBeverage',
    label: 'Food and Beverage',
    description:
            'Tasks related to managing culinary operations, including restaurant, room service, and banquet services, ensuring high standards of food quality, presentation, and guest satisfaction.',
  },
  {
    value: 'marketingAndSales',
    label: 'Marketing and Sales',
    description:
            'Tasks encompassing sales, conversions, promotions, advertising, online presence management, group bookings, and corporate client relationships to attract new guests and retain existing ones.',
  },
  {
    value: 'humanResources',
    label: 'Human Resources',
    description:
            'Tasks involving recruiting, training, and managing hotel staff, as well as administering payroll, benefits, and employee programs.',
  },
  {
    value: 'financeAndAccounting',
    label: 'Finance and Accounting',
    description:
            'Tasks related to financial management, including budgeting, reporting, accounts payable/receivable, and guest billing.',
  },
  {
    value: 'security',
    label: 'Security',
    description:
            'Tasks focused on safeguarding guests, staff, and rental / hotel property through patrols, surveillance monitoring, and emergency response planning to maintain a secure environment.',
  },
  {
    value: 'administrative',
    label: 'Administrative',
    description:
            'Tasks involving record-keeping, data management, legal compliance, and license maintenance to ensure the smooth and efficient operation in accordance with regulations.',
  },
  {
    value: 'misc',
    label: 'Miscellaneous',
    description: 'Tasks that do not fall under any specific category.',
  },
  {
    value: 'automated',
    label: 'AI Generated Task',
    description: 'Tasks that are generated by the AI based on guest message or review',
    hidden: true,
  },
  {
    value: 'onboarding',
    label: 'Onboarding',
    description: 'Onboarding and account setup tasks',
    hidden: true,
  },
]

export const workflowTemplates = {
  inquiryAbandonment: {
    type: 'Winback',
    name: 'Inquiry Winback',
  },

  earlyCheckin: {
    type: 'Guest Request',
    name: 'Early Check In Request',
  },
  customOfferPreCheckin: {
    type: 'Upsell',
    name: 'Custom Pre Stay Upsell',
  },
  earlyCheckinOffer: {
    type: 'Upsell',
    name: 'Early Check In Offer',
  },
  lateCheckout: {
    type: 'Guest Request',
    name: 'Late Check Out Request',
  },
  lateCheckoutOffer: {
    type: 'Upsell',
    name: 'Late Check Out Offer',
  },
  postStayExtraNightsOffer: {
    type: 'Upsell',
    name: 'Post Stay Gap Night Offer',
  },
  preStayExtraNightsOffer: {
    type: 'Upsell',
    name: 'Pre Stay Gap Night Offer',
  },
  recurringTask: {
    type: 'Tasks',
    name: 'Recurring Task',
  },
}

export const workflowTypes = {
  contactSubmissionFollowUpDefaultWeb: {
    type: 'Marketing',
    name: 'Contact Submission Follow Up - Web',
  },
  contactSubmissionFollowUpDefaultGuidebook: {
    type: 'Marketing',
    name: 'Contact Submission Follow Up - Guidebook',
  },
  oneOffEmailListSend: {
    type: 'Marketing',
    name: 'One Off Email List Send',
  },
}

export const countryCodes = [
  { value: 'AF', label: 'Afghanistan' },
  {
    value: 'AX',
    label: '\u00C5land Islands',
  },
  { value: 'AL', label: 'Albania' },
  { value: 'DZ', label: 'Algeria' },
  {
    value: 'AS',
    label: 'American Samoa',
  },
  { value: 'AD', label: 'Andorra' },
  { value: 'AO', label: 'Angola' },
  {
    value: 'AI',
    label: 'Anguilla',
  },
  { value: 'AQ', label: 'Antarctica' },
  { value: 'AG', label: 'Antigua and Barbuda' },
  {
    value: 'AR',
    label: 'Argentina',
  },
  { value: 'AM', label: 'Armenia' },
  { value: 'AW', label: 'Aruba' },
  {
    value: 'AU',
    label: 'Australia',
  },
  { value: 'AT', label: 'Austria' },
  { value: 'AZ', label: 'Azerbaijan' },
  {
    value: 'BS',
    label: 'Bahamas',
  },
  { value: 'BH', label: 'Bahrain' },
  { value: 'BD', label: 'Bangladesh' },
  {
    value: 'BB',
    label: 'Barbados',
  },
  { value: 'BY', label: 'Belarus' },
  { value: 'BE', label: 'Belgium' },
  {
    value: 'BZ',
    label: 'Belize',
  },
  { value: 'BJ', label: 'Benin' },
  { value: 'BM', label: 'Bermuda' },
  {
    value: 'BT',
    label: 'Bhutan',
  },
  { value: 'BO', label: 'Bolivia, Plurinational State of' },
  {
    value: 'BQ',
    label: 'Bonaire, Sint Eustatius and Saba',
  },
  { value: 'BA', label: 'Bosnia and Herzegovina' },
  { value: 'BW', label: 'Botswana' },
  {
    value: 'BV',
    label: 'Bouvet Island',
  },
  { value: 'BR', label: 'Brazil' },
  { value: 'IO', label: 'British Indian Ocean Territory' },
  {
    value: 'BN',
    label: 'Brunei Darussalam',
  },
  { value: 'BG', label: 'Bulgaria' },
  { value: 'BF', label: 'Burkina Faso' },
  {
    value: 'BI',
    label: 'Burundi',
  },
  { value: 'KH', label: 'Cambodia' },
  { value: 'CM', label: 'Cameroon' },
  {
    value: 'CA',
    label: 'Canada',
  },
  { value: 'CV', label: 'Cape Verde' },
  { value: 'KY', label: 'Cayman Islands' },
  {
    value: 'CF',
    label: 'Central African Republic',
  },
  { value: 'TD', label: 'Chad' },
  { value: 'CL', label: 'Chile' },
  {
    value: 'CN',
    label: 'China',
  },
  { value: 'CX', label: 'Christmas Island' },
  { value: 'CC', label: 'Cocos (Keeling) Islands' },
  {
    value: 'CO',
    label: 'Colombia',
  },
  { value: 'KM', label: 'Comoros' },
  { value: 'CG', label: 'Congo' },
  {
    value: 'CD',
    label: 'Congo, the Democratic Republic of the',
  },
  { value: 'CK', label: 'Cook Islands' },
  { value: 'CR', label: 'Costa Rica' },
  {
    value: 'CI',
    label: 'C\u00F4te d\'Ivoire',
  },
  { value: 'HR', label: 'Croatia' },
  { value: 'CU', label: 'Cuba' },
  {
    value: 'CW',
    label: 'Cura\u00E7ao',
  },
  { value: 'CY', label: 'Cyprus' },
  { value: 'CZ', label: 'Czech Republic' },
  {
    value: 'DK',
    label: 'Denmark',
  },
  { value: 'DJ', label: 'Djibouti' },
  { value: 'DM', label: 'Dominica' },
  {
    value: 'DO',
    label: 'Dominican Republic',
  },
  { value: 'EC', label: 'Ecuador' },
  { value: 'EG', label: 'Egypt' },
  {
    value: 'SV',
    label: 'El Salvador',
  },
  { value: 'GQ', label: 'Equatorial Guinea' },
  { value: 'ER', label: 'Eritrea' },
  {
    value: 'EE',
    label: 'Estonia',
  },
  { value: 'ET', label: 'Ethiopia' },
  { value: 'FK', label: 'Falkland Islands (Malvinas)' },
  {
    value: 'FO',
    label: 'Faroe Islands',
  },
  { value: 'FJ', label: 'Fiji' },
  { value: 'FI', label: 'Finland' },
  {
    value: 'FR',
    label: 'France',
  },
  { value: 'GF', label: 'French Guiana' },
  { value: 'PF', label: 'French Polynesia' },
  {
    value: 'TF',
    label: 'French Southern Territories',
  },
  { value: 'GA', label: 'Gabon' },
  { value: 'GM', label: 'Gambia' },
  {
    value: 'GE',
    label: 'Georgia',
  },
  { value: 'DE', label: 'Germany' },
  { value: 'GH', label: 'Ghana' },
  {
    value: 'GI',
    label: 'Gibraltar',
  },
  { value: 'GR', label: 'Greece' },
  { value: 'GL', label: 'Greenland' },
  {
    value: 'GD',
    label: 'Grenada',
  },
  { value: 'GP', label: 'Guadeloupe' },
  { value: 'GU', label: 'Guam' },
  {
    value: 'GT',
    label: 'Guatemala',
  },
  { value: 'GG', label: 'Guernsey' },
  { value: 'GN', label: 'Guinea' },
  {
    value: 'GW',
    label: 'Guinea-Bissau',
  },
  { value: 'GY', label: 'Guyana' },
  { value: 'HT', label: 'Haiti' },
  {
    value: 'HM',
    label: 'Heard Island and McDonald Islands',
  },
  { value: 'VA', label: 'Holy See (Vatican City State)' },
  { value: 'HN', label: 'Honduras' },
  {
    value: 'HK',
    label: 'Hong Kong',
  },
  { value: 'HU', label: 'Hungary' },
  { value: 'IS', label: 'Iceland' },
  {
    value: 'IN',
    label: 'India',
  },
  { value: 'ID', label: 'Indonesia' },
  { value: 'IR', label: 'Iran, Islamic Republic of' },
  {
    value: 'IQ',
    label: 'Iraq',
  },
  { value: 'IE', label: 'Ireland' },
  { value: 'IM', label: 'Isle of Man' },
  {
    value: 'IL',
    label: 'Israel',
  },
  { value: 'IT', label: 'Italy' },
  { value: 'JM', label: 'Jamaica' },
  {
    value: 'JP',
    label: 'Japan',
  },
  { value: 'JE', label: 'Jersey' },
  { value: 'JO', label: 'Jordan' },
  {
    value: 'KZ',
    label: 'Kazakhstan',
  },
  { value: 'KE', label: 'Kenya' },
  { value: 'KI', label: 'Kiribati' },
  {
    value: 'KP',
    label: 'Korea, Democratic People\'s Republic of',
  },
  { value: 'KR', label: 'Korea, Republic of' },
  { value: 'KW', label: 'Kuwait' },
  {
    value: 'KG',
    label: 'Kyrgyzstan',
  },
  { value: 'LA', label: 'Lao People\'s Democratic Republic' },
  { value: 'LV', label: 'Latvia' },
  {
    value: 'LB',
    label: 'Lebanon',
  },
  { value: 'LS', label: 'Lesotho' },
  { value: 'LR', label: 'Liberia' },
  {
    value: 'LY',
    label: 'Libya',
  },
  { value: 'LI', label: 'Liechtenstein' },
  { value: 'LT', label: 'Lithuania' },
  {
    value: 'LU',
    label: 'Luxembourg',
  },
  { value: 'MO', label: 'Macao' },
  {
    value: 'MK',
    label: 'Macedonia, the Former Yugoslav Republic of',
  },
  { value: 'MG', label: 'Madagascar' },
  { value: 'MW', label: 'Malawi' },
  {
    value: 'MY',
    label: 'Malaysia',
  },
  { value: 'MV', label: 'Maldives' },
  { value: 'ML', label: 'Mali' },
  {
    value: 'MT',
    label: 'Malta',
  },
  { value: 'MH', label: 'Marshall Islands' },
  { value: 'MQ', label: 'Martinique' },
  {
    value: 'MR',
    label: 'Mauritania',
  },
  { value: 'MU', label: 'Mauritius' },
  { value: 'YT', label: 'Mayotte' },
  {
    value: 'MX',
    label: 'Mexico',
  },
  { value: 'FM', label: 'Micronesia, Federated States of' },
  {
    value: 'MD',
    label: 'Moldova, Republic of',
  },
  { value: 'MC', label: 'Monaco' },
  { value: 'MN', label: 'Mongolia' },
  {
    value: 'ME',
    label: 'Montenegro',
  },
  { value: 'MS', label: 'Montserrat' },
  { value: 'MA', label: 'Morocco' },
  {
    value: 'MZ',
    label: 'Mozambique',
  },
  { value: 'MM', label: 'Myanmar' },
  { value: 'NA', label: 'Namibia' },
  {
    value: 'NR',
    label: 'Nauru',
  },
  { value: 'NP', label: 'Nepal' },
  { value: 'NL', label: 'Netherlands' },
  {
    value: 'NC',
    label: 'New Caledonia',
  },
  { value: 'NZ', label: 'New Zealand' },
  { value: 'NI', label: 'Nicaragua' },
  {
    value: 'NE',
    label: 'Niger',
  },
  { value: 'NG', label: 'Nigeria' },
  { value: 'NU', label: 'Niue' },
  {
    value: 'NF',
    label: 'Norfolk Island',
  },
  { value: 'MP', label: 'Northern Mariana Islands' },
  { value: 'NO', label: 'Norway' },
  {
    value: 'OM',
    label: 'Oman',
  },
  { value: 'PK', label: 'Pakistan' },
  { value: 'PW', label: 'Palau' },
  {
    value: 'PS',
    label: 'Palestine, State of',
  },
  { value: 'PA', label: 'Panama' },
  { value: 'PG', label: 'Papua New Guinea' },
  {
    value: 'PY',
    label: 'Paraguay',
  },
  { value: 'PE', label: 'Peru' },
  { value: 'PH', label: 'Philippines' },
  {
    value: 'PN',
    label: 'Pitcairn',
  },
  { value: 'PL', label: 'Poland' },
  { value: 'PT', label: 'Portugal' },
  {
    value: 'PR',
    label: 'Puerto Rico',
  },
  { value: 'QA', label: 'Qatar' },
  { value: 'RE', label: 'R\u00E9union' },
  {
    value: 'RO',
    label: 'Romania',
  },
  { value: 'RU', label: 'Russian Federation' },
  { value: 'RW', label: 'Rwanda' },
  {
    value: 'BL',
    label: 'Saint Barth\u00E9lemy',
  },
  { value: 'SH', label: 'Saint Helena, Ascension and Tristan da Cunha' },
  {
    value: 'KN',
    label: 'Saint Kitts and Nevis',
  },
  { value: 'LC', label: 'Saint Lucia' },
  { value: 'MF', label: 'Saint Martin (French part)' },
  {
    value: 'PM',
    label: 'Saint Pierre and Miquelon',
  },
  { value: 'VC', label: 'Saint Vincent and the Grenadines' },
  { value: 'WS', label: 'Samoa' },
  {
    value: 'SM',
    label: 'San Marino',
  },
  { value: 'ST', label: 'Sao Tome and Principe' },
  { value: 'SA', label: 'Saudi Arabia' },
  {
    value: 'SN',
    label: 'Senegal',
  },
  { value: 'RS', label: 'Serbia' },
  { value: 'SC', label: 'Seychelles' },
  {
    value: 'SL',
    label: 'Sierra Leone',
  },
  { value: 'SG', label: 'Singapore' },
  { value: 'SX', label: 'Sint Maarten (Dutch part)' },
  {
    value: 'SK',
    label: 'Slovakia',
  },
  { value: 'SI', label: 'Slovenia' },
  { value: 'SB', label: 'Solomon Islands' },
  {
    value: 'SO',
    label: 'Somalia',
  },
  { value: 'ZA', label: 'South Africa' },
  {
    value: 'GS',
    label: 'South Georgia and the South Sandwich Islands',
  },
  { value: 'SS', label: 'South Sudan' },
  { value: 'ES', label: 'Spain' },
  {
    value: 'LK',
    label: 'Sri Lanka',
  },
  { value: 'SD', label: 'Sudan' },
  { value: 'SR', label: 'Suriname' },
  {
    value: 'SJ',
    label: 'Svalbard and Jan Mayen',
  },
  { value: 'SZ', label: 'Swaziland' },
  { value: 'SE', label: 'Sweden' },
  {
    value: 'CH',
    label: 'Switzerland',
  },
  { value: 'SY', label: 'Syrian Arab Republic' },
  {
    value: 'TW',
    label: 'Taiwan, Province of China',
  },
  { value: 'TJ', label: 'Tajikistan' },
  { value: 'TZ', label: 'Tanzania, United Republic of' },
  {
    value: 'TH',
    label: 'Thailand',
  },
  { value: 'TL', label: 'Timor-Leste' },
  { value: 'TG', label: 'Togo' },
  {
    value: 'TK',
    label: 'Tokelau',
  },
  { value: 'TO', label: 'Tonga' },
  { value: 'TT', label: 'Trinidad and Tobago' },
  {
    value: 'TN',
    label: 'Tunisia',
  },
  { value: 'TR', label: 'Turkey' },
  { value: 'TM', label: 'Turkmenistan' },
  {
    value: 'TC',
    label: 'Turks and Caicos Islands',
  },
  { value: 'TV', label: 'Tuvalu' },
  { value: 'UG', label: 'Uganda' },
  {
    value: 'UA',
    label: 'Ukraine',
  },
  { value: 'AE', label: 'United Arab Emirates' },
  { value: 'GB', label: 'United Kingdom' },
  {
    value: 'US',
    label: 'United States',
  },
  { value: 'UM', label: 'United States Minor Outlying Islands' },
  {
    value: 'UY',
    label: 'Uruguay',
  },
  { value: 'UZ', label: 'Uzbekistan' },
  { value: 'VU', label: 'Vanuatu' },
  {
    value: 'VE',
    label: 'Venezuela, Bolivarian Republic of',
  },
  { value: 'VN', label: 'Viet Nam' },
  { value: 'VG', label: 'Virgin Islands, British' },
  {
    value: 'VI',
    label: 'Virgin Islands, U.S.',
  },
  { value: 'WF', label: 'Wallis and Futuna' },
  { value: 'EH', label: 'Western Sahara' },
  {
    value: 'YE',
    label: 'Yemen',
  },
  { value: 'ZM', label: 'Zambia' },
  { value: 'ZW', label: 'Zimbabwe' },
]

export const timezones = [
  {
    offset: 'GMT-11:00',
    name: 'Pacific/Midway',
  },
  {
    offset: 'GMT-10:00',
    name: 'America/Adak',
  },
  {
    offset: 'GMT-09:00',
    name: 'America/Anchorage',
  },
  {
    offset: 'GMT-09:00',
    name: 'Pacific/Gambier',
  },
  {
    offset: 'GMT-08:00',
    name: 'America/Dawson_Creek',
  },
  {
    offset: 'GMT-08:00',
    name: 'America/Ensenada',
  },
  {
    offset: 'GMT-08:00',
    name: 'America/Los_Angeles',
  },
  {
    offset: 'GMT-07:00',
    name: 'America/Chihuahua',
  },
  {
    offset: 'GMT-07:00',
    name: 'America/Denver',
  },
  {
    offset: 'GMT-06:00',
    name: 'America/Belize',
  },
  {
    offset: 'GMT-06:00',
    name: 'America/Cancun',
  },
  {
    offset: 'GMT-06:00',
    name: 'America/Chicago',
  },
  {
    offset: 'GMT-06:00',
    name: 'Chile/EasterIsland',
  },
  {
    offset: 'GMT-05:00',
    name: 'America/Bogota',
  },
  {
    offset: 'GMT-05:00',
    name: 'America/Havana',
  },
  {
    offset: 'GMT-05:00',
    name: 'America/New_York',
  },
  {
    offset: 'GMT-04:30',
    name: 'America/Caracas',
  },
  {
    offset: 'GMT-04:00',
    name: 'America/Campo_Grande',
  },
  {
    offset: 'GMT-04:00',
    name: 'America/Glace_Bay',
  },
  {
    offset: 'GMT-04:00',
    name: 'America/Goose_Bay',
  },
  {
    offset: 'GMT-04:00',
    name: 'America/Santiago',
  },
  {
    offset: 'GMT-04:00',
    name: 'America/La_Paz',
  },
  {
    offset: 'GMT-03:00',
    name: 'America/Argentina/Buenos_Aires',
  },
  {
    offset: 'GMT-03:00',
    name: 'America/Montevideo',
  },
  {
    offset: 'GMT-03:00',
    name: 'America/Araguaina',
  },
  {
    offset: 'GMT-03:00',
    name: 'America/Godthab',
  },
  {
    offset: 'GMT-03:00',
    name: 'America/Miquelon',
  },
  {
    offset: 'GMT-03:00',
    name: 'America/Sao_Paulo',
  },
  {
    offset: 'GMT-03:30',
    name: 'America/St_Johns',
  },
  {
    offset: 'GMT-02:00',
    name: 'America/Noronha',
  },
  {
    offset: 'GMT-01:00',
    name: 'Atlantic/Cape_Verde',
  },
  {
    offset: 'GMT',
    name: 'Europe/Belfast',
  },
  {
    offset: 'GMT',
    name: 'Africa/Abidjan',
  },
  {
    offset: 'GMT',
    name: 'Europe/Dublin',
  },
  {
    offset: 'GMT',
    name: 'Europe/Lisbon',
  },
  {
    offset: 'GMT',
    name: 'Europe/London',
  },
  {
    offset: 'GMT+01:00',
    name: 'Africa/Algiers',
  },
  {
    offset: 'GMT+01:00',
    name: 'Africa/Windhoek',
  },
  {
    offset: 'GMT+01:00',
    name: 'Atlantic/Azores',
  },
  {
    offset: 'GMT+01:00',
    name: 'Atlantic/Stanley',
  },
  {
    offset: 'GMT+01:00',
    name: 'Europe/Amsterdam',
  },
  {
    offset: 'GMT+01:00',
    name: 'Europe/Belgrade',
  },
  {
    offset: 'GMT+01:00',
    name: 'Europe/Brussels',
  },
  {
    offset: 'GMT+02:00',
    name: 'Africa/Cairo',
  },
  {
    offset: 'GMT+02:00',
    name: 'Africa/Blantyre',
  },
  {
    offset: 'GMT+02:00',
    name: 'Asia/Beirut',
  },
  {
    offset: 'GMT+02:00',
    name: 'Asia/Damascus',
  },
  {
    offset: 'GMT+02:00',
    name: 'Asia/Gaza',
  },
  {
    offset: 'GMT+02:00',
    name: 'Asia/Jerusalem',
  },
  {
    offset: 'GMT+03:00',
    name: 'Africa/Addis_Ababa',
  },
  {
    offset: 'GMT+03:00',
    name: 'Asia/Riyadh89',
  },
  {
    offset: 'GMT+03:00',
    name: 'Europe/Minsk',
  },
  {
    offset: 'GMT+03:30',
    name: 'Asia/Tehran',
  },
  {
    offset: 'GMT+04:00',
    name: 'Asia/Dubai',
  },
  {
    offset: 'GMT+04:00',
    name: 'Asia/Yerevan',
  },
  {
    offset: 'GMT+04:00',
    name: 'Europe/Moscow',
  },
  {
    offset: 'GMT+04:30',
    name: 'Asia/Kabul',
  },
  {
    offset: 'GMT+05:00',
    name: 'Asia/Tashkent',
  },
  {
    offset: 'GMT+05:30',
    name: 'Asia/Kolkata',
  },
  {
    offset: 'GMT+05:45',
    name: 'Asia/Katmandu',
  },
  {
    offset: 'GMT+06:00',
    name: 'Asia/Dhaka',
  },
  {
    offset: 'GMT+06:00',
    name: 'Asia/Yekaterinburg',
  },
  {
    offset: 'GMT+06:30',
    name: 'Asia/Rangoon',
  },
  {
    offset: 'GMT+07:00',
    name: 'Asia/Bangkok',
  },
  {
    offset: 'GMT+07:00',
    name: 'Asia/Novosibirsk',
  },
  {
    offset: 'GMT+08:00',
    name: 'Asia/Hong_Kong',
  },
  {
    offset: 'GMT+08:00',
    name: 'Asia/Krasnoyarsk',
  },
  {
    offset: 'GMT+08:00',
    name: 'Australia/Perth',
  },
  {
    offset: 'GMT+08:45',
    name: 'Australia/Eucla',
  },
  {
    offset: 'GMT+09:00',
    name: 'Asia/Irkutsk',
  },
  {
    offset: 'GMT+09:00',
    name: 'Asia/Seoul',
  },
  {
    offset: 'GMT+09:00',
    name: 'Asia/Tokyo',
  },
  {
    offset: 'GMT+09:30',
    name: 'Australia/Adelaide',
  },
  {
    offset: 'GMT+09:30',
    name: 'Australia/Darwin',
  },
  {
    offset: 'GMT+09:30',
    name: 'Pacific/Marquesas',
  },
  {
    offset: 'GMT+10:00',
    name: 'Australia/Brisbane',
  },
  {
    offset: 'GMT+10:00',
    name: 'Australia/Hobart',
  },
  {
    offset: 'GMT+10:00',
    name: 'Asia/Yakutsk',
  },
  {
    offset: 'GMT+10:30',
    name: 'Australia/Lord_Howe',
  },
  {
    offset: 'GMT+11:00',
    name: 'Asia/Vladivostok',
  },
  {
    offset: 'GMT+11:30',
    name: 'Pacific/Norfolk',
  },
  {
    offset: 'GMT+12:00',
    name: 'Asia/Anadyr',
  },
  {
    offset: 'GMT+12:00',
    name: 'Asia/Magadan',
  },
  {
    offset: 'GMT+12:00',
    name: 'Pacific/Auckland',
  },
  {
    offset: 'GMT+12:45',
    name: 'Pacific/Chatham',
  },
  {
    offset: 'GMT+13:00',
    name: 'Pacific/Tongatapu',
  },
  {
    offset: 'GMT+14:00',
    name: 'Pacific/Kiritimati',
  },
]

export const places = {
  countries: [
    'Afghanistan',
    'Albania',
    'Algeria',
    'Andorra',
    'Angola',
    'Anguilla',
    'Antigua & Barbuda',
    'Argentina',
    'Armenia',
    'Aruba',
    'Australia',
    'Austria',
    'Azerbaijan',
    'Bahamas',
    'Bahrain',
    'Bangladesh',
    'Barbados',
    'Belarus',
    'Belgium',
    'Belize',
    'Benin',
    'Bermuda',
    'Bhutan',
    'Bolivia',
    'Bosnia & Herzegovina',
    'Botswana',
    'Brazil',
    'British Virgin Islands',
    'Brunei',
    'Bulgaria',
    'Burkina Faso',
    'Burundi',
    'Cambodia',
    'Cameroon',
    'Canada',
    'Cape Verde',
    'Cayman Islands',
    'Central Arfrican Republic',
    'Chad',
    'Chile',
    'China',
    'Colombia',
    'Congo',
    'Cook Islands',
    'Costa Rica',
    'Cote D Ivoire',
    'Croatia',
    'Cuba',
    'Curacao',
    'Cyprus',
    'Czech Republic',
    'Denmark',
    'Djibouti',
    'Dominica',
    'Dominican Republic',
    'Ecuador',
    'Egypt',
    'El Salvador',
    'Equatorial Guinea',
    'Eritrea',
    'Estonia',
    'Ethiopia',
    'Falkland Islands',
    'Faroe Islands',
    'Fiji',
    'Finland',
    'France',
    'French Polynesia',
    'French West Indies',
    'Gabon',
    'Gambia',
    'Georgia',
    'Germany',
    'Ghana',
    'Gibraltar',
    'Greece',
    'Greenland',
    'Grenada',
    'Guam',
    'Guatemala',
    'Guernsey',
    'Guinea',
    'Guinea Bissau',
    'Guyana',
    'Haiti',
    'Honduras',
    'Hong Kong',
    'Hungary',
    'Iceland',
    'India',
    'Indonesia',
    'Iran',
    'Iraq',
    'Ireland',
    'Isle of Man',
    'Israel',
    'Italy',
    'Jamaica',
    'Japan',
    'Jersey',
    'Jordan',
    'Kazakhstan',
    'Kenya',
    'Kiribati',
    'Kosovo',
    'Kuwait',
    'Kyrgyzstan',
    'Laos',
    'Latvia',
    'Lebanon',
    'Lesotho',
    'Liberia',
    'Libya',
    'Liechtenstein',
    'Lithuania',
    'Luxembourg',
    'Macau',
    'Macedonia',
    'Madagascar',
    'Malawi',
    'Malaysia',
    'Maldives',
    'Mali',
    'Malta',
    'Marshall Islands',
    'Mauritania',
    'Mauritius',
    'Mexico',
    'Micronesia',
    'Moldova',
    'Monaco',
    'Mongolia',
    'Montenegro',
    'Montserrat',
    'Morocco',
    'Mozambique',
    'Myanmar',
    'Namibia',
    'Nauro',
    'Nepal',
    'Netherlands',
    'Netherlands Antilles',
    'New Caledonia',
    'New Zealand',
    'Nicaragua',
    'Niger',
    'Nigeria',
    'North Korea',
    'Norway',
    'Oman',
    'Pakistan',
    'Palau',
    'Palestine',
    'Panama',
    'Papua New Guinea',
    'Paraguay',
    'Peru',
    'Philippines',
    'Poland',
    'Portugal',
    'Puerto Rico',
    'Qatar',
    'Reunion',
    'Romania',
    'Russia',
    'Rwanda',
    'Saint Pierre & Miquelon',
    'Samoa',
    'San Marino',
    'Sao Tome and Principe',
    'Saudi Arabia',
    'Senegal',
    'Serbia',
    'Seychelles',
    'Sierra Leone',
    'Singapore',
    'Slovakia',
    'Slovenia',
    'Solomon Islands',
    'Somalia',
    'South Africa',
    'South Korea',
    'South Sudan',
    'Spain',
    'Sri Lanka',
    'St Kitts & Nevis',
    'St Lucia',
    'St Vincent',
    'Sudan',
    'Suriname',
    'Swaziland',
    'Sweden',
    'Switzerland',
    'Syria',
    'Taiwan',
    'Tajikistan',
    'Tanzania',
    'Thailand',
    'Timor L\'Este',
    'Togo',
    'Tonga',
    'Trinidad & Tobago',
    'Tunisia',
    'Turkey',
    'Turkmenistan',
    'Turks & Caicos',
    'Tuvalu',
    'Uganda',
    'Ukraine',
    'United Arab Emirates',
    'United Kingdom',
    'United States of America',
    'Uruguay',
    'Uzbekistan',
    'Vanuatu',
    'Vatican City',
    'Venezuela',
    'Vietnam',
    'Virgin Islands (US)',
    'Yemen',
    'Zambia',
    'Zimbabwe',
  ],
}
