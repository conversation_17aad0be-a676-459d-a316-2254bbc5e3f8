import { getFirestore } from 'firebase/firestore'
import { getFunctions } from 'firebase/functions'
import { getStorage } from 'firebase/storage'

export default defineNuxtPlugin(() => {
  const app = useFirebaseApp()
  const db = getFirestore(app)
  const storage = getStorage(app)
  const functions = getFunctions(app)

  return {
    provide: {
      db,
      storage,
      functions,
    },
  }
})
