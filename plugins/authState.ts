import { onAuthStateChanged } from 'firebase/auth'
import { useInboxStore } from '~/stores/inbox'
import { useListingStore } from '~/stores/listing'
import { useWorkspaceStore } from '~/stores/workspace'

export default defineNuxtPlugin((nuxtApp) => {
  if (process.server) {
    return
  }

  const auth = useFirebaseAuth()

  if (!auth) {
    console.error('Firebase Auth is not initialized')
    return
  }

  onAuthStateChanged(auth, async (user) => {
    if (user) {
      try {
        const _hsq = window._hsq = window._hsq || []
        _hsq.push(['identify', {
          email: user.email,
        }])
      }
      catch (e) {
        console.error('unable to identify user (onAuthStateChanged)')
      }
      const workspaceStore = useWorkspaceStore()
      const listingStore = useListingStore()
      const inboxStore = useInboxStore()
      await workspaceStore.init()
      listingStore.init()
      workspaceStore.loadListingLeaderboard()
      workspaceStore.loadCounts()
      workspaceStore.loadViews()
      workspaceStore.loadSmartSpaces()
      workspaceStore.loadContacts()
      workspaceStore.loadConversions()
      workspaceStore.loadChats()
      inboxStore.initChats()
      inboxStore.initConversations()
    }
    else {
      console.log('user is not logged in')
    }
  })
})
