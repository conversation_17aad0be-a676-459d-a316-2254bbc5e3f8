# Yada AI - Property Management AI Assistant

![Yada AI](https://cdn.yada.ai/logo.png)

Yada AI is an intelligent platform designed to help property managers and hosts automate and enhance their guest communication and property management workflows. Built with Nuxt 3 and Firebase, Yada AI provides a comprehensive suite of tools for managing listings, automating responses, creating smart guidebooks, and more.

## Features

- **AI-Powered Responses**: Automatically respond to guest inquiries with context-aware AI
- **Smart Guidebooks**: Create and share digital guidebooks for your properties
- **Listing Management**: Organize and manage all your properties in one place
- **Workflow Automation**: Set up automated workflows for common guest interactions
- **Integration with PMS**: Connect with popular property management systems
- **Chat Widget**: Embed a customizable chat widget on your website
- **Content Generation**: Auto-generate property descriptions and responses

## Tech Stack

- **Frontend**: Nuxt 3, Vue 3, Nuxt UI, Nuxt UI Pro
- **Backend**: Firebase (Authentication, Firestore, Storage, Functions)
- **AI Integration**: Custom AI models for property management
- **Analytics**: PostHog for user behavior tracking
- **Payment Processing**: Chargebee for subscription management

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Firebase
FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_DATABASE_URL=
FIREBASE_PROJECT_ID=
FIREBASE_STORAGE_BUCKET=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_APP_ID=
FIREBASE_MEASUREMENT_ID=

# PostHog
NUXT_PUBLIC_POSTHOG_KEY=
NUXT_PUBLIC_POSTHOG_HOST=
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## Project Structure

- `/components`: Reusable Vue components
- `/composables`: Vue composables for shared logic
- `/layouts`: Page layouts including the dashboard layout
- `/pages`: Application routes and views
- `/server`: Server-side API endpoints
- `/stores`: Pinia stores for state management
- `/shared`: Shared utilities and constants
- `/ragas-evaluation-service`: Python service for AI response evaluation deployed on Fly.io

## RAGAS Evaluation Service (Fly.io)

The project includes a separate Python service (`/ragas-evaluation-service`) that handles AI response evaluation using the RAGAS framework. This service is deployed on Fly.io for scalable, cost-effective hosting.

### Service Overview

The RAGAS evaluation service is a FastAPI application that:
- Evaluates AI responses for relevancy and accuracy
- Uses OpenAI's models for evaluation through the RAGAS framework
- Provides RESTful endpoints for the main Nuxt application
- Auto-scales based on traffic (0 machines when idle to save costs)

### Deployment Architecture

- **Platform**: Fly.io with auto-scaling machines
- **Runtime**: Python 3.11 with FastAPI
- **Configuration**: Dockerized deployment with health checks
- **Scaling**: Auto-start/stop machines (min 0 running for cost optimization)
- **Resources**: 1 CPU, 1GB RAM (configurable in `fly.toml`)

### Key Files

- `ragas-evaluation-service/main.py`: FastAPI application with evaluation endpoints
- `ragas-evaluation-service/fly.toml`: Fly.io deployment configuration
- `ragas-evaluation-service/Dockerfile`: Container build configuration
- `ragas-evaluation-service/DEPLOYMENT.md`: Detailed deployment instructions

### Deployment Commands

```bash
# Navigate to the service directory
cd ragas-evaluation-service

# Install Fly CLI and authenticate
flyctl auth login

# Deploy the service
flyctl deploy

# Set required environment variables
flyctl secrets set OPENAI_API_KEY=your_openai_api_key

# Monitor the service
flyctl logs
flyctl status
```

### Integration

The main Nuxt application communicates with this service through HTTP requests to evaluate AI-generated responses in real-time, ensuring quality and relevance of automated guest communication.

## Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/my-new-feature`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature/my-new-feature`
5. Submit a pull request

## License

Proprietary - All Rights Reserved

## Learn More

- [Nuxt 3 Documentation](https://nuxt.com/docs/getting-started/introduction)
- [Vue 3 Documentation](https://vuejs.org/guide/introduction.html)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Nuxt UI Documentation](https://ui.nuxt.com/)
