import { put } from '@vercel/blob'
import { sendTransactional } from '~/services/loops'

// Constants
export const SMART_SPACES_TYPES = {
  COVER: 'cover',
  AMENITIES: 'amenities',
}

export const EMAIL_TEMPLATES = {
  COVER_DOWNLOAD: 'TXN_APP_SMARTSPACE_DOWNLOADCOVER_SINGLELISTING',
  AMENITIES_DOWNLOAD: 'TXN_APP_SMARTSPACE_DOWNLOADAMENITIES_SINGLELISTING',
}

/**
 * Interface for the request body
 */
export interface SmartSpacesRequestBody {
  type: string
  amenities?: string[]
}

/**
 * Saves a PDF to Vercel Blob storage and sends a notification email
 * 
 * @param id Listing ID
 * @param pdfBytes PDF data as Buffer
 * @param type Type of smart space (cover or amenities)
 * @param listingName Name of the listing
 * @param userEmail Email of the user to notify
 * @returns URL of the saved PDF
 */
export async function savePdfAndNotify(
  id: string,
  pdfBytes: Buffer,
  type: string,
  listingName: string,
  userEmail: string,
): Promise<string> {
  // Generate a unique filename with timestamp
  const fileName = `smart-spaces/${id}/${type}-${Date.now()}.pdf`
  
  // Save PDF to Vercel Blob storage
  const blob = await put(fileName, pdfBytes, {
    access: 'public',
    contentType: 'application/pdf',
  })
  
  const url = blob.url
  
  if (!url) {
    throw createError({ 
      statusCode: 500, 
      statusMessage: 'Failed to get valid URL from storage' 
    })
  }
  
  // Determine which email template to use
  const emailTemplate = type === SMART_SPACES_TYPES.COVER 
    ? EMAIL_TEMPLATES.COVER_DOWNLOAD 
    : EMAIL_TEMPLATES.AMENITIES_DOWNLOAD
  
  // Send notification email
  await sendTransactional(
    emailTemplate,
    userEmail,
    {
      listingName,
      fileLink: url,
    },
  )
  
  return url
}
