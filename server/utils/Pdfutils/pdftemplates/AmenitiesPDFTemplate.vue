<script setup>
import { Document, Image, Page, Text, View } from '@ceereals/vue-pdf'
import { computed, onMounted, reactive } from 'vue'

const props = defineProps({
  cards: Array,
  listing: Object,
})
const cards = props.cards || []
const listing = props.listing

// Calculate number of pages needed (10 cards per page)
const totalPages = computed(() => Math.ceil(cards.length / 10))

// Business card styles
const cardContainerStyle = reactive({
  flexDirection: 'row',
  flexWrap: 'wrap',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '0mm',
  marginTop: '0mm',
  marginBottom: '0mm',
})

// Standard business card size (85mm × 55mm)
const businessCardStyle = reactive({
  width: '85mm',
  height: '55mm',
  border: '1pt dotted #999',
  margin: '0mm',
  padding: '3mm',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'space-between',
})

const cardTitleStyle = reactive({
  fontSize: '10pt',
  fontWeight: 'bold',
  textAlign: 'center',
  marginBottom: '2mm',
})

const scanTextStyle = reactive({
  fontSize: '9pt',
  textAlign: 'center',
  marginTop: '2mm',
})

const qrCodeContainerStyle = reactive({
  alignItems: 'center',
  justifyContent: 'center',
})

// Add a style for the listing name header
const listingHeaderStyle = reactive({
  fontSize: '14pt',
  fontWeight: 'bold',
  textAlign: 'left',
  marginBottom: '5mm',
  marginLeft: '10mm',
  marginTop: '3mm',
})

// Add a style for the page number
const pageNumberStyle = reactive({
  fontSize: '9pt',
  textAlign: 'center',
  position: 'absolute',
  bottom: '2mm',
  left: 0,
  right: 0,
})

onMounted(() => {

})
</script>

<template>
  <Document>
    <!-- Create a page for each set of 10 cards -->
    <Page
      v-for="page in totalPages"
      :key="page"
      size="A4"
      orientation="portrait"
    >
      <!-- Listing Name Header -->
      <Text :style="listingHeaderStyle">
        {{ listing?.name || 'Business Cards' }}
      </Text>

      <View :style="cardContainerStyle">
        <!-- Loop through cards for this page (10 per page) -->
        <View
          v-for="(card) in cards.slice((page - 1) * 10, page * 10)"
          :key="card.id"
          :style="businessCardStyle"
        >
          <!-- Card Title -->
          <Text :style="cardTitleStyle">
            {{ card.name }}
          </Text>

          <!-- QR Code -->
          <View :style="qrCodeContainerStyle">
            <Image
              :source="card.qrCodePath"
              style="width: 65px; height: 65px;"
            />
          </View>

          <!-- Bottom Text -->
          <Text :style="scanTextStyle">
            Scan for Details
          </Text>
        </View>

        <!-- Add empty placeholder cards if needed to maintain grid of 2 -->
        <View
          v-for="i in (2 - (cards.slice((page - 1) * 10, page * 10).length % 2)) % 2"
          :key="`placeholder-${i}`"
          :style="[businessCardStyle, { opacity: 0 }]"
        />
      </View>

      <!-- Page Footer with Page Number -->
      <Text :style="pageNumberStyle">
        Page {{ page }} of {{ totalPages }}
      </Text>
    </Page>
  </Document>
</template>
