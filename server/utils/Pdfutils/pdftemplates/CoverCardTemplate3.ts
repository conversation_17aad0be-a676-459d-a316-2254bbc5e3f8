import { Document, Image, Page, Text, View } from '@ceereals/vue-pdf'
import { defineComponent, h } from 'vue'

export default defineComponent({
  name: 'CoverCardTemplate2',
  props: {
    listingName: String,
    qrCode: String,
    imageUrl: String,
    adminUser: Object,
    bubbleUrl: String,
    callingUrl: String,
    emailUrl: String,
    wwwUrl: String,
  },
  setup(props) {
    return () =>
      h(Document, {}, {
        default: () => [
          h(Page, { size: 'A4', wrap: true }, {
            default: () => [

              h(View, {
                style: {
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  fontFamily: 'Helvetica',
                  padding: 0,
                },
              }, {
                default: () => [
                  // Top Section: Digital Guidebook and Listing Name
                  h(View, {
                    style: {
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      textAlign: 'center',
                      height: '20%',
                      fontWeight: 'bold',
                    },
                  }, {
                    default: () => [
                      h(Text, {
                        style: {
                          fontSize: '28px',
                          fontFamily: 'Helvetica',
                          color: '#333333',
                          textAlign: 'center',
                          position: 'absolute',
                          width: '100%',
                          top: '7%',
                          whiteSpace: 'pre-line', // allows line breaks
                        },
                      }, `${props.listingName}`),
                    ],
                  }),

                  // // Bubble image and SCAN ME
                  // h(Image, {
                  //   source: props.bubbleUrl,
                  //   style: {
                  //     position: 'absolute',
                  //     right: '2%',
                  //     top: '1%',
                  //     width: '200px',
                  //     height: '100px',
                  //   },
                  // }),
                  // h(Text, {
                  //   style: {
                  //     position: 'absolute',
                  //     right: '5%',
                  //     top: '3%',
                  //     fontSize: '32px',
                  //     fontWeight: 'bold',
                  //     fontFamily: 'Helvetica',
                  //     color: '#333333',
                  //   },
                  // }, 'SCAN ME !'),

                  // Thumbnail Section
                  // Thumbnail Section
                  h(View, {
                    style: {
                      position: 'absolute',
                      top: '20%',
                      left: 0,
                      width: '100%',
                      height: '70%',
                    },
                  }, {
                    default: () => [
                      h(Image, {
                        source: props.imageUrl,
                        style: {
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        },
                      }),
                    ],
                  }),

                  // Add top booking-style shapes above the image (siblings, not children)

                  h(View, {
                    style: {
                      position: 'absolute',
                      top: '20%', // slightly above the image
                      left: '-25px',
                      height: '50px',
                      width: '400px',
                      borderBottom: '2px solid #3b82f6',
                      backgroundColor: '#3b82f6',
                      transform: 'skew(45deg),translateY(-50%)',
                      transformOrigin: 'right top',
                    },
                  }),
                  h(View, {
                    style: {
                      position: 'absolute',
                      top: '20%', // slightly above the image
                      left: '-25px',
                      height: '50px',
                      width: '400px',
                      borderTop: '2px solid #3b82f6',
                      backgroundColor: '#3b82f6',
                      transform: 'skew(-45deg)',
                      transformOrigin: 'right top',
                    },
                  }),

                  // Add unskewed, wrapping text above the red shape
                  h(Text, {
                    style: {
                      position: 'absolute',
                      top: '16%', // slightly above the red shape
                      left: '2%',
                      width: '300px',
                      color: 'white',
                      fontSize: '28px',
                      fontWeight: 'bold',
                      fontFamily: 'Helvetica',
                      textAlign: 'left',
                      whiteSpace: 'pre-wrap',
                    },
                  }, 'Scan QR code to open Digital Guidebook'),

                  // QR Code in the Top Right Corner
                  props.qrCode && h(View, {
                    style: {
                      position: 'absolute',
                      top: '8%',
                      right: '2%',
                      // transform: 'translateY(-px)',
                      backgroundColor: 'white',
                      padding: '4px',
                      borderRadius: '8px',
                      border: '5px solid black',
                    },
                  }, {
                    default: () => [
                      h(Image, {
                        source: props.qrCode,
                        style: {
                          width: '180px',
                          height: '180px',
                        },
                      }),
                    ],
                  }),

                  // Bottom Section: Contact and Booking Information
                  h(View, {
                    style: {
                      position: 'absolute',
                      bottom: '0px',
                      left: '0px',
                      width: '100%',
                      height: '10%',
                    },
                  }, {
                    default: () => [
                      h(View, { style: { position: 'absolute', bottom: '8px', left: '13px' } }, {
                        default: () => [
                          h(Text, {
                            style: {
                              fontSize: '12px',
                              fontFamily: 'Helvetica',
                              fontWeight: 'bold',
                              color: '#555555',
                              padding: '5px',
                            },
                          }, 'Contact Information'),
                          h(View, { style: { flexDirection: 'row', alignItems: 'center', padding: '5px' } }, {
                            default: () => [
                              h(Image, {

                                source: props.emailUrl,
                                style: { width: '16px', height: '16px', marginRight: '8px' },
                              }),
                              h(Text, {
                                style: { fontSize: '12px', fontFamily: 'Helvetica', color: '#555555' },
                              }, props.adminUser?.guestContactEmail || ''),
                            ],
                          }),
                          h(View, { style: { flexDirection: 'row', alignItems: 'center', padding: '5px' } }, {
                            default: () => [
                              h(Image, {

                                source: props.callingUrl,
                                style: { width: '16px', height: '16px', marginRight: '8px' },
                              }),
                              h(Text, {
                                style: { fontSize: '12px', fontFamily: 'Helvetica', color: '#555555' },
                              }, props.adminUser?.guestContactPhone || ''),
                            ],
                          }),
                        ],
                      }),
                    ],
                  }),

                  // Booking Information
                  h(View, {
                    style: {
                      position: 'absolute',
                      height: '50px',
                      width: '400px',
                      right: '-5px',
                      bottom: '10%',
                      borderBottom: '2px solid #3b82f6',
                      backgroundColor: '#3b82f6',
                      transform: 'skew(-45deg)',
                      transformOrigin: 'left bottom',
                    },
                  }),
                  h(View, {
                    style: {
                      position: 'absolute',
                      height: '50px',
                      width: '400px',
                      bottom: '10%',
                      right: '-5px',
                      borderTop: '2px solid #3b82f6',
                      backgroundColor: '#3b82f6',
                      transform: 'skew(45deg),translateY(50%)',
                      transformOrigin: 'left bottom',
                    },
                  }),
                  h(Text, {
                    style: {
                      fontSize: '20px',
                      fontFamily: 'Helvetica',
                      fontWeight: 'bold',
                      width: '300px',
                      color: 'white',
                      textAlign: 'right',
                      position: 'absolute',
                      bottom: '10%',
                      right: '15px',
                    },
                  }, props.adminUser?.offer || ''),
                  h(View, {
                    style: {
                      flexDirection: 'row',
                      alignItems: 'center',
                      position: 'absolute',
                      bottom: '10%',
                      right: '15px',
                      transform: 'translateY(30%)',
                    },
                  }, {
                    default: () => [
                      h(Image, {

                        source: props.wwwUrl,
                        style: { width: '20px', height: '20px', marginRight: '8px' },
                      }),
                      (() => {
                        // Estimate text width for Helvetica: average 0.56em per char at 20px font size
                        const maxWidthPx = 350
                        const minFontSize = 10
                        let fontSize = 20
                        const text = props.adminUser?.bookingSiteUrl || ''
                        // Helvetica average width per char at 20px: ~11.2px (0.56 * 20)
                        const avgCharWidth = 0.56
                        let estWidth = text.length * fontSize * avgCharWidth
                        while (estWidth > maxWidthPx && fontSize > minFontSize) {
                          fontSize--
                          estWidth = text.length * fontSize * avgCharWidth
                        }
                        return h(Text, {
                          style: { fontSize: `${fontSize}px`, fontFamily: 'Helvetica', color: 'white' },
                        }, text)
                      })(),
                    ],
                  }),
                  h(Text, {
                    style: {
                      fontSize: '10px',
                      fontFamily: 'Helvetica',
                      color: 'black',
                      position: 'absolute',
                      bottom: '2px',
                      right: '50%',
                      transform: 'translateX(50%)',
                    },
                  }, 'Powered by Yada.ai'),
                ],
              }),
            ],
          }),
        ],
      })
  },
})
