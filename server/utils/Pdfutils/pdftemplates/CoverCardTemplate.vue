<script setup>
import { useListingStore } from '@/stores/listing'
import { useWorkspaceStore } from '@/stores/workspace'
import { Document, Image, Page, Text, View } from '@ceereals/vue-pdf'
import { computed, onBeforeMount, ref } from 'vue'
import phoneIcon from './calling.png'
// Import local PNG icons
import emailIcon from './email.png'
import webIcon from './web.png'

const props = defineProps({
  listingId: String, // Only pass listing ID
  qrCode: String,
})

const listingId = props.listingId
const qrCode = props.qrCode

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

// Initialize with listing ID
const listing = ref(null)

const adminUser = computed(() => workspaceStore.profile)

// Create a reactive reference for the image URL
const imageUrl = ref(null)

// Computed property for thumbnail style without height constraints
const thumbnailStyle = computed(() => {
  return {
    width: '90%',
    objectFit: 'contain',
  }
})

onBeforeMount(async () => {
  try {
    // Load listing data using listing ID
    const listingData = await listingStore.loadListing(listingId)
    if (listingData) {
      listing.value = listingData
      // Check if pictures array exists and has items
      if (listingData.pictures && listingData.pictures.length > 0) {
        imageUrl.value = listingData.pictures[0].src
      }
      else if (listingData.thumbnail) {
        imageUrl.value = listingData.thumbnail
      }
    }
  }
  catch (error) {
    console.error('Error loading listing data:', error)
  }
})
</script>

<template>
  <Document>
    <Page size="A4" wrap>
      <View :style="{ flexDirection: 'column', width: '100%', height: '100%', fontFamily: 'Times-Roman' }">
        <!-- Welcome Header Section -->
        <View :style="{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }">
          <Text :style="{ fontSize: '32px', textAlign: 'center', marginBottom: '20px', marginTop: '20px', fontFamily: 'Times-Bold', color: '#333333' }">
            WELCOME
          </Text>
          <!-- <Text :style="{ fontSize: '24px', textAlign: 'center', marginBottom: '20px', fontFamily: 'Times-Roman', color: '#555555' }">
            To {{ listing.name }}
          </Text> -->
        </View>

        <!-- Thumbnail Section -->
        <View :style="{ width: '100%', marginBottom: '20px' }">
          <View v-if="imageUrl" :style="{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
            <Image :source="imageUrl" :style="thumbnailStyle" />
          </View>
        </View>

        <!-- QR Code Section -->
        <View :style="{ width: '100%', marginBottom: '40px' }">
          <View :style="{ flexDirection: 'row', alignItems: 'center', margin: '0 5% 0 5%', flexWrap: 'nowrap', width: '90%', alignSelf: 'center' }">
            <View :style="{ flex: 1 }">
              <Text :style="{ fontSize: '16px', whiteSpace: 'nowrap', overflow: 'visible', fontFamily: 'Times-Roman', color: '#333333' }">
                Scan QR code to access digital guidebook
              </Text>
              <Text v-if="listing" :style="{ fontSize: '16px', marginTop: '10px', whiteSpace: 'nowrap', overflow: 'visible', fontFamily: 'Times-Roman', color: '#555555' }">
                and enjoy your stay with us at {{ listing.name }}
              </Text>
              <Text v-else :style="{ fontSize: '16px', marginTop: '10px', whiteSpace: 'nowrap', overflow: 'visible', fontFamily: 'Times-Roman', color: '#555555' }">
                and enjoy your stay with us
              </Text>
            </View>
            <View :style="{ alignItems: 'flex-end' }">
              <Image :source="qrCode" :style="{ width: '100px', height: '100px' }" />
            </View>
          </View>
        </View>

        <!-- Flexible Spacer -->
        <View :style="{ flex: 1 }" />

        <!-- Middle Section with Contact Info -->
        <View :style="{ flexDirection: 'column', width: '100%', marginBottom: '40px' }">
          <!-- Two-column layout for contact and booking info -->
          <View :style="{ flexDirection: 'row', justifyContent: 'space-between', margin: '0 5% 0 5%', width: '90%', alignSelf: 'center' }">
            <!-- Left column: Contact Information -->
            <View :style="{ flex: 1, paddingRight: '10px', alignItems: 'flex-start' }">
              <Text :style="{ fontSize: '16px', fontWeight: 'bold', margin: '0 0 10px 0', fontFamily: 'Times-Bold', color: '#333333' }">
                Contact Information
              </Text>
              <!-- Email with icon -->
              <View :style="{ flexDirection: 'row', alignItems: 'center', marginBottom: '10px' }">
                <Image :source="emailIcon" :style="{ width: '16px', height: '16px', marginRight: '8px' }" />
                <Text :style="{ fontSize: '14px', fontFamily: 'Times-Roman', color: '#555555' }">
                  {{ adminUser.guestContactEmail }}
                </Text>
              </View>
              <!-- Phone with icon -->
              <View :style="{ flexDirection: 'row', alignItems: 'center' }">
                <Image :source="phoneIcon" :style="{ width: '16px', height: '16px', marginRight: '8px' }" />
                <Text :style="{ fontSize: '14px', fontFamily: 'Times-Roman', color: '#555555' }">
                  {{ adminUser.guestContactPhone }}
                </Text>
              </View>
            </View>

            <!-- Right column: Booking Information -->
            <View :style="{ flex: 1, paddingLeft: '10px', alignItems: 'flex-end' }">
              <Text :style="{ fontSize: '16px', fontWeight: 'bold', margin: '0 0 10px 0', fontFamily: 'Times-Bold', color: '#333333' }">
                Direct booking at
              </Text>
              <!-- Website with icon -->
              <View :style="{ flexDirection: 'row', alignItems: 'center' }">
                <Image :source="webIcon" :style="{ width: '16px', height: '16px', marginRight: '8px' }" />
                <Text :style="{ fontSize: '14px', fontFamily: 'Times-Roman', color: '#555555' }">
                  {{ adminUser.bookingSiteUrl }}
                </Text>
              </View>
            </View>
          </View>
        </View>

        <!-- Flexible Spacer -->
        <View :style="{ flex: 1 }" />

        <!-- Bottom Section with only Offer -->
        <View :style="{ flexDirection: 'column', width: '100%', marginBottom: '50px' }">
          <!-- Offer at the bottom -->
          <View :style="{ margin: '0 5% 0 5%', width: '90%', alignSelf: 'center' }">
            <Text :style="{ fontSize: '14px', textAlign: 'center', fontFamily: 'Times-Roman', color: '#555555' }">
              {{ adminUser.offer }}
            </Text>
          </View>
        </View>
      </View>
    </Page>
  </Document>
</template>
