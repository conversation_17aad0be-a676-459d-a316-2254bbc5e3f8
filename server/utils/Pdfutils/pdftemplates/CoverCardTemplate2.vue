<script setup>
import {  } from '@/stores/workspace'
import { Document, Image, Page, Text, View } from '@ceereals/vue-pdf'
import {  } from 'vue'

// Import local PNG icons
import bubble from './bubble.png'
import phoneIcon from './calling.png'
import emailIcon from './email.png'
import www from './www.png'

const props = defineProps({
  listingName: String,
  qrCode: String,
  imageUrl: String,
  adminUser: Object,
})

const listingName = props.listingName
const qrCode = props.qrCode
const imageUrl = props.imageUrl
const adminUser = props.adminUser
</script>

<template>
  <Document>
    <Page size="A4" wrap>
      <!-- Main Content -->
      <View :style="{ position: 'relative', width: '100%', height: '100%', fontFamily: 'Helvetica', padding: '0' }">
        <!-- Top Section: Digital Guidebook and Listing Name -->
        <View
          :style="{
            position: 'absolute',
            top: '20px',
            left: '0',
            width: '100%',
            height: '20%',
            textAlign: 'left',
            fontWeight: 'bold',
          }"
        >
          <Text
            :style="{
              fontSize: '32px',
              fontFamily: 'Helvetica',
              color: '#333333',
              textAlign: 'left',
              position: 'absolute',
              bottom: '80%',
              left: '5%',
            }"
          >
            Digital
          </Text>
          <Text
            :style="{
              fontSize: '32px',
              fontFamily: 'Helvetica',
              color: '#333333',
              textAlign: 'left',
              position: 'absolute',
              bottom: '50%',
              left: '5%',
            }"
          >
            Guidebook
          </Text>
          <Text
            :style="{
              fontSize: '32px',
              fontFamily: 'Helvetica',
              color: '#333333',
              textAlign: 'left',
              position: 'absolute',
              bottom: '20%',
              left: '5%',
            }"
          >
            {{ listingName }}
          </Text>
        </View>

        <Image :source="bubble" :style="{ position: 'absolute', right: '2%', top: '1%', width: '200px', height: '100px' }" />
        <Text :style="{ position: 'absolute', right: '5%', top: '3%', fontSize: '32px', fontWeight: 'bold', fontFamily: 'Helvetica', color: '#333333' }">
          SCAN ME !
        </Text>
        <!-- Thumbnail Section -->
        <View
          :style="{
            position: 'absolute',
            top: '20%',
            left: '0',
            width: '100%',
            height: '70%',
          }"
        >
          <Image
            :source="imageUrl" :style="{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }"
          />,
        </View>
        <!-- QR Code in the Top Right Corner -->
        <View
          v-if="qrCode" :style="{
            position: 'absolute',
            top: '20%',
            right: '15%',
            transform: 'translateY(-59px)',
            backgroundColor: 'white',
            padding: '4px',
            borderRadius: '8px',
            border: '5px solid black',
          }"
        >
          <Image :source="qrCode" :style="{ width: '130px', height: '130px' }" />,
        </View>
        <!-- Bottom Section: Contact and Booking Information -->
        <!-- Contact Information -->
        <View
          :style="{
            position: 'absolute',
            bottom: '0px',
            left: '0px',
            width: '100%',
            height: '10%',
          }"
        >
          <View :style="{ position: 'absolute', bottom: '8px', left: '13px' }">
            <Text :style="{ fontSize: '12px', fontFamily: 'Helvetica', fontWeight: 'bold', color: '#555555', padding: '5px' }">
              Contact Information
            </Text>
            <View :style="{ flexDirection: 'row', alignItems: 'center', padding: '5px' }">
              <Image :source="emailIcon" :style="{ width: '16px', height: '16px', marginRight: '8px' }" />,
              <Text :style="{ fontSize: '12px', fontFamily: 'Helvetica', color: '#555555' }">
                {{ adminUser.guestContactEmail }}
              </Text>
            </View>
            <View :style="{ flexDirection: 'row', alignItems: 'center', padding: '5px' }">
              <Image :source="phoneIcon" :style="{ width: '16px', height: '16px', marginRight: '8px' }" />,
              <Text :style="{ fontSize: '12px', fontFamily: 'Helvetica', color: '#555555' }">
                {{ adminUser.guestContactPhone }}
              </Text>
            </View>
          </View>
        </View>
        <!-- Booking Information -->
        <View :style="{ position: 'absolute', height: '50px', width: '400px', right: '-5px', bottom: '10%', borderBottom: '2px solid red', backgroundColor: 'red', transform: 'skew(-45deg)', transformOrigin: 'left bottom' }" />
        <View :style="{ position: 'absolute', height: '50px', width: '400px', bottom: '10%', right: '-5px', borderTop: '2px solid red', backgroundColor: 'red', transform: 'skew(45deg),translateY(50%)', transformOrigin: 'left bottom' }" />
        <Text
          :style="{
            fontSize: '20px',
            fontFamily: 'Helvetica',
            fontWeight: 'bold',
            width: '300px',
            color: 'white',
            textAlign: 'right',
            position: 'absolute',
            bottom: '10%',
            right: '15px',
          }"
        >
          {{ adminUser.offer }}
        </Text>
        <View :style="{ flexDirection: 'row', alignItems: 'center', position: 'absolute', bottom: '10%', right: '15px', transform: 'translateY(30%)' }">
          <Image :source="www" :style="{ width: '20px', height: '20px', marginRight: '8px' }" />
          <Text :style="{ fontSize: '20px', fontFamily: 'Helvetica', color: 'white' }">
            {{ adminUser.bookingSiteUrl }}
          </Text>
        </View>
        <Text
          :style="{
            fontSize: '10px',
            fontFamily: 'Helvetica',
            color: 'black',
            position: 'absolute',
            bottom: '2px',
            right: '50%',
            transform: 'translateX(50%)',
          }"
        >
          Powered by Yada.ai
        </Text>
      </View>
    </Page>
  </Document>
</template>
