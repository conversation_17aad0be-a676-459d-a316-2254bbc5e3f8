import { Buffer } from 'node:buffer'
import { renderToBuffer } from '@ceereals/vue-pdf'
import fetch from 'node-fetch'
import { PDFDocument, rgb } from 'pdf-lib'
import QRCode from 'qrcode'
import { h } from 'vue'
import CoverCardTemplate3 from './pdftemplates/CoverCardTemplate3'

// Types
export interface CardData {
  title: string
  subtitle: string
  qrContent: string
}

export interface ListingData {
  id: string
  name: string
  thumbnail: string
  pictures?: Array<{ src: string }>
}

export interface AdminUser {
  guestContactEmail?: string
  guestContactPhone?: string
  bookingSiteUrl?: string
  offer?: string
}

// Constants
const QR_CODE_OPTIONS = {
  errorCorrectionLevel: 'M',
  width: 200,
  margin: 1,
}

/**
 * Generates a QR code as a PNG buffer
 * @param text The text to encode in the QR code
 * @returns Buffer containing the QR code image
 */
export async function generateQRCodePNG(text: string): Promise<Buffer> {
  try {
    const qrBuffer = await QRCode.toBuffer(text, QR_CODE_OPTIONS)
    return qrBuffer
  }
  catch (error) {
    console.error('Failed to generate QR code', { error, text })
    throw error
  }
}

/**
 * Fetches an image from a URL and returns it as a Buffer
 * @param imageUrl URL of the image to fetch
 * @returns Buffer containing the image data
 */
export async function getImageFromUrl(imageUrl: string): Promise<Buffer> {
  try {
    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`)
    }
    return Buffer.from(await response.arrayBuffer())
  }
  catch (error) {
    console.error('Error downloading image from URL', { error, imageUrl })
    throw error
  }
}

/**
 * Generates a cover card PDF for a listing
 * @param listing Listing data to include in the cover card
 * @param adminUser Admin user data to include in the cover card
 * @returns Buffer containing the PDF data
 */
export async function generateCoverCard(
  listing: ListingData,
  adminUser: AdminUser,
): Promise<Buffer> {
  try {
    // Generate QR code as data URL for the template
    const qrCodeUrl = `https://guide.yada.ai/${listing.id}?src=qr&sub_src=cover`
    const qrCodeBuffer = await generateQRCodePNG(qrCodeUrl)
    const qrCodeBase64 = `data:image/png;base64,${qrCodeBuffer.toString('base64')}`

    // Use the first picture or fallback to thumbnail
    const imageUrl = listing.pictures?.[0]?.src || listing.thumbnail

    // Get asset images as base64 data URLs
    const assets = useStorage('assets:server')
    const bubbleUrl = assets.getItemRaw('bubble.png')
    const callingUrl = assets.getItemRaw('calling.png')
    const emailUrl = assets.getItemRaw('email.png')
    const wwwUrl = assets.getItemRaw('www.png')

    // Prepare props for the template
    const props = {
      listingName: listing.name,
      qrCode: qrCodeBase64,
      imageUrl,
      adminUser,
      bubbleUrl,
      callingUrl,
      emailUrl,
      wwwUrl,
    }

    // Render the PDF using vue-pdf Node API and the TS template
    const pdfBuffer = await renderToBuffer(h(CoverCardTemplate3, props))

    return Buffer.from(pdfBuffer)
  }
  catch (error) {
    console.error('Error generating cover card with vue-pdf', {
      error,
      listingId: listing.id,
      listingName: listing.name,
    })
    throw error
  }
}

/**
 * Fills a template PDF with amenity cards
 * @param cards Array of card data to include in the template
 * @param listingName Name of the listing
 * @returns Buffer containing the PDF data
 */
export async function fillTemplate(cards: CardData[], listingName: string): Promise<Buffer> {
  try {
    const assets = useStorage('assets:server')
    const qrTemplatePdfBytes = await assets.getItemRaw('amenities-qr-template.pdf')

    // Convert to Buffer if your PDF library (like pdf-lib) expects Buffer
    const pdfBuffer = Buffer.from(qrTemplatePdfBytes as Uint8Array)

    const pdfDoc = await PDFDocument.load(pdfBuffer)

    // Store the original blank template content
    const originalTemplatePage = pdfDoc.getPages()[0]
    const { width, height } = originalTemplatePage.getSize()
    const blankTemplateContent = await pdfDoc.embedPage(originalTemplatePage)

    // Constants for measurements (in points, 72 points = 1 inch)
    const CARDS_PER_ROW = 2
    const CARDS_PER_COLUMN = 5
    const CARDS_PER_PAGE = CARDS_PER_ROW * CARDS_PER_COLUMN
    const TOTAL_PAGES_NEEDED = Math.ceil(cards.length / CARDS_PER_PAGE)

    // Remove the original page and create all pages fresh
    pdfDoc.removePage(0)

    // Create all pages from scratch
    for (let i = 0; i < TOTAL_PAGES_NEEDED; i++) {
      const newPage = pdfDoc.addPage([width, height])
      // Copy the blank template content to the new page
      newPage.drawPage(blankTemplateContent, {
        x: 0,
        y: 0,
        width,
        height,
      })

      // Add listing name to top left
      newPage.drawText(`Listing: ${listingName}`, {
        x: 30,
        y: height - 30,
        size: 10,
        color: rgb(0.4, 0.4, 0.4),
        font: await pdfDoc.embedFont('Helvetica'),
      })

      // Add listing name to bottom left
      newPage.drawText(`Listing: ${listingName}`, {
        x: 30,
        y: 20,
        size: 10,
        color: rgb(0.4, 0.4, 0.4),
        font: await pdfDoc.embedFont('Helvetica'),
      })
    }

    // Process each card
    for (let cardIndex = 0; cardIndex < cards.length; cardIndex++) {
      const pageIndex = Math.floor(cardIndex / CARDS_PER_PAGE)
      const cardPositionOnPage = cardIndex % CARDS_PER_PAGE
      const row = Math.floor(cardPositionOnPage / CARDS_PER_ROW)
      const col = cardPositionOnPage % CARDS_PER_ROW

      const page = pdfDoc.getPages()[pageIndex]
      const PAGE_WIDTH = page.getWidth()
      const PAGE_HEIGHT = page.getHeight()
      const CARD_WIDTH = 3.5 * 72 // 3.5 inches
      const CARD_HEIGHT = 2 * 72 // 2 inches
      const QR_SIZE = Math.min(CARD_WIDTH, CARD_HEIGHT) * 0.5
      const TITLE_SIZE = 12
      const SUBTITLE_SIZE = 10

      // Calculate position for this card
      const xMargin = (PAGE_WIDTH - CARDS_PER_ROW * CARD_WIDTH) / 2
      const yMargin = (PAGE_HEIGHT - CARDS_PER_COLUMN * CARD_HEIGHT) / 2
      const pos = {
        x: xMargin + col * CARD_WIDTH,
        y: PAGE_HEIGHT - yMargin - (row + 1) * CARD_HEIGHT,
      }

      const centerX = pos.x + CARD_WIDTH / 2
      const centerY = pos.y + CARD_HEIGHT / 2

      const card = cards[cardIndex]

      // Generate and embed QR code
      const qrCodeImage = await generateQRCodePNG(card.qrContent)
      const qrCodeImageEmbed = await pdfDoc.embedPng(qrCodeImage)

      // Add title
      page.drawText(card.title, {
        x: centerX - (card.title.length * TITLE_SIZE) / 4,
        y: pos.y + CARD_HEIGHT - TITLE_SIZE * 2,
        size: TITLE_SIZE,
        font: await pdfDoc.embedFont('Helvetica-Bold'),
      })

      // Add subtitle
      page.drawText(card.subtitle, {
        x: centerX - (card.subtitle.length * SUBTITLE_SIZE) / 4,
        y: pos.y + TITLE_SIZE,
        size: SUBTITLE_SIZE,
        color: rgb(0.4, 0.4, 0.4),
      })

      // Draw QR code centered
      page.drawImage(qrCodeImageEmbed, {
        x: centerX - QR_SIZE / 2,
        y: centerY - QR_SIZE / 2,
        width: QR_SIZE,
        height: QR_SIZE,
      })
    }

    const pdfBytes = await pdfDoc.save()
    return Buffer.from(pdfBytes)
  }
  catch (error) {
    console.error('Error filling template', { error })
    throw error
  }
}
