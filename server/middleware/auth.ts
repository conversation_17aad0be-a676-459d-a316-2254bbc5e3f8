import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getRequestHeader } from 'h3'
import { auth } from '~/helpers/firebase'

const PROTECTED_ROUTES = ['listings', 'ai', 'forms', 'sops', 'tasks', 'workspace', 'integrations', 'smart-spaces', 'widget', 'contacts', 'stays', 'workflows', 'conversations', 'chats']

export default defineEventHandler(async (event) => {
  const url = event.node.req.url
  if (!url) {
    return
  }

  const isProtectedRoute = PROTECTED_ROUTES.some(route => url.startsWith(`/api/${route}`))

  if (!isProtectedRoute) {
    return
  }

  const authHeader = getRequestHeader(event, 'Authorization')

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Missing or invalid Authorization header',
    })
  }

  const token = authHeader.split(' ')[1]

  try {
    const decodedToken = await auth.verifyIdToken(token)
    event.context.auth = decodedToken
  }
  catch (error) {
    console.error('unable to verify token', error)
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid or expired token',
    })
  }
})
