import { firestore } from '~/helpers/firebase';
import Calry from '~/services/calry'
import { calryReservationStatuses, calryChannelTypes } from '~/shared/enums';

export default defineEventHandler(async (event) => {
  const body = await readBody(event)

  // Respond immediately with 200 OK
  setResponseStatus(event, 200)

  console.log('syncing calry reservation details', JSON.stringify(body))

  try {
    const calry = new Calry(body?.integrationAccountId)
    const reservation = await calry.getReservation(body?.calryReservationId)

    console.log('calry reservation details', JSON.stringify(reservation))

    const [userSnap, listingSnap, contactSnap, staySnap] = await Promise.all([
      firestore.collection('users').doc(body.userId).get(),
      firestore.collection('listings').where('calryPropertyId', '==', `${reservation?.data?.propertyId}`).where('calryRoomTypeId', '==', `${reservation?.data?.roomTypeIds?.[0] || reservation?.data?.propertyId}`).get(),
      firestore.collection('contacts').where('userId', '==', body.userId).where('calryId', '==', `${reservation?.data?.primaryGuest.id}`).get(),
      firestore.collection('stays').where('userId', '==', body.userId).where('calryId', '==', `${reservation?.data?.id}`).get()
    ]);

    // Validate both user and listing exist
    if (!userSnap.exists) {
      console.error(`No user found for user ID: ${body.userId}`);
      return { success: false, error: 'User not found' };
    }

    if (listingSnap.empty) {
      console.error(`No listing found for Calry Property ID: ${reservation?.data?.propertyId}`);
      return { success: false, error: 'Listing not found' };
    }

    if (contactSnap.empty) {
      console.error(`No contact found for Calry ID: ${reservation?.data?.primaryGuest.id}`);
    }
    if (staySnap.empty) {
      console.error(`No stay found for Calry ID: ${reservation?.data?.id}`);
    }

    console.log(`User found: ${userSnap.id}, Listing found: ${listingSnap.docs[0].id}`);

    // Extract user and listing data
    const user = { ...userSnap.data(), id: userSnap.id };
    const listing = { ...listingSnap.docs[0].data(), id: listingSnap.docs[0].id };

    // Convert arrival and departure dates to millisecond Unix timestamps
    const arrivalDate = reservation?.data?.arrivalDate;
    const departureDate = reservation?.data?.departureDate; if (!arrivalDate || !departureDate) {
      console.error('Missing arrival or departure date in reservation data');
      return { success: false, error: 'Missing arrival or departure date' };
    }

    const checkInTimestamp = new Date(arrivalDate).getTime();
    const checkOutTimestamp = new Date(departureDate).getTime();

    // Prepare contact payload
    const contactPayload = {
      userId: user.id,
      calryId: String(reservation?.data?.primaryGuest?.id || ''),
      email: reservation?.data?.primaryGuest?.emails?.[0] || '',
      firstName: reservation?.data?.primaryGuest?.nameFirst || '',
      lastName: reservation?.data?.primaryGuest?.nameLast || '',
      fullName: reservation?.data?.primaryGuest?.name || '',
      lastActive: Date.now(),
      lastActivityAt: Date.now(),
      notes: [],
      phone: (reservation?.data?.primaryGuest?.mobileNumbers?.[0] || '').replace(/\s/g, ''),
      sources: ['calry']
    };

    // Prepare stay payload
    const stayPayload = {
      userId: user.id,
      calryId: String(reservation?.data?.id || ''),
      checkIn: checkInTimestamp,
      checkOut: checkOutTimestamp,
      totalPaid: reservation?.data?.finances?.totalPaid || reservation?.data?.totalPrice || 0,
      listingId: listing.id,
      status: calryReservationStatuses[reservation?.data?.status as keyof typeof calryReservationStatuses] || 'unknown',
      channel: calryChannelTypes[reservation?.data?.source as keyof typeof calryChannelTypes] || 'unknown',
    };

    console.log('Contact payload:', contactPayload);
    console.log('Stay payload:', stayPayload);

    try {
      let contactRef;
      let contactId;    // Handle contact creation or update
      if (contactSnap.empty) {
        // Create new contact
        contactRef = await firestore.collection('contacts').add(contactPayload);
        contactId = contactRef.id;
        console.log('Contact created with ID:', contactId);
      } else {
        // Update existing contact with only specific fields
        const existingContact = contactSnap.docs[0];
        contactRef = existingContact.ref;
        contactId = existingContact.id;

        const updatePayload = {
          firstName: contactPayload.firstName,
          lastName: contactPayload.lastName,
          fullName: contactPayload.fullName,
          phone: contactPayload.phone,
          email: contactPayload.email,
          lastActive: Date.now(),
          lastActivityAt: Date.now(),
        }

        console.log('update payload', updatePayload);

        await contactRef.update(updatePayload);
        console.log('Contact updated with ID:', contactId);
      }

      let stayRef;
      let stayId;

      // Handle stay creation or update
      if (staySnap.empty) {
        // Create new stay with contact reference
        const finalStayPayload = {
          ...stayPayload,
          contactId: contactId
        };

        stayRef = await firestore.collection('stays').add(finalStayPayload);
        stayId = stayRef.id;
        console.log('Stay created with ID:', stayId);

        // Update contact to include the stay ID in the stayIds array
        const existingStayIds = contactSnap.empty ? [] : (contactSnap.docs[0].data().stayIds || []);
        await contactRef.update({
          stayIds: [...existingStayIds, stayId]
        });
      } else {
        // Update existing stay with only specific fields
        const existingStay = staySnap.docs[0];
        stayRef = existingStay.ref;
        stayId = existingStay.id;

        const stayUpdatePayload = {
          checkIn: stayPayload.checkIn,
          checkOut: stayPayload.checkOut,
          totalPaid: stayPayload.totalPaid,
          status: stayPayload.status,
          contactId: contactId
        }

        console.log('Stay update payload:', stayUpdatePayload);

        await stayRef.update(stayUpdatePayload);
        console.log('Stay updated with ID:', stayId);

        // Always ensure contact has this stay in stayIds array (sync relationship)
        const existingContact = contactSnap.empty ? null : contactSnap.docs[0];
        const existingStayIds = existingContact?.data().stayIds || [];

        if (!existingStayIds.includes(stayId)) {
          await contactRef.update({
            stayIds: [...existingStayIds, stayId]
          });
          console.log('Added stayId to contact stayIds array');
        }
      }

      console.log('Successfully processed contact and stay with proper relationships');
      console.log('Contact ID:', contactId, 'Stay ID:', stayId);

      return {
        success: true,
        contactId,
        stayId,
        message: 'Contact and stay processed successfully'
      };

    } catch (error) {
      console.error('Error creating/updating contact and stay:', error);
      return { success: false, error: 'Failed to process reservation' };
    }

  } catch (error) {
    console.error('Error in background processing:', error);
    return { success: false, error: 'Failed to sync reservation' };
  }
})
