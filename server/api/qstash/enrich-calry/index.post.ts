import { firestore } from '~/helpers/firebase';

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  console.log('enriching calry stuff', JSON.stringify(body))

  setResponseStatus(event, 200)


  const [staySnap, contactSnap] = await Promise.all([
    firestore.collection('stays').where('userId', '==', body.userId).where('calryId', "==", `${body.calryReservationId}`).get(),
    firestore.collection('contacts').where('userId', '==', body.userId).where('calryId', "==", `${body.calryGuestId}`).get()
  ])

  if (staySnap.empty || contactSnap.empty) {
    console.error('No matching stay or contact found for userId:', body.userId);
    return { success: false, message: 'No matching stay or contact found' }
  }

  const stayId = staySnap.docs[0].id;
  const contactId = contactSnap.docs[0].id;

  try {
    await firestore.collection('conversations').doc(body.conversationId).update({ stayId, contactId });

  } catch (error) {
    console.error('Error updating conversation:', error);
    return { success: false, message: 'Error updating conversation' }
  }
  return 'success'
})
