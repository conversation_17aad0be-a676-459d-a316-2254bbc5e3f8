import Calry from '~/services/calry'
import { calculateRandomDelay, scheduleQstash } from '~/services/upstash'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)

  try {
    const calry = new Calry(body.integrationAccountId)
    const calryAccount = await calry.getAccount()
    const properties = await calry.getAllProperties()
    console.log(`[Calry Webhook] Found ${properties.length} properties. Scheduling import jobs...`)
    for (const property of properties) {
      const delay = calculateRandomDelay(properties.length)
      console.log(`[Calry Webhook] Scheduling import for property ${property.id || ''} with delay ${delay} seconds`)
      await scheduleQstash('https://nuxt-yada-ai.vercel.app/api/qstash/import-calry-property-as-listing', { property, calryAccount, integrationAccountId: body.integrationAccountId, userId: body.yadaUserId }, delay, 3)
    }
  }
  catch (e) {
    return 'success'
  }
})
