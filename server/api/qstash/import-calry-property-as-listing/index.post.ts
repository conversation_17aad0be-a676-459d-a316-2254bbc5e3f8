import { firestore } from '~/helpers/firebase'
import Calry from '~/services/calry'
import { initFaqs } from '~/services/yada'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const calry = new Calry(body.integrationAccountId)
  const roomTypes = await calry.getRoomTypesForProperty(body.property.id)
  for (const room of roomTypes) {
    // Check to see if the listing exists;
    const listingSnap: any = await firestore.collection('listings').where('calryPropertyId', '==', body.property.id).where('calryRoomTypeId', '==', room.id).get()
    if (!listingSnap.empty) {
      console.log('listing already exists')
      continue
    }
    const listing = {
      v: 4,
      userId: body.userId,
      active: false,
      respondingActive: false,
      kb: '',
      integrations: {
        calry: {
          pms: body.calryAccount.integrationDefinition.key,
        },
      },
      calryPropertyId: String(body.property.id),
      calryRoomTypeId: String(room.id),
      name: body.property.name || '',
      internalName: body.property.internalName || '',
      type: body.property.type || '',
      thumbnail: body.property.thumbnailUrl,
      address: body.property.address,
      currency: body.property.currency || '',
      timeZone: body.property.timeZone || '',
      rating: body.property.rating || null,
      roomTypes: body.property.roomTypes,
      pictures: room.pictures.map((p: any) => ({ src: p.url, description: p.description })),
      units: room.units || [],
      bedRoom: room.bedRoom,
      bathRoom: room.bathRoom,
      rooms: room.rooms,
      minimumNights: room.minimumNights,
      maximumNights: room.maximumNights,
      maxOccupancy: room.maxOccupancy,
      channels: room.channels,
      checkIn: room.checkIn,
      checkOut: room.checkOut,
      wifiDetails: room.wifiDetails,
      roomTypeDetails: room.roomTypeDetails,
      agreements: room.agreements,
      contact: room.contact,
      policy: room.policy,
      others: room.others,
      customFields: room.customFields,
      faqs: initFaqs(),
      amenities: calry.initOwnerrezAmenities(room.amenities),
    }
    let res
    try {
      res = await firestore.collection('listings').add(listing)
      console.log(`Successfully created yada listing with ID: ${res.id}`)
    }
    catch (e) {
      console.error('Error adding listing:', e)
    }

    await sleep(10000)

    for (const [key, value] of Object.entries(listing.amenities)) {
      console.log(`Amenity key: ${key}, value:`, value)
      try {
        await firestore.doc(`listings/${res.id}/content/${key}`).update({ amenityAvailable: value.amenityAvailable, amenityKnown: value.amenityKnown })
      }
      catch (e) {
        console.error(`Unable to update card listings/${res.id}/content/${key}`, JSON.stringify(e))
      }
    }
  }
  return 'Hello Nitro'
})

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
