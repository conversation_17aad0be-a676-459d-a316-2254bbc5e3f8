import { format } from 'date-fns'
import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'
import { sendTransactional } from '~/services/loops'
import { OpenPhone } from '~/services/openPhone'

function buildDefaultFields(contact: any) {
  const defaultFields = {
    firstName: contact.firstName ?? 'Unknown',
    lastName: contact.lastName ?? '',
    company: contact.company ?? '',
    phoneNumbers: [
      {
        name: 'primary',
        value: contact.phone,
      },
    ],
  }
  if (contact.email) {
    Object.assign(defaultFields, {
      emails: [
        {
          name: 'primary',
          value: contact.email,
        },
      ],
    })
  }
  return defaultFields
}

function formatTimestamp(ts: any) {
  let timestamp = ts
  if (typeof timestamp === 'string') {
    timestamp = Number.parseInt(timestamp, 10)
  }
  if (timestamp > 9999999999) {
    timestamp = timestamp / 1000
  }
  return new Date(timestamp * 1000)
}

function buildCustomFields({ stay, listing, fieldNameToKey }: any) {
  const customFields = []
  if (stay && stay.checkIn && fieldNameToKey.checkIn) {
    const checkInDate = formatTimestamp(stay.checkIn)
    customFields.push({
      key: fieldNameToKey.checkIn,
      value: format(checkInDate, 'MM/dd/yyyy'),
    })
  }
  if (stay && stay.checkOut && fieldNameToKey.checkOut) {
    const checkOutDate = formatTimestamp(stay.checkOut)
    customFields.push({
      key: fieldNameToKey.checkOut,
      value: format(checkOutDate, 'MM/dd/yyyy'),
    })
  }
  if (stay && typeof stay.totalPaid !== 'undefined' && fieldNameToKey.amountPaid) {
    customFields.push({
      key: fieldNameToKey.amountPaid,
      value: stay.totalPaid,
    })
  }
  if (listing && listing.name && fieldNameToKey.property) {
    customFields.push({
      key: fieldNameToKey.property,
      value: listing.name,
    })
  }
  // if (stay && stay.status && fieldNameToKey.reservationStatus) {
  //   customFields.push({
  //     key: fieldNameToKey.reservationStatus,
  //     value: stay.status,
  //   })
  // }
  return customFields
}

async function sendWelcomeMessage(openPhone: any, user: any, contact: any, contactId: string) {
  const companyName = user.companyName
  const welcomeMessage = companyName
    ? `Welcome from the team at ${companyName}! We're excited to have you stay with us soon. If you need anything, please reach out!`
    : 'Welcome! We\'re excited to have you stay with us soon. If you need anything, please reach out!'
  try {
    await openPhone.sendMessage(
      user.integrations.openphone.numbers[0].number,
      contact.phone.startsWith('+') ? contact.phone : `+${contact.phone}`,
      welcomeMessage,
    )
  }
  catch (messageError) {
    console.error('Failed to send welcome message', {
      contactId,
      error: messageError,
    })
  }
}

export default defineEventHandler(async (event) => {
  const body = await readBody(event)

  const user = await getAdminUser(body.userId)
  const contactSnap = await firestore.collection('contacts').doc(body.contactId).get()
  if (!contactSnap.exists) {
    console.error(`[Qstash Sync Contact OpenPhone] Contact not found: ${body.contactId}`)
    throw createError({
      statusCode: 404,
      message: `Contact with ID ${body.contactId} not found`,
    })
  }
  const contact: any = contactSnap.data()
  if (!contact.phone) {
    return 'success'
  }

  // If contact already has an openphoneId, change operation to update
  if (contact.openphoneId && body.operation === 'create') {
    body.operation = 'update'
  }

  console.log('[contact]', JSON.stringify(contact))

  let stay: any = null
  // Support both contact.stayIds and contact.stays
  let stayId: string | null = null
  if (Array.isArray(contact.stayIds) && contact.stayIds.length > 0 && typeof contact.stayIds[0] === 'string' && contact.stayIds[0]) {
    stayId = contact.stayIds[0]
  }
  else if (Array.isArray(contact.stays) && contact.stays.length > 0 && typeof contact.stays[0] === 'string' && contact.stays[0]) {
    stayId = contact.stays[0]
  }
  if (stayId) {
    const staySnap = await firestore.collection('stays').doc(stayId).get()
    stay = { ...staySnap.data(), id: staySnap.id }
  }

  console.log('[stay data]', JSON.stringify(stay))

  let listing: any
  if (stay && stay.listingId) {
    const listingSnap = await firestore.collection('listings').doc(stay.listingId).get()
    if (listingSnap.exists) {
      listing = { ...listingSnap.data(), id: listingSnap.id }
    }
  }

  console.log('[listing data]', JSON.stringify(listing))

  if (!listing.active) {
    console.warn('Listing is not active, skipping OpenPhone sync', {
      listingId: listing.id,
      contactId: body.contactId,
      operation: body.operation,
    })
    return 'success'
  }

  const openPhone = new OpenPhone(user.integrations.openphone.apiKey)

  if (body.operation === 'create') {
    const existingOpenPhoneContact = await openPhone.getOpenPhoneContactByExternalId(body.contactId)
    if (existingOpenPhoneContact && Array.isArray(existingOpenPhoneContact) && existingOpenPhoneContact.length > 0) {
      body.operation = 'update'
      const openPhoneId = existingOpenPhoneContact[0].id
      await firestore.collection('contacts').doc(body.contactId).update({
        openphoneId: openPhoneId,
      })
    }
  }

  const availableOpenPhoneCustomFields = await openPhone.getCustomFields()

  // Check for required custom fields
  const requiredCustomFields = ['checkIn', 'checkOut', 'property', 'amountPaid', 'guestScore', 'reservationStatus']
  const availableFieldNames = availableOpenPhoneCustomFields.map((field: any) => field.name)
  const missingCustomFields = requiredCustomFields.filter(field => !availableFieldNames.includes(field))

  if (missingCustomFields.length > 0) {
    const missingFieldsString = missingCustomFields.join(', ')
    await sendTransactional('TXN_API_CREATE_OPENPHONE_CUSTOM_FIELDS', user.email, {
      missingFields: missingFieldsString,
    })
  }

  // Create a mapping of field names to keys for easy lookup
  const fieldNameToKey = availableOpenPhoneCustomFields.reduce((acc: any, field: any) => {
    acc[field.name] = field.key
    return acc
  }, {})

  switch (body.operation) {
    case 'create': {
      const defaultFields = buildDefaultFields(contact)
      const customFields = buildCustomFields({ stay, listing, fieldNameToKey })
      const openphoneContactData = {
        defaultFields,
        customFields,
        externalId: body.contactId,
        source: 'yada-api',
        sourceUrl: `https://app.yada.ai/contacts/${body.contactId}`,
      }
      console.warn('Creating new OpenPhone contact', {
        contactId: body.contactId,
        payload: JSON.stringify(openphoneContactData),
        userId: body.userId,
      })
      try {
        const openphoneContact = await openPhone.createContact(openphoneContactData)
        if (openphoneContact && openphoneContact.id) {
          await firestore.collection('contacts').doc(body.contactId).update({
            openphoneId: openphoneContact.id,
          })
          await sendWelcomeMessage(openPhone, user, contact, body.contactId)
        }
      }
      catch (error) {
        console.error('Failed to sync contact with OpenPhone', {
          contactId: body.contactId,
          error,
        })
      }
      break
    }
    case 'update': {
      const defaultFields = buildDefaultFields(contact)
      const customFields = buildCustomFields({ stay, listing, fieldNameToKey })
      const openphoneContactData = {
        defaultFields,
        customFields,
        externalId: body.contactId,
        source: 'yada-api',
        sourceUrl: `https://app.yada.ai/contacts/${body.contactId}`,
      }
      console.warn('Updating OpenPhone contact', {
        contactId: body.contactId,
        payload: JSON.stringify(openphoneContactData),
        userId: body.userId,
      })
      await openPhone.updateContact(contact.openphoneId, openphoneContactData)
      break
    }
    default: {
      // Handle unknown operations
      console.error('Unknown operation for OpenPhone contact sync', {
        operation: body.operation,
        contactId: body.contactId,
      })

      break
    }
  }

  return 'success'
})
