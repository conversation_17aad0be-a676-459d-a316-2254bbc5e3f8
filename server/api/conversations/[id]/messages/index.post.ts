import { firestore } from '~/helpers/firebase'
import Calry from '~/services/calry'
import { getAdminUser } from '~/services/db'
import { Guesty } from '~/services/guesty'
import { Hostaway } from '~/services/hostaway'

export default defineEventHandler(async (event) => {
  const { id } = getRouterParams(event)
  const body = await readBody(event)

  console.log('[sendConversationMessage', id, body)

  const [user, conversationRef] = await Promise.all([
    getAdminUser(event.context.auth.uid),
    firestore.doc(`conversations/${id}`).get(),
  ])

  if (!conversationRef.exists) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Conversation not found',
      message: 'The specified conversation does not exist.',
    })
  }

  const conversationData = conversationRef.data()
  if (!conversationData) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Invalid conversation data',
      message: 'The conversation data is corrupted.',
    })
  }

  if (conversationData.calryId) {
    console.log('[sendMessageCalry] - conversation has calryId:', conversationData.calryId)

    const stayId = conversationData.stayId
    if (!stayId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No stay ID found',
        message: 'Conversation has no associated stay.',
      })
    }

    // Fetch stay and user integration in parallel
    const [stayRef, calryIntegrations] = await Promise.all([
      firestore.doc(`stays/${stayId}`).get(),
      Promise.resolve(user?.integrations?.calry),
    ])

    if (!stayRef.exists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Stay not found',
        message: 'The associated stay does not exist.',
      })
    }

    const stay = stayRef.data()
    const reservationId = stay?.calryId
    console.log('[sendMessageCalry] - got calry reservationId:', reservationId)
    if (!reservationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No Calry reservation ID found',
        message: 'Stay has no associated Calry reservation ID.',
      })
    }

    const firstAccountId = calryIntegrations ? Object.keys(calryIntegrations)[0] : null
    if (!firstAccountId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No Calry integration found',
        message: 'User has no configured Calry integrations.',
      })
    }

    const calry = new Calry(firstAccountId)
    const conversation = await calry.getConversation(conversationData.calryId)
    console.log('[getCalryConversation] - got calry conversation first message', JSON.stringify(conversation.messages[0].type))
    await calry.sendMessage(reservationId, conversationData.calryId, body.body, conversation.messages[0].type || 'CHANNEL')
  }
  if (conversationData.integrationChannel === 'hostaway') {
    console.log('[sendMessageHostaway] - conversation has integrationChannel: hostaway')
    const hostaway = new Hostaway(user.integrations.hostaway.accessToken, user.id)
    const heartbeat = await hostaway.heartbeat()
    console.log('[sendMessageHostaway] - heartbeat response:', heartbeat)
    if (!heartbeat) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Hostaway integration not available',
        message: 'Hostaway integration is not available or the access token is invalid. Please ensure that you have a valid Hostaway access token.',
      })
    }

    // Extract Hostaway conversation ID from conversationRef.id
    // Format: hostaway_I3xuFecITShxX4U0ilhihkDvswe2_11539407
    // We need the part after the last underscore
    const hostawayConversationId = conversationRef.id.split('_').pop()
    console.log('[sendMessageHostaway] - extracted conversation ID:', hostawayConversationId)

    if (!hostawayConversationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid conversation ID format',
        message: 'Unable to extract Hostaway conversation ID from the conversation reference.',
      })
    }

    await hostaway.sendMessage(hostawayConversationId, body.body, 'inbox')
    console.log('[sendMessageHostaway] - message sent successfully')
  }

  if (conversationData.integrationChannel === 'guesty') {
    console.log('[sendMessageGuesty] - conversation has integrationChannel: guesty')
    const guesty = new Guesty(user.integrations.guesty.accessToken, user.id)
    const heartbeat = await guesty.heartbeat()
    console.log('[sendMessageGuesty] - heartbeat response:', heartbeat)
    if (!heartbeat) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Guesty integration not available',
        message: 'Guesty integration is not available or the access token is invalid. Please ensure that you have a valid Guesty access token.',
      })
    }

    await guesty.sendMessage(
      conversationRef.id,
      body.body,
      'inbox',
    )
    console.log('[sendMessageGuesty] - message sent successfully')
  }

  return 'Hello Nitro'
})
