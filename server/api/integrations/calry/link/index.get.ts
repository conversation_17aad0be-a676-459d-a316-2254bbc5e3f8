import axios from 'axios'
import { getAdminUser } from '~/services/db'
import { CalryPmsIntegrationDefinitionKeys } from '~/shared'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const pmsId = query.pmsId as string // Cast pmsId to string

  const user = await getAdminUser(event.context.auth.uid)

  if (!user || user.type !== 'admin') {
    throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })
  }

  // Validate pmsId and get integration definition
  if (!pmsId || !(pmsId in CalryPmsIntegrationDefinitionKeys)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid or missing pmsId query parameter.',
    })
  }

  // Assert pmsId as a key of the enum/object type after the check
  const integrationDefinition = CalryPmsIntegrationDefinitionKeys[pmsId as keyof typeof CalryPmsIntegrationDefinitionKeys]

  const linkBody = {
    integrationDefinitionKey: integrationDefinition.key,
    expiresIn: 3600,
    linkName: integrationDefinition.name,
    workspaceId: process.env.CALRY_WORKSPACE_ID,
    accountIdentifier: user.userId,
    redirectUrl: 'https://app.yada.ai',
    preferOAuth: true,
  }

  console.log('Constructed linkBody:', linkBody) // Example log

  const calryApiKey = process.env.CALRY_API_KEY
  if (!calryApiKey) {
    throw createError({ statusCode: 500, statusMessage: 'Calry API key is not configured.' })
  }

  const apiUrl = 'https://dev.calry.app/api/v1/link'
  const headers = {
    'Authorization': `Bearer ${calryApiKey}`,
    'Content-Type': 'application/json',
  }

  let link
  try {
    const response = await axios.post(apiUrl, linkBody, { headers })
    console.log('Calry API Response:', response.data)

    link = response.data
  }
  catch (error) {
    console.error('Error calling Calry API:', error.response?.data || error.message)
    throw createError({
      statusCode: error.response?.status || 500,
      statusMessage: `Failed to create Calry link: ${error.response?.data?.message || error.message}`,
    })
  }

  const linkId = link.linkId

  if (!linkId) {
    console.error('Link ID not found in Calry API response', response.data)
    throw createError({ statusCode: 500, statusMessage: 'Failed to retrieve link ID from Calry API.' })
  }
  return { linkId }
})
