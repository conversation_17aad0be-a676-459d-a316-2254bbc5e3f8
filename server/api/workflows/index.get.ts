import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const adminUser: any = await getAdminUser(event.context.auth.uid)

  try {
    const workflowsSnap = await firestore.collection('workflows').where('userId', '==', adminUser.userId).get()
    return workflowsSnap.docs.map(d => ({ ...d.data(), id: d.id }))
  }
  catch (e) {
    console.error('unable to retrieve workflows: ', JSON.stringify(e))
    throw createError({ statusCode: 500, message: 'unable to get workflows' })
  }
})
