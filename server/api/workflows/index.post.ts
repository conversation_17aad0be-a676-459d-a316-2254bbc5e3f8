import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

// Create a workflow in the yada database
export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)
  if (!user) {
    throw createError({ statusCode: 401, message: 'Unauthorized' })
  }

  const body = await readBody(event)

  try {
    const docRef = await firestore.collection('workflows').add({ ...body, userId: user.id })
    return { success: true, id: docRef.id }
  } catch (e) {
    console.error('unable to create workflow: ', JSON.stringify(e))
    throw createError({ statusCode: 500, message: 'Unable to create workflow' })
  }
})
