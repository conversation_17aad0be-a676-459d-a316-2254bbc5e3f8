import { upsert } from '~/services/ai/indexing'
import { getListing } from '~/services/db'

export default defineEventHandler(async (event) => {
  const id: string | undefined = event.context.params?.id
  if (typeof id !== 'string') {
    throw createError({ statusCode: 400, statusMessage: 'Invalid listing ID' })
  }
  const listing: any = await getListing(id)

  if (listing.v === 4) {
    console.log('training a new version of a listing')

    // Process amenities and FAQs in parallel batches
    const amenityPromises = processAmenities(id, listing)
    const faqPromises = processFaqs(id, listing)

    // Combine all promises
    const allPromises = [...amenityPromises, ...faqPromises]

    // Wait for all upsert operations to complete
    await Promise.all(allPromises)
    console.log(`Upserted ${allPromises.length} cards for listing ${id}`)
  }

  return { success: true, message: 'Training completed successfully' }
})

// Helper function to process amenities
function processAmenities(id: string, listing: any) {
  const promises = []
  const amenities = listing.amenities || {}

  for (const key of Object.keys(amenities)) {
    if (!key.startsWith('amenity_'))
      continue

    const amenity = amenities[key]
    if (!amenity || amenity.amenityKnown !== true)
      continue

    const title = amenity.title || ''
    const availabilityText = amenity.amenityAvailable
      ? ` - Yes there is ${title} at the rental.`
      : ` - No there is not ${title} at the rental`

    const enhancedText = `${title}${availabilityText} ${amenity.text || ''}`.trim()

    if (enhancedText) {
      promises.push(
        upsert('listing', `${id}_${key}`, {
          listingId: id,
          cardId: key,
          private: amenity.private || false,
          userId: listing.userId,
        }, enhancedText),
      )
    }
  }

  return promises
}

// Helper function to process FAQs
function processFaqs(id: string, listing: any) {
  const promises = []
  const faqs = listing.faqs || {}

  for (const key of Object.keys(faqs)) {
    if (!key.startsWith('faq_'))
      continue

    const faq = faqs[key]
    if (!faq || !faq.text)
      continue

    promises.push(
      upsert('listing', `${id}_${key}`, {
        listingId: id,
        cardId: key,
        private: faq.private || false,
        userId: listing.userId,
      }, faq.text),
    )
  }

  return promises
}
