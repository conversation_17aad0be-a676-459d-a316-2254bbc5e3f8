import { firestore } from '~/helpers/firebase'
import { upsert } from '~/services/ai/indexing'
import { getListing } from '~/services/db'

export default defineEventHandler(async (event: any) => {
  const { id, cardId } = event.context.params
  const body = await readBody(event)

  if (!id || !cardId || !body) {
    return {
      statusCode: 400,
      error: 'Missing required parameter(s): listingId, cardId, or body.',
    }
  }

  // Extract the update payload from the structured payload
  // If the body has an 'update' property, use it; otherwise, use the entire body
  const updatePayload = body.update || body

  const listing: any = await getListing(id)

  if (listing.v !== 4) {
    try {
      await firestore.doc(`listings/${id}/content/${cardId}`).update(updatePayload)
    }
    catch (e) {
      console.error('Error updating listing:', e)
      throw createError({ statusCode: 500, statusMessage: 'Unable to update listing' })
    }
  }
  else {
    // Determine the type based on cardId prefix
    let type = 'customField' // default type

    // Use a switch statement with true to evaluate conditions
    switch (true) {
      case cardId.startsWith('faq_'):
        type = 'faqs'
        break
      case cardId.startsWith('amenity_'):
        type = 'amenities'
        break
      // default case is already handled by initializing type as 'customField'
    }

    // Asynchronously iterate through all keys in the updatePayload
    await Promise.all(Object.keys(updatePayload).map(async (key) => {
      const fieldPath = `${type}.${cardId}.${key}`
      await firestore.doc(`listings/${id}`).update(fieldPath, updatePayload[key])
      await upsert('listing', `${id}_${cardId}`, { listingId: id, cardId, private: body.card.private, userId: listing.userId }, body.card.text)
    }))

    // Add the type to the response
    updatePayload.type = type
  }
  return { success: true, id, cardId, payloadReceived: body }
})
