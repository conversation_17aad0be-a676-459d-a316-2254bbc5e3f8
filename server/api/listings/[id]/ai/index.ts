import { createOpenAI } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'
import { detectRoute, query } from '~/services/ai'
import { getListing } from '~/services/db'
import { getAccuracy, getRelevance } from '~/services/ai/evaluations'

export default defineLazyEventHandler(async () => {
  const apiKey = useRuntimeConfig().openaiApiKey
  if (!apiKey)
    throw new Error('Missing OpenAI API key')
  const openai = createOpenAI({
    apiKey,
  })

  return defineEventHandler(async (event: any) => {
    const { messages } = await readBody(event)
    const id: string | undefined = event.context.params?.id
    // const listing = await getListing(id)

    console.log(messages[messages.length - 1].content)

    const route = await detectRoute(messages[messages.length - 1].content, messages)

    console.log('route: ', route)

    let result
    switch (route) {
      case 'smalltalk': {
        result = streamText({
          model: openai('gpt-4o-mini'),
          messages,
          system: `You are a helpful assistant. Your job is to respond to chitchat type messages. Please do so succinctly and in the language of the incoming message.`,
        })
        break
      }
      case 'faq': {
        result = streamText({
          model: openai('gpt-4.1-mini'),
          messages,
          system: `You are a friendly Airbnb host assistant. Follow this exact workflow:

1. FIRST - REQUIRED: Call the getInformation tool to search the knowledge base
2. SECOND - REQUIRED: Provide your complete, helpful response to the user in a warm, conversational tone (this should be your main response text, not just a tool parameter)
3. THIRD - REQUIRED: Call the getCitations tool to record the sources you used (but don't include citations in your visible response),
4. FINALLY - REQUIRED: call the getScores tool (but don't include scores in your visible response),

Always provide a direct text response to the user's question. Never show raw tool output to the user. Always call ALL the required tools.`,
          tools: {
            getInformation: tool({
              description: `REQUIRED: Search your knowledge base for information about the rental property, amenities, local area, house rules, check-in/out procedures, or any guest-related questions. Use this when you need specific information to answer the user's question.`,
              parameters: z.object({
                question: z.string().describe('the guest question to search for in the knowledge base'),
              }),
              execute: async ({ question }) => {
                const result = await query('listing', `listingId = '${id}'`, question)
                return result
              },
            }),
            getCitations: tool({
              description: `REQUIRED: Record citations for sources used in your response. Call this after you've provided your main text response to the user. This is for internal tracking only - don't include citations in your visible response.`,
              parameters: z.object({
                userResponse: z.string().describe('The complete response you provided to the user'),
                userQuestion: z.string().describe('The original question asked by the user'),
                relevantSources: z.array(z.object({
                  id: z.string(),
                  text: z.string(),
                })).describe('Only the specific pieces of information from retrieved data that were actually used in your response'),
              }),
              execute: async ({ relevantSources }) => {
                if (!relevantSources || relevantSources.length === 0) {
                  return [] // No citations needed if no sources were used
                }
                // Deduplicate sources by id
                const uniqueSources = relevantSources.filter((source, index, self) =>
                  index === self.findIndex(s => s.id === source.id),
                )

                // Helper function to clean up IDs
                const cleanId = (id: string) => {
                  if (id.startsWith('hostaway')) {
                    // Remove everything up to and including the third underscore
                    const parts = id.split('_')
                    return parts.slice(3).join('_')
                  }
                  else {
                    // Remove everything up to and including the first underscore
                    const firstUnderscoreIndex = id.indexOf('_')
                    return firstUnderscoreIndex !== -1 ? id.substring(firstUnderscoreIndex + 1) : id
                  }
                }

                const citations = uniqueSources.map(source => ({
                  id: cleanId(source.id),
                  text: source.text,
                }))
                return citations
              },
            }),
            getScores: tool({
              description: `FINALLY, REQUIRED: Evaluate the quality of your response by calculating accuracy and relevance scores. Call this after providing your response to the user.`,
              parameters: z.object({
                userQuestion: z.string().describe('The original question asked by the user'),
                userResponse: z.string().describe('The complete response you provided to the user'),
                retrievedContext: z.string().describe('The context/information retrieved from the knowledge base that was used to answer the question'),
              }),
              execute: async ({ userQuestion, userResponse, retrievedContext }) => {
                try {
                  // Get accuracy and relevance scores concurrently
                  const [accuracyResult, relevanceResult] = await Promise.all([
                    getAccuracy(userQuestion, userResponse, retrievedContext),
                    getRelevance(userQuestion, userResponse, retrievedContext)
                  ])

                  return {
                    accuracy: accuracyResult,
                    relevance: relevanceResult
                  }
                } catch (error) {
                  console.error('Error calculating scores:', error)
                  return {
                    accuracy: { grounded: null, explanation: 'Error calculating accuracy score' },
                    relevance: { relevant: null, explanation: 'Error calculating relevance score' },
                  }
                }
              },
            }),
          },
          maxSteps: 4,
        })
        break
      }
      default: {
        result = streamText({
          model: openai('gpt-4o-mini'),
          messages,
          system: `You are a helpful assistant. Check your knowledge base before answering any questions. Only respond to questions using information from tool calls. If no relevant information is found in the tool calls, respond, "Sorry, I don't know."`,
        })
        break
      }
    }
    return result.toDataStreamResponse()
  })
})
