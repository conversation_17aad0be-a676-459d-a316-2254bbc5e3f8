import { firestore } from '~/helpers/firebase'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const id: string | undefined = event.context.params?.id
  if (typeof id !== 'string') {
    throw createError({ statusCode: 400, statusMessage: 'Invalid listing ID' })
  }
  try {
    await firestore.doc(`listings/${id}`).update(body)
  }
  catch (e) {
    console.error('Error updating listing:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to update listing' })
  }
  return 'success'
})
