import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'
import { listingAmenities, listingFaqs } from '~/shared'

export default defineEventHandler(async (event: any) => {
  const user = await getAdminUser(event.context.auth.uid)

  let listing: any
  try {
    const listingSnap = await firestore.doc(`listings/${event.context.params.id}`).get()
    if (!listingSnap.exists) {
      throw createError({ statusCode: 404, statusMessage: 'Unable to find listing' })
    }
    listing = { ...listingSnap.data(), id: listingSnap.id }
  }
  catch (e) {
    throw createError({ statusCode: 404, statusMessage: 'Unable to find listing' })
  }

  if (listing.userId !== user.id) {
    throw createError({ statusCode: 403, statusMessage: 'You are not authorized to access this listing' })
  }

  if (listing.v !== 4) {
    try {
      const contentSnap = await firestore.collection(`listings/${event.context.params.id}/content`).get()
      if (contentSnap.empty) {
        throw createError({ statusCode: 404, statusMessage: 'Unable to find listing content' })
      }
      listing.content = contentSnap.docs.map(doc => ({ ...doc.data(), id: doc.id }))
    }
    catch (e) {
      throw createError({ statusCode: 404, statusMessage: 'Unable to find listing content' })
    }
  } else {
    // For v4 listings, transform amenities and faqs objects into arrays
    const amenitiesArray = []
    const faqsArray = []

    // Transform amenities object to array
    if (listing.amenities) {
      for (const key in listing.amenities) {
        if (key.startsWith('amenity_')) {
          const amenity = listing.amenities[key]
          amenitiesArray.push({
            ...amenity,
            id: key,
            template: 'amenity'
          })
        }
      }
    }

    // Transform faqs object to array
    if (listing.faqs) {
      for (const key in listing.faqs) {
        if (key.startsWith('faq_')) {
          const faq = listing.faqs[key]
          faqsArray.push({
            ...faq,
            id: key,
            template: 'faq'
          })
        }
      }
    }

    // Combine arrays to create content array
    listing.content = [...amenitiesArray, ...faqsArray]
  }

  const defaults = [...listingFaqs, ...listingAmenities]
  const defaultsLookup = Object.fromEntries(defaults.map(item => [item.id, item]))

  // Transform listing.content using the provided logic
  listing.content = (listing.content || [])
    .filter((doc: any) => !doc.archived)
    .map((doc: any) => {
      const data = doc
      const defaultItem: any = defaultsLookup[data.internalId]
      const isAmenity = data.template === 'amenity'

      return {
        oldText: data.text,
        ...data,
        id: doc.id,
        internalId: isAmenity ? data.internalId : doc.id,
        tags: defaultItem?.tags || (data.tags?.length > 0 ? data.tags : ['misc']),
        title: defaultItem?.name || data.title,
        priority: defaultItem?.priority || 1,
        autoPopulate: defaultItem?.autoPopulate || false,
        sampleText: defaultItem?.sampleText || '',
        status: data.text?.length > 0 ? 1 : !data.amenityKnown ? 3 : 2,
      }
    })

  return listing
})
