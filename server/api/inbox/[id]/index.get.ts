import { firestore } from '~/helpers/firebase'

export default defineEventHandler(async (event) => {
  const id = event.context.params?.id
  if (typeof id !== 'string') {
    throw createError({ statusCode: 400, statusMessage: 'Invalid conversation ID', message: 'The conversation ID must be a string.' })
  }

  const convoRes = await firestore.collection('conversations').doc(id).get()
  if (convoRes.exists) {
    return { ...convoRes.data(), id: convoRes.id, type: 'conversation' }
  }
  const chatRes = await firestore.collection('chats').doc(id).get()
  if (chatRes.exists) {
    return { ...chatRes.data(), id: chatRes.id, type: 'chat' }
  }
  throw createError({ statusCode: 404, statusMessage: 'Conversation not found', message: 'The requested conversation does not exist or has been deleted.' })
})
