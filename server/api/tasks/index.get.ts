import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)

  const { listingId, conversationId } = query

  const user = await getAdminUser(event.context.auth.uid)
  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    })
  }

  // Fetch tasks, filtering by specific IDs if provided, otherwise by userId and recent date
  try {
    let tasksRef: any = firestore.collection('tasks')

    if (listingId) {
      tasksRef = tasksRef.where('userId', '==', user.id).where('listing.id', '==', listingId).orderBy('createdAt', 'desc')
    }
    else if (conversationId) {
      tasksRef = tasksRef.where('userId', '==', user.id).where('conversation.id', '==', conversationId).orderBy('createdAt', 'desc')
    }
    else {
      // Only filter by userId and createdAt when no specific IDs are provided
      tasksRef = tasksRef.where('userId', '==', user.id).orderBy('createdAt', 'desc')
      // tasksRef = tasksRef.where('createdAt', '>=', Date.now() - 60 * 24 * 60 * 60 * 1000)
    }
    try {
      const snapshot = await tasksRef.get()
      const tasks = snapshot.docs.map((doc: any) => {
        const { asignees, ...taskData } = doc.data()
        return { ...taskData, id: doc.id }
      })
      return tasks
    }
    catch (e) {
      console.error('Error fetching tasks', e)
      throw createError({
        statusCode: 404,
        statusMessage: 'Unable to find tasks',
      })
    }
  }
  catch (e) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Unable to find tasks',
    })
  }
})
