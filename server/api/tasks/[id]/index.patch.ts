import { firestore } from '~/helpers/firebase'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { id } = event.context.params
  try {
    await firestore.doc(`tasks/${id}`).update(body)
  }
  catch (e) {
    console.error('Error updating task:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to update task' })
  }
  return 'Hello Nitro'
})
