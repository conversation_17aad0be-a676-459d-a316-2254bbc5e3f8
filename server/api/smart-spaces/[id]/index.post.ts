import { put } from '@vercel/blob'
import { getAdminUser, getListing } from '~/services/db'
import { sendTransactional } from '~/services/loops'
import { listingAmenities } from '~/shared'

export default defineEventHandler(async (event) => {
  try {
    const id: string | undefined = event.context.params?.id
    if (typeof id !== 'string') {
      throw createError({ statusCode: 400, statusMessage: 'Invalid listing ID' })
    }

    let listing: any
    try {
      listing = await getListing(id)
      if (!listing) {
        throw createError({ statusCode: 404, statusMessage: 'Listing not found', message: 'The requested listing does not exist or has been deleted.' })
      }
      // if (!listing.active) {
      //   throw createError({ statusCode: 403, statusMessage: 'Listing is not active', message: 'This listing is not active. Please activate this listing to download smart spaces.' })
      // }
    }
    catch (error: any) {
      console.error('Error fetching listing', { error, id })
      throw createError({
        statusCode: error.statusCode || 500,
        statusMessage: error.statusMessage || 'Failed to fetch listing data',
      })
    }

    let user
    try {
      user = await getAdminUser(event.context.auth.uid)
      if (!user) {
        throw createError({ statusCode: 404, statusMessage: 'User not found' })
      }
    }
    catch (error: any) {
      console.error('Error fetching admin user', { error, uid: event.context.auth.uid })
      throw createError({
        statusCode: error.statusCode || 500,
        statusMessage: error.statusMessage || 'Failed to fetch user data',
      })
    }

    const body = await readBody(event)
    if (!body || !body.type) {
      throw createError({ statusCode: 400, statusMessage: 'Request body or type is missing' })
    }

    switch (body.type) {
      case 'cover': {
        try {
          const listingData: ListingData = {
            id: listing.id,
            name: listing.name,
            thumbnail: listing.thumbnail,
            pictures: listing.pictures,
          }

          // Generate the cover card
          let pdfBytes
          try {
            pdfBytes = await generateCoverCard(listingData, user)
          }
          catch (error) {
            console.error('Failed to generate cover card', { error, listingId: listing.id })
            throw createError({ statusCode: 500, statusMessage: 'Failed to generate cover card' })
          }

          // Save PDF directly using Vercel Blob Service
          const fileName = `smart-spaces/${id}/cover-${Date.now()}.pdf`

          let blob
          try {
            blob = await put(fileName, pdfBytes, {
              access: 'public',
              contentType: 'application/pdf',
            })
          }
          catch (error) {
            console.error('Failed to upload PDF to Vercel Blob', { error, fileName })
            throw createError({ statusCode: 500, statusMessage: 'Failed to upload PDF to storage' })
          }

          const url = blob.url
          if (!url) {
            console.error('Failed to get valid URL from storage')
            throw createError({ statusCode: 500, statusMessage: 'Failed to get valid URL from storage' })
          }

          try {
            await sendTransactional(
              'TXN_APP_SMARTSPACE_DOWNLOADCOVER_SINGLELISTING',
              event.context.auth.email,
              {
                listingName: listing.name,
                fileLink: url,
              },
            )
          }
          catch (error) {
            // Log but don't fail the request if email sending fails
            console.error('Failed to send transactional email', {
              error,
              template: 'TXN_APP_SMARTSPACE_DOWNLOADCOVER_SINGLELISTING',
              email: event.context.auth.email,
            })
            // Continue execution - don't throw error here as the PDF was successfully generated
          }

          return url
        }
        catch (error: any) {
          // This catch block handles any errors not caught by the specific try-catch blocks above
          console.error('Error processing cover card request', { error, listingId: listing.id })
          throw error.statusCode
            ? error
            : createError({
              statusCode: 500,
              statusMessage: error.message || 'Failed to process cover card request',
            })
        }
      }
      case 'amenities': {
        try {
          if (!body.amenities || !Array.isArray(body.amenities) || body.amenities.length === 0) {
            throw createError({ statusCode: 400, statusMessage: 'Amenities array is required and cannot be empty' })
          }

          // Map amenities to cards
          const cards: CardData[] = body.amenities.map((amenityId: string) => {
            if (typeof amenityId !== 'string') {
              throw createError({ statusCode: 400, statusMessage: 'Each amenity ID must be a string' })
            }
            // Find the amenity in listingAmenities
            const amenityInfo = listingAmenities.find(a => a.id === amenityId)
            const name = amenityInfo?.name || amenityId

            return {
              title: name,
              subtitle: 'Scan for Details',
              qrContent: `https://guide.yada.ai/${id}?src=qr&sub_src=amenity&card_id=${amenityId}`,
            }
          })

          let pdfBytes
          try {
            pdfBytes = await fillTemplate(cards, listing.name)
            if (!pdfBytes) {
              throw createError({ statusCode: 500, statusMessage: 'Failed to generate PDF' })
            }
          }
          catch (error) {
            console.error('Failed to fill template for amenities', { error, listingId: listing.id })
            throw createError({ statusCode: 500, statusMessage: 'Failed to generate amenities PDF' })
          }

          const fileName = `smart-spaces/${id}/amenities-${Date.now()}.pdf`

          let blob
          try {
            blob = await put(fileName, pdfBytes, {
              access: 'public',
              contentType: 'application/pdf',
            })
          }
          catch (error) {
            console.error('Failed to upload amenities PDF to Vercel Blob', { error, fileName })
            throw createError({ statusCode: 500, statusMessage: 'Failed to upload amenities PDF to storage' })
          }

          const url = blob.url
          if (!url) {
            throw createError({ statusCode: 500, statusMessage: 'Failed to get valid URL from storage' })
          }

          try {
            await sendTransactional(
              'TXN_APP_SMARTSPACE_DOWNLOADAMENITIES_SINGLELISTING',
              event.context.auth.email,
              {
                listingName: listing.name,
                fileLink: url,
              },
            )
          }
          catch (error) {
            // Log but don't fail the request if email sending fails
            console.error('Failed to send transactional email', {
              error,
              template: 'TXN_APP_SMARTSPACE_DOWNLOADAMENITIES_SINGLELISTING',
              email: event.context.auth.email,
            })
            // Continue execution - don't throw error here as the PDF was successfully generated
          }

          return url
        }
        catch (error: any) {
          // This catch block handles any errors not caught by the specific try-catch blocks above
          console.error('Error processing amenities request', { error, listingId: listing.id })
          throw error.statusCode
            ? error
            : createError({
              statusCode: 500,
              statusMessage: error.message || 'Failed to process amenities request',
            })
        }
      }
      default:
        throw createError({ statusCode: 400, statusMessage: 'A valid smart spaces type is required (cover or amenities)' })
    }
  }
  catch (error: any) {
    // Global error handler for any uncaught errors
    console.error('Unhandled error in smart-spaces endpoint', {
      error,
      path: `/smart-spaces/${event.context.params?.id}`,
      method: 'POST',
      body: await readBody(event).catch(() => 'Could not read body'),
    })

    // Return appropriate error response
    if (error.statusCode) {
      // If it's already a Nuxt error with statusCode, pass it through
      throw error
    }
    else {
      // Otherwise create a generic 500 error
      throw createError({
        statusCode: 500,
        statusMessage: error.message || 'An unexpected error occurred',
      })
    }
  }
})
