import { sendTransactional } from '~/services/loops'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { email, pdfUrl, pdfType, listingNName } = body

    if (!email || !pdfUrl || !pdfType || !listingNName) {
      throw createError({ statusCode: 400, statusMessage: 'Missing required fields' })
    }

    // Choose template based on pdfType
    let template = ''
    if (pdfType === 'cover') {
      template = 'TXN_APP_SMARTSPACE_DOWNLOADCOVER_SINGLELISTING'
    }
    else if (pdfType === 'amenities') {
      template = 'TXN_APP_SMARTSPACE_DOWNLOADAMENITIES_SINGLELISTING'
    }
    else {
      throw createError({ statusCode: 400, statusMessage: 'Invalid pdfType' })
    }
    try {
      await sendTransactional(
        template,
        email,
        {
          listingName: listingNName,
          fileLink: pdfUrl,
        },
      )
    }
    catch (error) {
      // Log but don't fail the request if email sending fails
      console.error('Failed to send transactional email', {
        error,
        template,
        email,
      })
      // Optionally: return a 202 Accepted with a warning
      return { status: 'warning', message: 'PDF generated but email failed to send.' }
    }

    return { status: 'success' }
  }
  catch (error: any) {
    // Global error handler
    console.error('Error in sendPDFmail endpoint', { error })
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to send PDF via email',
    })
  }
})
