import process from 'node:process'
import axios from 'axios'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const formId = body.formId
  const hutk = body.hutk
  const payload = body.payload

  const data = {
    fields: [
      {
        objectTypeId: '0-1',
        name: 'email',
        value: payload.email,
      },
    ],
    context: {
      hutk,
    },
  }
  const portalId = process.env.HUBS_PORTAL_ID
  const token = process.env.HUBS_PRIVATE_ACCESS_TOKEN
  const url = `https://api.hsforms.com/submissions/v3/integration/submit/${portalId}/${formId}`

  try {
    const response = await axios.post(url, data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })
    return response.data
  }
  catch (error: unknown) {
    let message = 'Unknown error'
    if (typeof error === 'object' && error !== null) {
      if ('response' in error && error.response && typeof error.response === 'object' && 'data' in error.response) {
        message = (error.response as any).data
      }
      else if ('message' in error) {
        message = (error as any).message
      }
    }
    console.error('HubSpot form submission error:', message)
    return {
      error: true,
      message,
    }
  }
})
