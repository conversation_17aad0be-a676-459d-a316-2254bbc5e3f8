import { firestore } from '~/helpers/firebase'
import { getUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const { id } = event.context.params
  const body = await readBody(event)

  const user: any = await getUser(event.context.auth.uid)
  if (user.type !== 'admin') {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    })
  }

  try {
    await firestore.doc(`users/${id}`).update(body)
  }
  catch (e) {
    console.error('Error updating user:', e)
    throw createError({
      statusCode: 500,
      statusMessage: 'Unable to update user',
    })
  }
  return 'success'
})
