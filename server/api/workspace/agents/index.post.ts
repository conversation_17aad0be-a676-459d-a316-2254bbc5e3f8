import { FieldValue } from 'firebase-admin/firestore'
import generatePassword from 'password-generator'
import { auth, firestore } from '~/helpers/firebase'
import { getUser } from '~/services/db'

import { sendTransactional } from '~/services/loops'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const user: any = await getUser(event.context.auth.uid)
  if (user.type !== 'admin') {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    })
  }

  const password = generatePassword()

  let uid

  try {
    const res = await auth.createUser({ email: body.email, password, emailVerified: false })
    uid = res.uid
  }
  catch (e: any) {
    console.error('Error creating user:', e)
    throw createError({ statusCode: 500, statusMessage: e.message })
  }
  try {
    await firestore.doc(`users/${user.id}`).update({ agents: FieldValue.arrayUnion(uid) })
  }
  catch (error) {
    console.error('unable to update admin user', { error })
    throw createError({ statusCode: 500, statusMessage: 'Unable to complete agent creation process' })
  }

  await sleep(5000)

  try {
    await firestore.doc(`users/${uid}`).update({ type: 'agent', adminId: user.id, active: true, email: body.email })
  }
  catch (e) {
    console.error('Error updating agent user:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to complete agent creation process' })
  }

  await sendTransactional('TXN_API_NEWAGENT_ADMINCONFIRM', user.email, { agentEmail: body.email, temporaryPassword: password })
  await sendTransactional('TXN_API_NEWAGENT_AGENTCONFIRM', body.email, { adminEmail: user.email, agentEmail: body.email, temporaryPassword: password })
  return 'success'
})

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
