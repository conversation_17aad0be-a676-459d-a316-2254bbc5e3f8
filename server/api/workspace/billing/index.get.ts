import { loadCustomer, loadInvoices, loadPaymentMethod, loadSubscription } from '~/services/chargebee'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)
  if (process.env.NODE_ENV === 'production') {
    user.billing.customer.id = 'Azz35zUfArtq42Wbi'
  }
  const [subscription, invoices, customer, paymentMethod] = await Promise.all([
    loadSubscription(user),
    loadInvoices(user),
    loadCustomer(user),
    loadPaymentMethod(user),
  ])

  return {
    subscription,
    invoices,
    customer,
    paymentMethod,
  }
})
