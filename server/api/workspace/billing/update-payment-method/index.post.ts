import { firestore } from '~/helpers/firebase'
import { createCustomer, createPaymentMethod, loadCustomer } from '~/services/chargebee'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  let user = await getAdminUser(event.context.auth.uid)

  if (!user?.billing?.customer?.id) {
    const customer = await createCustomer(
      body.firstName,
      body.lastName,
      user.email,
      body.address,
      body.city,
      body.state,
      body.postalCode,
      body.country || 'US',
    )
    if (!customer) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Unable to create customer',
      })
    }
    console.info('created customer', { c: customer })
    try {
      await firestore.doc(`users/${user.id}`).update({
        'billing.customer': customer,
      })
    }
    catch (e) {
      console.error('Error updating user:', e)
      throw createError({ statusCode: 500, statusMessage: 'Unable to update user' })
    }
  }

  user = await getAdminUser(event.context.auth.uid)

  console.info('Got user after all payment method stuff', user)

  if (!body.cbToken) {
    createError({ statusCode: 400, statusMessage: 'Missing payment token' })
  }

  if (!user?.billing?.customer?.id) {
    createError({ statusCode: 500, statusMessage: 'No customer record found' })
  }

  const customer = await loadCustomer(user)

  console.info('loaded customer', { c: customer })

  const paymentMethod = await createPaymentMethod(user.billing.customer.id, body.cbToken)

  console.info('created payment method', { p: paymentMethod })

  if (!paymentMethod) {
    createError({ statusCode: 500, statusMessage: 'Unable to save payment method' })
  }

  console.info('saved payment method', { p: paymentMethod })
  return paymentMethod
})
