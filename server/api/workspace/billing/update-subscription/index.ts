import { loadSubscription, updateSubscription } from '~/services/chargebee'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const user = await getAdminUser(event.context.auth.uid)
  console.info('body', body)

  const subscription = await loadSubscription(user)
  if (!subscription || body.numListings <= subscription.subscription_items[0].quantity) {
    createError({ statusCode: 500, statusMessage: 'Unable to update subscription' })
  }

  console.log('lets continue updating this subscription')

  const sub = await updateSubscription(subscription, body.numListings)
  if (!sub) {
    createError({ statusCode: 500, statusMessage: 'Unable to update subscription' })
  }
})
