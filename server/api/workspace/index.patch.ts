import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const user = await getAdminUser(event.context.auth.uid)
  try {
    await firestore.doc(`users/${user.id}`).update(body)
  }
  catch (e) {
    console.error('Error updating workspace:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to update workspace' })
  }
  return 'Hello Nitro'
})
