import { loadSubscription } from '~/services/chargebee'
import { getAdminUser, getAgents } from '~/services/db'

interface Personalize {
  greeting: boolean
  greetingText: string
  signature: boolean
  signatureText: string
}

interface WorkspaceConfig {
  id: string
  plan?: string
  aiVersion: number
  integrations: any
  powerups: Record<string, any>
  buyTime: Record<string, any>
  aiConfidence: number
  accuracy: number
  chatbotSettings: Record<string, any>
  smallTalk: Record<string, any>
  timeActive: boolean
  aiStartTime: Date | null
  aiEndTime: Date | null
  backoffMinutes: number
  timezone: string
  personalize: Personalize
  companyInfo: {
    name: string
    bookingSiteUrl: string
    aboutUs: string
    logo: string
  }
  contactInfo: {
    email: string
    phone: string
    whatsapp: string
  }
  billing?: any
  primaryColor: string
  secondaryColor: string
  companyName: string
  bookingSiteUrl: string
  aboutUs: string
  guestContactEmail: string
  guestContactPhone: string
  guestContactWhatsapp: string
  logo: string
  users: any[]
  approveAiMessage: boolean
  allTimes: any
  offer: string
}

const DEFAULT_VALUES = {
  aiVersion: 1,
  powerups: {},
  buyTime: {
    active: false,
    guidebook: false,
    time: 7,
    message: 'Hey we\'re not online right now, but we will get back to you as soon as possible.',

  },
  aiConfidence: 0.8,
  accuracy: 0.8,
  chatbotSettings: {},
  smallTalk: {},
  timeActive: false,
  aiStartTime: null,
  aiEndTime: null,
  backoffMinutes: 0,
  timezone: 'America/New_York',
  personalize: {
    greeting: false,
    greetingText: 'Hello {guestName},',
    signature: false,
    signatureText: '',
  },
  theme: {
    primaryColor: '#3e6db5',
    secondaryColor: '#22becb',
  },
  allTimes: {
    numBuyTimes: 0,
    numContacts: 0,
    numTasks: 0,
    numWebsiteViews: 0,
    numGuidebookViews: 0,
  },
  offer: '',
} as const

export default defineEventHandler(async (event) => {
  const adminUser = await getAdminUser(event.context.auth.uid)

  const workspace: WorkspaceConfig = {
    id: adminUser.id,
    plan: adminUser?.billing?.subscription?.type,
    aiVersion: adminUser.aiVersion || DEFAULT_VALUES.aiVersion,
    integrations: adminUser.integrations || {},
    powerups: adminUser.powerups || DEFAULT_VALUES.powerups,
    buyTime: adminUser.buyTime || DEFAULT_VALUES.buyTime,
    aiConfidence: adminUser.aiConfidence || DEFAULT_VALUES.aiConfidence,
    accuracy: adminUser.accuracy || DEFAULT_VALUES.accuracy,
    chatbotSettings: adminUser.chatbotSettings || DEFAULT_VALUES.chatbotSettings,
    smallTalk: adminUser.smallTalk || DEFAULT_VALUES.smallTalk,
    timeActive: adminUser.timeActive || DEFAULT_VALUES.timeActive,
    aiStartTime: adminUser.aiStartTime || DEFAULT_VALUES.aiStartTime,
    aiEndTime: adminUser.aiEndTime || DEFAULT_VALUES.aiEndTime,
    backoffMinutes: adminUser.backoffMinutes || DEFAULT_VALUES.backoffMinutes,
    timezone: adminUser.timezone || DEFAULT_VALUES.timezone,
    personalize: {
      greeting: adminUser?.personalize?.greeting ?? DEFAULT_VALUES.personalize.greeting,
      greetingText: adminUser?.personalize?.greetingText ?? DEFAULT_VALUES.personalize.greetingText,
      signature: adminUser?.personalize?.signature ?? DEFAULT_VALUES.personalize.signature,
      signatureText:
        adminUser?.personalize?.signatureText ?? DEFAULT_VALUES.personalize.signatureText,
    },
    companyInfo: {
      name: adminUser.companyName || '',
      bookingSiteUrl: adminUser.bookingSiteUrl || '',
      aboutUs: adminUser.aboutUs || '',
      logo: adminUser.logo || '',
    },
    contactInfo: {
      email: adminUser.guestContactEmail || '',
      phone: adminUser.guestContactPhone || '',
      whatsapp: adminUser.guestContactWhatsapp || '',
    },
    primaryColor: adminUser.primaryColor || DEFAULT_VALUES.theme.primaryColor,
    secondaryColor: adminUser.secondaryColor || DEFAULT_VALUES.theme.secondaryColor,
    companyName: adminUser.companyName || '',
    bookingSiteUrl: adminUser.bookingSiteUrl || '',
    aboutUs: adminUser.aboutUs || '',
    guestContactEmail: adminUser.guestContactEmail || '',
    guestContactPhone: adminUser.guestContactPhone || '',
    guestContactWhatsapp: adminUser.guestContactWhatsapp || '',
    logo: adminUser.logo || '',
    users: [],
    approveAiMessage: adminUser.approveAiMessage || false,
    allTimes: adminUser.allTimes || DEFAULT_VALUES.allTimes,
    offer: adminUser.offer || '',
  }

  const agents = await getAgents(adminUser.id)
  workspace.users = agents.map((agent: any) => ({
    userId: agent.id,
    email: agent.email,
  }))

  workspace.users.push({
    userId: adminUser.id,
    email: adminUser.email,
  })

  workspace.billing = await loadSubscription(adminUser)

  return workspace
})
