import axios from 'axios'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user: any = await getAdminUser(event.context.auth.uid)
  const url = new URL(
    `https://api.us-east.tinybird.co/v0/pipes/yar_gdp_listing_leaderboard.json?userId=${user.id}`,
  )

  try {
    const response = await axios.get(url.toString(), {
      headers: {
        Authorization: 'Bearer p.eyJ1IjogImZhN2UzZTFmLThjZjktNDhhMS04NjIzLWEzNWIyYjRlYTA3OCIsICJpZCI6ICJhMGQ5ODU2My1lNDgwLTQ2YTktYTExYi02MTk3OGE3OWFkMjQiLCAiaG9zdCI6ICJ1c19lYXN0In0.W37vM9-IZE5N8DYISm-vSjVWlNZujQBG6789Z8uVPg8',
      },
    })

    const result = response.data

    if (!result.data) {
      console.error(`there is a problem running the query: ${JSON.stringify(result)}`)
      return { error: 'Failed to fetch GDP counts' }
    }
    return result.data
  }
  catch (error) {
    console.error('Error fetching GDP counts:', error)
    throw createError({ statusCode: 500, statusMessage: 'Failed to fetch GDP counts' })
  }
})
