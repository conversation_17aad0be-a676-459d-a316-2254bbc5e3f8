import axios from 'axios'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user: any = await getAdminUser(event.context.auth.uid)
  const url = new URL(
    `https://api.us-east.tinybird.co/v0/pipes/yar_gdp_views.json?userId=${user.id}`,
  )
  try {
    const response = await axios.get(url.toString(), {
      headers: {
        Authorization:
          'Bearer p.eyJ1IjogImZhN2UzZTFmLThjZjktNDhhMS04NjIzLWEzNWIyYjRlYTA3OCIsICJpZCI6ICI5Mjc3ZWNjMi0yMmQ2LTRkYTktOGYxNC1kNjFhMmVmNWU0NDIiLCAiaG9zdCI6ICJ1c19lYXN0In0.D_VS_ZDXIjblFlgcecmODN64pgz7bNzr0RoycrALGjo',
      },
    })

    const result = response.data

    if (!result.data) {
      console.error(`there is a problem running the query: ${JSON.stringify(result)}`)
      return { error: 'Failed to fetch GDP counts' }
    }
    return result
  }
  catch (error) {
    throw new Error(`Failed to fetch GDP views: ${error instanceof Error ? error.message : String(error)}`)
  }
})
