import { getAdminUser, getContactBySource } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user: any = await getAdminUser(event.context.auth.uid)

  const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000
  const sixtyDaysAgo = Date.now() - 60 * 24 * 60 * 60 * 1000

  const [
    recentGuidebookContacts,
    recentPopupContacts,
    recentChatContacts,
    previousGuidebookContacts,
    previousPopupContacts,
    previousChatContacts,
  ] = await Promise.all([
    // Last 30 days
    getContactBySource(user.id, 'guidebook', thirtyDaysAgo, Date.now()),
    getContactBySource(user.id, 'popup', thirtyDaysAgo, Date.now()),
    getContactBySource(user.id, 'chat', thirtyDaysAgo, Date.now()),
    // 30-60 days ago
    getContactBySource(user.id, 'guidebook', sixtyDaysAgo, thirtyDaysAgo),
    getContactBySource(user.id, 'popup', sixtyDaysAgo, thirtyDaysAgo),
    getContactBySource(user.id, 'chat', sixtyDaysAgo, thirtyDaysAgo),
  ])

  return {
    data: {
      recent: {
        guidebookContacts: recentGuidebookContacts,
        popupContacts: recentPopupContacts,
        chatContacts: recentChatContacts,
      },
      previous: {
        guidebookContacts: previousGuidebookContacts,
        popupContacts: previousPopupContacts,
        chatContacts: previousChatContacts,
      },
    },
  }
})
