import axios from 'axios'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)

  const url = new URL(
    `https://api.us-east.tinybird.co/v0/pipes/yar_gdp_smartspaces.json?userId=${user.id}`,
  )

  try {
    const response = await axios.get(url.toString(), {
      headers: {
        Authorization:
          'Bearer p.eyJ1IjogImZhN2UzZTFmLThjZjktNDhhMS04NjIzLWEzNWIyYjRlYTA3OCIsICJpZCI6ICI4MmExY2E1OC00MDc2LTRhYzUtYWFlYS02M2JkNjBmZDQyZGUiLCAiaG9zdCI6ICJ1c19lYXN0In0.rHiIyS6iAHPUi9niPLamntAmwZZdFrDbAUY-jqRodzk',
      },
    })

    const result = response.data

    if (!result.data) {
      console.error(`there is a problem running the query: ${JSON.stringify(result)}`)
      return { error: 'Failed to fetch GDP counts' }
    }
    return result
  }
  catch (error) {
    console.error('Error fetching GDP counts:', error)
    throw new Error(`Failed to fetch GDP smart spaces: ${error instanceof Error ? error.message : String(error)}`)
  }
})
