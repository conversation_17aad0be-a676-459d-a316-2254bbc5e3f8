import axios from 'axios'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user: any = await getAdminUser(event.context.auth.uid)

  const url = new URL(
    `https://api.us-east.tinybird.co/v0/pipes/yar_gdp_counts.json?userId=${user.id}`,
  )

  try {
    const response = await axios.get(url.toString(), {
      headers: {
        Authorization:
          'Bearer p.eyJ1IjogImZhN2UzZTFmLThjZjktNDhhMS04NjIzLWEzNWIyYjRlYTA3OCIsICJpZCI6ICIxNDNhNWJjYi04MWI2LTQxMWUtODlkMC1mZmM2YjUyNDkyYjQiLCAiaG9zdCI6ICJ1c19lYXN0In0.ksbn_AjDzR001kgtcx7o7vY-YDPvtTCe8F7nVpsHzPc',
      },
    })

    const result = response.data

    if (!result.data) {
      console.error(`there is a problem running the query: ${JSON.stringify(result)}`)
      return { error: 'Failed to fetch GDP counts' }
    }
    return result
  }
  catch (error) {
    console.error('Error fetching GDP counts:', error)
    return { error: error instanceof Error ? error.message : String(error) }
  }
})
