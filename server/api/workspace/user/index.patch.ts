import {firestore} from '~/helpers/firebase'

export default defineEventHandler(async (event) => {
  console.log('updating user')
  const body = await readBody(event)
  console.log(body)
  const userId = event.context.auth.uid
  try {
    await firestore.doc(`users/${userId}`).update(body)
  }
  catch (e) {
    console.error('Error updating user:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to update user' })
  }
  return 'success'
})
