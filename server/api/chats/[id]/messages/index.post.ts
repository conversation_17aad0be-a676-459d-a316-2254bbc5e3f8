import { firestore } from '~/helpers/firebase'

export default defineEventHandler(async (event) => {
  const { id } = getRouterParams(event)
  const body = await readBody(event)
  try {
    await firestore.collection(`chats/${id}/messages`).add({
      type: body.type,
      body: body.body,
      createdAt: Date.now(),
      conversationId: id,
      source: 'app',
      template: 'message'
    })
    await firestore.doc(`chats/${id}`).update({
      aiRespond: false,
      flow: 'chat'
    })
  } catch (error: any) {
    console.error('Error sending chat message:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to send chat message',
      message: error.message || 'An error occurred while sending the chat message.'
    })
  }
  return 'success'
})
