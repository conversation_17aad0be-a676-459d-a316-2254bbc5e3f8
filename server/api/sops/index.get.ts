import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)

  const allowedParams = ['listingId']
  const queryKeys = Object.keys(query)

  // Check for any disallowed query params
  const invalidParams = queryKeys.filter(key => !allowedParams.includes(key))
  if (invalidParams.length > 0) {
    throw createError({
      statusCode: 400,
      statusMessage: `Invalid query parameter(s): ${invalidParams.join(', ')}`,
    })
  }

  const { listingId } = query

  const user = await getAdminUser(event.context.auth.uid)
  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    })
  }

  try {
    let sopsRef = firestore.collection('sops');
    // Add query filters based on query params
    if (listingId) {
      sopsRef = sopsRef.where('selectedListings', 'array-contains', listingId);
    } else {
      // Fetch all sops for this user if no listingId is provided
      sopsRef = sopsRef.where('userId', '==', user.id);
    }
    const { docs } = await sopsRef.get();
    return docs.map(doc => ({ ...doc.data(), id: doc.id }));
  } catch (e) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Unable to find sops',
    });
  }
})
