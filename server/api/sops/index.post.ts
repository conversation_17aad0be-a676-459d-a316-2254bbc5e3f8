import { getAdminUser } from '~/services/db'
import {firestore} from '~/helpers/firebase'
import {upsert} from '~/services/ai/indexing'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const user = await getAdminUser(event.context.auth.uid)

  const sop = {...body, userId: user.id}

  try {
    const res = await firestore.collection('sops').add(sop)
    await upsert('sops', res.id, {...sop}, `${sop.title} ${sop.description} ${sop.text}`)
  } catch (e) {
    console.error('Error adding SOP:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to add SOP' })
  }
  return 'SOP added successfully'
})
