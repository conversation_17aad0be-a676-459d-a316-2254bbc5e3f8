import { firestore } from '~/helpers/firebase'
import { upsert } from '~/services/ai/indexing'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const user = await getAdminUser(event.context.auth.uid)
  const { id } = event.context.params
    try {
      await firestore.doc(`sops/${id}`).update(body)
      await upsert('sops', id, {...body, userId: user.id}, `${body.title} ${body.description} ${body.text}`)
    }
    catch (e) {
      console.error('Error updating SOP:', e)
      throw createError({ statusCode: 500, statusMessage: 'Unable to update SOP' })
    }
  return 'Hello Nitro'
})
