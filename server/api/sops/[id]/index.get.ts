import { firestore } from '~/helpers/firebase'

export default defineEventHandler(async (event) => {
  const { id } = event.context.params
  try {
    const doc = await firestore.doc(`sops/${id}`).get()
    if (!doc.exists) {
      throw createError({ statusCode: 404, statusMessage: 'SOP not found' })
    }
    return {...doc.data(), id: doc.id}
  } catch (e) {
    console.error('Error fetching SOP:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to fetch SOP' })
  }
})
