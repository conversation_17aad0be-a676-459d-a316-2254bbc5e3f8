import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'
import { sendTransactional } from '~/services/loops'

export default defineEventHandler(async (event) => {
  const { id } = event.context.params
  console.log('sharing form', id)
  const body = await readBody(event)

  const user = await getAdminUser(event.context.auth.uid)

  let listing
  try {
    const listingSnap = await firestore.doc(`listings/${body.listingId}`).get()
    if (!listingSnap.exists) {
      throw createError({ statusCode: 404, statusMessage: 'Unable to find listing' })
    }
    listing = { ...listingSnap.data(), id: listingSnap.id }
  }
  catch (e) {
    throw createError({ statusCode: 404, statusMessage: 'Unable to find listing' })
  }

  await sendTransactional(process.env.LOOPS_TXN_FORM_SHARE, body.email, { listingName: listing.name, senderEmail: user.email, recipientEmail: body.email, formUrl: `https://forms.yada.ai/${id}` })

  return 'Hello Nitro'
})
