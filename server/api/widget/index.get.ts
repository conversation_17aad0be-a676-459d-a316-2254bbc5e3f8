import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)

  try {
    const doc = await firestore.doc(`widgets/${user.id}`).get()
    return { ...doc.data(), id: doc.id }
  }
  catch (e) {
    console.error('Error fetching widget:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to fetch widget' })
  }
})
