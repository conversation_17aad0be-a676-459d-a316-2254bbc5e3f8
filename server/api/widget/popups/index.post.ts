import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)
  const body = await readBody(event)

  const popup = { ...body, userId: user.id }

  try {
    const res = await firestore.collection('popups').add(popup)
    return res.id
  }
  catch (e) {
    console.error('Error adding popup:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to add popup' })
  }
})
