import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)
  try {
    const popups = await firestore.collection('popups').where('userId', '==', user.id).get()
    return popups.docs.map((doc) => ({ ...doc.data(), id: doc.id }))
  }
  catch (e) {
    console.error('Error fetching popups:', e)
    throw createError({ statusCode: 500, statusMessage: 'Unable to fetch popups' })
  }
})
