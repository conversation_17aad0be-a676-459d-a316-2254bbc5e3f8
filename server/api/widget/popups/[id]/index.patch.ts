import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)
  const { id } = event.context.params
  const body = await readBody(event)

  // Reference to popups collection
  const popupsRef = firestore.collection('popups')

  if (body.active === true) {
    const snapshot = await popupsRef.where('userId', '==', user.id).get()
    const batch = firestore.batch()
    snapshot.docs.forEach((doc) => {
      if (doc.id !== id) {
        batch.update(doc.ref, { active: false })
      }
    })
    batch.update(popupsRef.doc(id), body)
    await batch.commit()
    return 'success'
  }
  else {
    // Just update the popup as usual
    await popupsRef.doc(id).update(body)
    return 'success'
  }
})
