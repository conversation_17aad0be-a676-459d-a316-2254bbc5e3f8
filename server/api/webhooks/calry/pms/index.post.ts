import { handleReservationUpdated, handleReservationCreated, handleConversationUpdated } from '~/services/calry';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  // Respond immediately with 200 OK
  setResponseStatus(event, 200)

  // setImmediate(async () => {
  try {
    switch (body.hook?.eventType) {
      case 'conversation.updated':
        await handleConversationUpdated(body);
        break;
      case 'reservation.created':
        await handleReservationCreated(body);
        break;
      case 'reservation.updated':
        await handleReservationUpdated(body);
        break;
      default:
        console.log('Unknown hook type:', body.hook?.type);
    }
  } catch (error) {
    console.error('Error in background processing:', error);
  }
  // });

  return { 'success': true }
});
