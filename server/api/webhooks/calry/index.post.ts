import { firestore } from '~/helpers/firebase'
import Calry from '~/services/calry'
import { calculateRandomDelay, scheduleQstash } from '~/services/upstash'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)

  setResponseStatus(event, 200)

  if (!body || !body.hook || !body.hook.eventType) {
    console.error('[Calry Webhook] Invalid webhook payload structure:', body)
    return { success: true }
  }

  try {
    const { eventType } = body.hook
    const { accountIdentifier: yadaUserId, integrationAccountId } = body.data
    console.log(`[Calry Webhook] Event type: ${eventType}`)

    switch (eventType) {
      case 'account.created': {
        console.log('[Calry Webhook] Handling account.created event')
        const yadaUserId = body.data.accountIdentifier
        const calry = new Calry(body.data.integrationAccountId)
        console.log('[Calry Webhook] Fetching Calry account info...')
        const calryAccount = await calry.getAccount()
        console.log('[<PERSON>ry Webhook] Calry account:', JSON.stringify(calryAccount))
        const fieldPath = `integrations.calry.${body.data.integrationAccountId}`
        console.log(`[Calry Webhook] Updating Firestore at users/${yadaUserId}, field: ${fieldPath}`)
        await firestore.doc(`users/${yadaUserId}`).update(fieldPath, calryAccount)
        console.log('[Calry Webhook] Setting up Calry webhooks...')
        await calry.setupWebhooks()
        console.log('[Calry Webhook] Webhook setup successfully')
        const updatedCalryAccount = await calry.getAccount()
        console.log('[Calry Webhook] Updated Calry account:', JSON.stringify(updatedCalryAccount))
        await firestore.doc(`users/${yadaUserId}`).update(fieldPath, updatedCalryAccount)
        console.log('scheduling calry property import')
        await scheduleQstash('https://nuxt-yada-ai.vercel.app/api/qstash/init-calry-property-import', { integrationAccountId, yadaUserId }, 300)
        break
      }
      default: {
        console.log(`[Calry Webhook] Unhandled event type: ${body.hook.eventType}`)
        break
      }
    }
    console.log('[Calry Webhook] Handler completed')
  }
  catch (error) {
    console.error('[Calry Webhook] Error occurred:', error)
  }

  return { success: true }



  // Continue processing in the background
  // setImmediate(async () => {
  //   try {
  //     const { eventType } = body.hook
  //     const { accountIdentifier: yadaUserId, integrationAccountId } = body.data
  //     console.log(`[Calry Webhook] Event type: ${eventType}`)
  //     console.log(`[Calry Webhook] Yada User ID: ${yadaUserId}, Integration Account ID: ${integrationAccountId}`)

  //     switch (eventType) {
  //       case 'account.created': {
  //         console.log('[Calry Webhook] Handling account.created event')
  //         const yadaUserId = body.data.accountIdentifier
  //         const calry = new Calry(body.data.integrationAccountId)
  //         console.log('[Calry Webhook] Fetching Calry account info...')
  //         const calryAccount = await calry.getAccount()
  //         console.log('[Calry Webhook] Calry account:', JSON.stringify(calryAccount))
  //         const fieldPath = `integrations.calry.${body.data.integrationAccountId}`
  //         console.log(`[Calry Webhook] Updating Firestore at users/${yadaUserId}, field: ${fieldPath}`)
  //         await firestore.doc(`users/${yadaUserId}`).update(fieldPath, calryAccount)
  //         console.log('[Calry Webhook] Setting up Calry webhooks...')
  //         await calry.setupWebhooks()
  //         console.log('[Calry Webhook] Webhook setup successfully')
  //         const updatedCalryAccount = await calry.getAccount()
  //         console.log('[Calry Webhook] Updated Calry account:', JSON.stringify(updatedCalryAccount))
  //         await firestore.doc(`users/${yadaUserId}`).update(fieldPath, updatedCalryAccount)
  //         console.log('scheduling calry property import')
  //         await scheduleQstash('https://nuxt-yada-ai.vercel.app/api/qstash/init-calry-property-import', { integrationAccountId, yadaUserId }, 300)
  //         break
  //       }
  //       default: {
  //         console.log(`[Calry Webhook] Unhandled event type: ${body.hook.eventType}`)
  //         break
  //       }
  //     }
  //     console.log('[Calry Webhook] Handler completed')
  //   }
  //   catch (error) {
  //     console.error('[Calry Webhook] Error occurred:', error)
  //   }
  // })
})
