import { put } from '@vercel/blob'
import { defineEventHandler, readMultipartFormData } from 'h3'

export default defineEventHandler(async (event) => {
  const formData = await readMultipartFormData(event)

  if (!formData) {
    throw new Error('No form data')
  }

  const file = formData.find(f => f.name === 'file')

  if (!file || !file.data) {
    throw new Error('No file uploaded')
  }

  // You must specify contentType
  const blob = await put(file.filename, file.data, {
    access: 'public',
    contentType: file.type || 'application/octet-stream', // This is critical
  })

  return blob
})
