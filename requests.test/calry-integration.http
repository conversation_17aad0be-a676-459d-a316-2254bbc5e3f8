# POST request to yada webhook when calry account is created
POST http://localhost:3000/api/webhooks/calry
Content-Type: application/json

{
  "data": {
    "accountIdentifier": "I3xuFecITShxX4U0ilhihkDvswe2",
    "createdAt": "2025-05-01T16:10:38.670Z",
    "integrationAccountId": "7a0f8c3a-ef19-476e-831b-8505312d845b",
    "integrationAccountName": "OwnerRez Authentication Link_5v1cj",
    "integrationDefinition": {
      "integrationDefinitionId": "a3181d1b-d40d-4af8-b8d0-************",
      "key": "ownerrez",
      "name": "OwnerRez"
    },
    "linkId": "97ed556a-d322-47be-b61f-0c07e1dfda66",
    "pmsAccountId": "*********",
    "pmsAccountIdentifier": "40ea1095ed65606256eb935225aaafc7860e7d4c8791b294c5f4a53518cb6ac8",
    "result": "SUCCESS",
    "workspaceId": "ab5eba02-bc46-4bf2-9f8b-35513ab17a18"
  },
  "hook": {
    "eventType": "account.created",
    "id": "5e3deb3e-c7c7-45c6-abda-65fee285d644",
    "source": "CALRY_WEBHOOK",
    "timestamp": "2025-05-01T16:10:38.789Z"
  }
}

###


### POST request to import Calry property as listing
POST http://localhost:3000/api/qstash/import-calry-property-as-listing
Content-Type: application/json

{
  "property": {
    "id": "452779",
    "name": "Yadvilla Magnifica",
    "description": "",
    "type": "",
    "status": "ACTIVE",
    "thumbnailUrl": "",
    "address": {
      "city": "Cambridge",
      "line1": "240 Brattle Street",
      "state": "",
      "country": "UNITED STATES",
      "postal_code": "02138"
    },
    "geoLocation": {
      "latitude": 42.3756505,
      "longitude": -71.1425075
    },
    "roomTypes": [
      {
        "id": "452779",
        "name": "Yadvilla Magnifica"
      }
    ]
  },
  "integrationAccountId": "7a0f8c3a-ef19-476e-831b-8505312d845b",
  "userId": "I3xuFecITShxX4U0ilhihkDvswe2",
  "calryAccount": {
    "integrationAccountId": "7a0f8c3a-ef19-476e-831b-8505312d845b",
    "integrationDefinitionId": "a3181d1b-d40d-4af8-b8d0-************",
    "workspaceId": "ab5eba02-bc46-4bf2-9f8b-35513ab17a18",
    "integrationAccountName": "OwnerRez Authentication Link_5v1cj",
    "deleted": null,
    "createdAt": "2025-05-01T16:10:38.670Z",
    "updatedAt": "2025-05-01T16:10:38.670Z",
    "accountIdentifier": "I3xuFecITShxX4U0ilhihkDvswe2",
    "pmsAccountIdentifier": "40ea1095ed65606256eb935225aaafc7860e7d4c8791b294c5f4a53518cb6ac8",
    "pmsAccountId": "*********",
    "firstSync": true,
    "syncEnabled": true,
    "syncPeriod": "EVERY_HOUR",
    "linkId": "97ed556a-d322-47be-b61f-0c07e1dfda66",
    "integrationDefinition": {
      "integrationDefinitionId": "a3181d1b-d40d-4af8-b8d0-************",
      "name": "OwnerRez",
      "key": "ownerrez",
      "icon": "ownerrez.svg",
      "partnerKey": {},
      "integrationType": "VRS"
    },
    "workspace": {
      "workspaceId": "ab5eba02-bc46-4bf2-9f8b-35513ab17a18",
      "slug": "xenogeneic-turquoise",
      "name": "xenogeneic-turquoise",
      "userId": "063f48d3-4c72-41b3-bf3f-f8fc05baddac",
      "initialSetupComplete": false,
      "anonymousDataCollection": false,
      "syncEnabled": true,
      "deleted": null
    }
  }
}

###