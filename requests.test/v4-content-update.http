### Test updating an amenity in a v4 listing
PATCH http://localhost:3000/api/listings/YOUR_LISTING_ID/content-cards/amenity_wifi
Content-Type: application/json

{
  "text": "The WiFi password is 'guest1234' and the network name is 'GuestNetwork'"
}

### Test updating multiple fields in an amenity
PATCH http://localhost:3000/api/listings/YOUR_LISTING_ID/content-cards/amenity_wifi
Content-Type: application/json

{
  "text": "The WiFi password is 'guest1234' and the network name is 'GuestNetwork'",
  "amenityAvailable": true,
  "amenityKnown": true
}

### Test updating a FAQ in a v4 listing
PATCH http://localhost:3000/api/listings/YOUR_LISTING_ID/content-cards/faq_checkIn
Content-Type: application/json

{
  "text": "Check-in is at 3 PM and check-out is at 11 AM. Early check-in may be available upon request."
}

### Test updating multiple fields in a FAQ
PATCH http://localhost:3000/api/listings/YOUR_LISTING_ID/content-cards/faq_checkIn
Content-Type: application/json

{
  "text": "Check-in is at 3 PM and check-out is at 11 AM. Early check-in may be available upon request.",
  "showInGuidebook": true,
  "requireConfirmedReservation": false
}
