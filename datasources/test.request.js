import axios from 'axios'

const jsonObject = {
  name: '<PERSON><PERSON>',
  age: 30,
  isDeveloper: true,
}

const data = {
  timestamp: '2025-06-08T14:32:45Z',
  userId: 'I3xuFecITShxX4U0ilhihkDvswe2',
  event: 'guidebook_view',
  listingId: '123123123',
  contactId: 'asdfasdfasdf',
  anonymousId: 'anon123123',
  sessionId: 'asdfasdf888asdf',
  ip: '***********',
  src: 'local.com',
  subSrc: 'asdfasdf',
  cardId: 'squiglywoo',
  jsonData: JSON.stringify(jsonObject),
}

axios.post(
  'https://api.us-east.aws.tinybird.co/v0/events?name=gdp',
  data,
  {
    headers: {
      'Authorization': 'Bearer p.eyJ1IjogIjU1Njg0MjNmLTQwN2EtNDRjOS1hOWEyLTgwMDkwZDhmZTRjMSIsICJpZCI6ICJlYmY0ODQ3Mi00MzNhLTQ5NjMtODFjYS0zNzY0NGVmMzIyZmYiLCAiaG9zdCI6ICJ1cy1lYXN0LWF3cyJ9.OC1kBx7s4_sGpM0xTo0wYwHV4km6s1TYDgDzV47KLcw',
      'Content-Type': 'application/json',
    },
  },
)
  .then((response) => {
    console.log('Status:', response.status)
    console.log('Response data:', response.data)
  })
  .catch((error) => {
    if (error.response) {
      // Server responded with a non-2xx code
      console.error('Error status:', error.response.status)
      console.error('Error data:', error.response.data)
    }
    else if (error.request) {
      // Request made but no response received
      console.error('No response received:', error.request)
    }
    else {
      // Something else went wrong
      console.error('Error:', error.message)
    }
  })
